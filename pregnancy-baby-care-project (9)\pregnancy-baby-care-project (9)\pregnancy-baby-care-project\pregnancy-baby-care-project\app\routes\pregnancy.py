from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from datetime import datetime, date, timedelta
from app.data_manager import DataManager
import uuid
import json

pregnancy_bp = Blueprint('pregnancy', __name__, url_prefix='/pregnancy')

def login_required(f):
    """Simple login required decorator"""
    def decorated_function(*args, **kwargs):
        if not session.get('user_id'):
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Page Routes

@pregnancy_bp.route('/')
@login_required
def index():
    """Pregnancy care main page"""
    return render_template('pregnancy/pregnancy.html')

@pregnancy_bp.route('/nutrition')
@login_required
def nutrition():
    """Pregnancy nutrition page"""
    return render_template('pregnancy/nutrition.html')

@pregnancy_bp.route('/exercise')
@login_required
def exercise():
    """Pregnancy exercise page"""
    return render_template('pregnancy/exercise.html')

@pregnancy_bp.route('/meditation')
@login_required
def meditation():
    """Pregnancy meditation page"""
    return render_template('pregnancy/meditation.html')

@pregnancy_bp.route('/weight-tracker')
@login_required
def weight_tracker():
    """Pregnancy weight tracker page"""
    return render_template('pregnancy/weight_tracker.html')

@pregnancy_bp.route('/appointments')
@login_required
def appointments():
    """Pregnancy appointments page"""
    return render_template('pregnancy/appointments.html')

@pregnancy_bp.route('/faq')
@login_required
def faq():
    """Pregnancy FAQ page"""
    return render_template('pregnancy/faq.html')

@pregnancy_bp.route('/schemes')
@login_required
def schemes():
    """Pregnancy schemes page"""
    return render_template('pregnancy/schemes.html')

@pregnancy_bp.route('/reports')
@login_required
def reports():
    """Pregnancy reports page"""
    return render_template('pregnancy/reports.html')

@pregnancy_bp.route('/assistant')
@login_required
def assistant():
    """Pregnancy assistant page"""
    return render_template('pregnancy/assistant.html')

# API Routes

# Removed old nutrition API - now using admin-managed content from /api/nutrition-data