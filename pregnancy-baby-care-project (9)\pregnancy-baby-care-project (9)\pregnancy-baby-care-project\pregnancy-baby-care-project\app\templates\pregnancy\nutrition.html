
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Nutrition Plans - Maternal Health and Child Health</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: "Arial", sans-serif;
    }

    body {
      color: #333;
      background-color: #fff5f7;
      padding-top: 80px; /* Account for fixed header */
    }

    /* Header Styles */
    .header {
        background: white;
        box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        padding: 0.5rem 0;
    }

    .nav-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 70px;
    }

    .logo {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 1.5rem;
        font-weight: 700;
        color: #e91e63;
        text-decoration: none;
    }

    .logo-icon {
        background: linear-gradient(135deg, #e91e63, #ff6b9d);
        width: 45px;
        height: 45px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.8rem;
    }

    .nav-menu {
        display: flex;
        list-style: none;
        gap: 1.5rem;
        align-items: center;
    }

    .nav-link {
        text-decoration: none;
        color: #333;
        font-weight: 500;
        transition: all 0.3s ease;
        position: relative;
        padding: 0.8rem 1.2rem;
        border-radius: 25px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.95rem;
    }

    .nav-link:hover,
    .nav-link.active {
        color: white;
        background: linear-gradient(135deg, #e91e63, #ff6b9d);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
    }

    .logout-btn:hover {
        background: linear-gradient(135deg, #f44336, #ff5722) !important;
        box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3) !important;
    }

    .mobile-menu-btn {
        display: none;
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #333;
        cursor: pointer;
    }

    /* Responsive Styles */
    @media (max-width: 768px) {
        .nav-menu {
            display: none;
        }

        .mobile-menu-btn {
            display: block;
        }

        .nav-container {
            padding: 0 1rem;
        }
    }
      color: #333;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s;
      position: relative;
    }

    .nav-links a:hover {
      color: #d63384;
    }

    .nav-links a::after {
      content: "";
      position: absolute;
      width: 0;
      height: 2px;
      bottom: -5px;
      left: 0;
      background-color: #d63384;
      transition: width 0.3s;
    }

    .nav-links a:hover::after {
      width: 100%;
    }

    .book-now {
      background-color: #d63384;
      border: none;
      padding: 0.7rem 1.5rem;
      border-radius: 25px;
      cursor: pointer;
      font-weight: bold;
      color: white;
      transition: all 0.3s;
      box-shadow: 0 4px 8px rgba(214, 51, 132, 0.3);
    }

    .book-now:hover {
      background-color: #b52a6f;
      transform: translateY(-2px);
    }

    /* Page header */
    .page-header {
      background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
        url("https://images.unsplash.com/photo-1490818387583-1baba5e638af?auto=format&fit=crop&w=1470&q=80")
          no-repeat center center/cover;
      height: 50vh;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      color: white;
      margin-top: 70px;
    }

    .page-header h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .page-header p {
      font-size: 1.2rem;
      max-width: 800px;
      margin: 0 auto;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }

    /* Content section */
    .content-section {
      padding: 5rem 2rem;
      background-color: white;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .section-title {
      text-align: center;
      margin-bottom: 2rem;
      color: #d63384;
      font-size: 2.5rem;
    }

    /* Nutrition plan styles */
    .nutrition-tabs {
      display: flex;
      justify-content: center;
      margin-bottom: 2rem;
      flex-wrap: wrap;
    }

    .tab-btn {
      padding: 1rem 2rem;
      background-color: #f8f9fa;
      border: none;
      margin: 0 0.5rem 1rem;
      border-radius: 30px;
      cursor: pointer;
      font-weight: bold;
      color: #333;
      transition: all 0.3s;
    }

    .tab-btn.active {
      background-color: #d63384;
      color: white;
      box-shadow: 0 4px 8px rgba(214, 51, 132, 0.3);
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
      animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    .meal-plan {
      margin-bottom: 3rem;
    }

    .meal-plan h3 {
      color: #d63384;
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }

    .meal-card {
      background-color: #fff9fb;
      border-radius: 10px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .meal-card h4 {
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .meal-card h4 i {
      margin-right: 10px;
      color: #d63384;
    }

    .meal-card ul {
      list-style-type: none;
      margin-left: 2rem;
    }

    .meal-card li {
      margin-bottom: 0.5rem;
      position: relative;
    }

    .meal-card li:before {
      content: "•";
      color: #d63384;
      position: absolute;
      left: -1rem;
    }

    /* Admin Panel Styles */
    .admin-panel {
      background: rgba(214, 51, 132, 0.1);
      border: 2px solid #d63384;
      border-radius: 15px;
      padding: 1.5rem;
      margin: 2rem 0;
      box-shadow: 0 8px 32px rgba(214, 51, 132, 0.1);
    }

    .admin-header {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
      font-size: 1.2rem;
      font-weight: bold;
      color: #d63384;
    }

    .admin-header i {
      margin-right: 10px;
      color: #ffd700;
    }

    .admin-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .btn-admin {
      background: #d63384;
      color: white;
      border: none;
      padding: 0.8rem 1.5rem;
      border-radius: 25px;
      font-weight: 500;
      transition: all 0.3s ease;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-admin:hover {
      background: #b52a6f;
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(214, 51, 132, 0.3);
    }

    .btn-admin i {
      font-size: 0.9rem;
    }

    /* Footer styles */
    .footer {
      background-color: #2c3e50;
      color: white;
      padding: 3rem 2rem;
    }

    .footer-container {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }

    .footer-column {
      flex: 1 1 300px;
      margin-bottom: 2rem;
    }

    .footer-logo {
      font-size: 1.8rem;
      font-weight: bold;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .footer-logo i {
      margin-right: 10px;
    }

    .footer-links {
      list-style: none;
    }

    .footer-links li {
      margin-bottom: 0.5rem;
    }

    .footer-links a {
      color: #ecf0f1;
      text-decoration: none;
      transition: color 0.3s;
    }

    .footer-links a:hover {
      color: #d63384;
    }

    .social-icons {
      display: flex;
      margin-top: 1rem;
    }

    .social-icons a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #34495e;
      color: white;
      margin-right: 1rem;
      transition: all 0.3s;
    }

    .social-icons a:hover {
      background-color: #d63384;
      transform: translateY(-3px);
    }

    .footer-bottom {
      width: 100%;
      text-align: center;
      padding-top: 2rem;
      margin-top: 2rem;
      border-top: 1px solid #34495e;
    }

    /* Mobile menu */
    .mobile-menu-btn {
      display: none;
      background: none;
      border: none;
      font-size: 1.5rem;
      color: #333;
      cursor: pointer;
    }

    @media (max-width: 768px) {
      .mobile-menu-btn {
        display: block;
      }

      .nav-links {
        position: fixed;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: white;
        flex-direction: column;
        align-items: center;
        padding: 2rem 0;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-150%);
        transition: transform 0.3s;
      }

      .nav-links.active {
        transform: translateY(0);
      }

      .nav-links a {
        margin: 1rem 0;
      }
    }

    /* Empty State Styles */
    .empty-state {
      text-align: center;
      padding: 3rem 2rem;
      color: #666;
    }

    .empty-state h4 {
      margin-bottom: 0.5rem;
      color: #333;
    }

    .empty-state p {
      color: #999;
      font-style: italic;
    }

    /* Loading State */
    .loading {
      text-align: center;
      padding: 2rem;
      color: #666;
    }

    .loading i {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header class="header">
      <div class="nav-container">
          <a href="/" class="logo">
              <div class="logo-icon">
                  <i class="fas fa-utensils"></i>
              </div>
              <span>Pregnancy Nutrition</span>
          </a>
          <nav class="nav-menu">
              <a href="/" class="nav-link">
                  <i class="fas fa-home"></i> Home
              </a>
              <a href="/pregnancy" class="nav-link">
                  <i class="fas fa-heart"></i> Pregnancy Care
              </a>
              <a href="/pregnancy/nutrition" class="nav-link active">
                  <i class="fas fa-utensils"></i> Nutrition
              </a>
              <a href="/pregnancy/exercise" class="nav-link">
                  <i class="fas fa-dumbbell"></i> Exercise
              </a>
              <a href="/pregnancy/weight-tracker" class="nav-link">
                  <i class="fas fa-weight"></i> Weight Tracker
              </a>
              <a href="/pregnancy/appointments" class="nav-link">
                  <i class="fas fa-calendar-alt"></i> Appointments
              </a>
              <a href="/pregnancy/reports" class="nav-link">
                  <i class="fas fa-chart-line"></i> Reports
              </a>
              <a href="/babycare" class="nav-link">
                  <i class="fas fa-baby"></i> Baby Care
              </a>
              <a href="/auth/logout" class="nav-link logout-btn">
                  <i class="fas fa-sign-out-alt"></i> Logout
              </a>
          </nav>
          <button class="mobile-menu-btn">
              <i class="fas fa-bars"></i>
          </button>
      </div>
  </header>

  <section class="page-header">
    <div>
      <h1>Nutrition Plans</h1>
      <p>Healthy eating guidelines for a nourishing pregnancy journey</p>
    </div>
  </section>

  <section class="content-section">
    <div class="container">
      <!-- Admin Panel for Nutrition -->
      <div id="nutrition-admin-panel" class="admin-panel" style="display: none;">
        <div class="admin-header">
          <i class="fas fa-crown"></i>
          <span>Nutrition Admin Panel</span>
        </div>
        <div class="admin-actions">
          <button class="btn btn-admin" onclick="addNutritionItem()">
            <i class="fas fa-plus"></i> Add Meal Plan
          </button>
          <button class="btn btn-admin" onclick="manageNutritionContent()">
            <i class="fas fa-edit"></i> Manage Content
          </button>
          <button class="btn btn-admin" onclick="addNutritionTip()">
            <i class="fas fa-lightbulb"></i> Add Nutrition Tip
          </button>
        </div>
      </div>

      <h2 class="section-title">Trimester-Specific Nutrition</h2>
      
      <div class="nutrition-tabs">
        <button class="tab-btn active" data-tab="first">First Trimester</button>
        <button class="tab-btn" data-tab="second">Second Trimester</button>
        <button class="tab-btn" data-tab="third">Third Trimester</button>
      </div>

      <div id="first" class="tab-content active">
        <div class="meal-plan">
          <h3>First Trimester Meal Plan</h3>

          <div id="first-trimester-content" class="trimester-content">
            <!-- Content will be loaded dynamically from admin-added data -->
            <div class="empty-state">
              <i class="fas fa-utensils" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
              <h4>No meal plans available</h4>
              <p>Meal plans for the first trimester will be added by our nutrition experts.</p>
            </div>
          </div>
        </div>
      </div>

      <div id="second" class="tab-content">
        <div class="meal-plan">
          <h3>Second Trimester Meal Plan</h3>

          <div id="second-trimester-content" class="trimester-content">
            <!-- Content will be loaded dynamically from admin-added data -->
            <div class="empty-state">
              <i class="fas fa-utensils" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
              <h4>No meal plans available</h4>
              <p>Meal plans for the second trimester will be added by our nutrition experts.</p>
            </div>
          </div>
        </div>
      </div>

      <div id="third" class="tab-content">
        <div class="meal-plan">
          <h3>Third Trimester Meal Plan</h3>

          <div id="third-trimester-content" class="trimester-content">
            <!-- Content will be loaded dynamically from admin-added data -->
            <div class="empty-state">
              <i class="fas fa-utensils" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
              <h4>No meal plans available</h4>
              <p>Meal plans for the third trimester will be added by our nutrition experts.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="footer-container">
      <div class="footer-column">
        <div class="footer-logo">
          <i class="fas fa-baby-carriage"></i>
          Maternal Health & Child Health
        </div>
        <p>
          Your trusted companion for pregnancy and baby care, providing expert
          guidance and support every step of the way.
        </p>
        <div class="social-icons">
          <a href="#"><i class="fab fa-facebook-f"></i></a>
          <a href="#"><i class="fab fa-twitter"></i></a>
          <a href="#"><i class="fab fa-instagram"></i></a>
          <a href="#"><i class="fab fa-youtube"></i></a>
        </div>
      </div>

      <div class="footer-column">
        <h3>Quick Links</h3>
        <ul class="footer-links">
          <li><a href="/home.html">Home</a></li>
          <li><a href="/pages/Preg/pregcare.html">Pregnancy Care</a></li>
          <li><a href="/pages/baby/baby-care.html">Baby Care</a></li>
          <li><a href="/pages/doctor/dashboard.html">Consult Doctor</a></li>
        </ul>
      </div>

      <div class="footer-column">
        <h3>Services</h3>
        <ul class="footer-links">
          <li><a href="/pages/Preg/nutrition.html">Nutrition Plans</a></li>
          <li><a href="/pages/Preg/schemes.html">Government Schemes</a></li>
          <li><a href="/pages/contact.html">Contact Us</a></li>
          <li><a href="/login">Login</a></li>
        </ul>
      </div>

      <div class="footer-column">
        <h3>Contact Info</h3>
        <p><i class="fas fa-phone"></i> +****************</p>
        <p><i class="fas fa-envelope"></i> <EMAIL></p>
        <p><i class="fas fa-map-marker-alt"></i> 123 Health Street, Care City</p>
      </div>
    </div>

    <div class="footer-bottom">
      <p>&copy; 2024 Maternal Health and Child Health. All rights reserved.</p>
    </div>
  </footer>

  <script>
    // Tab functionality
    document.addEventListener('DOMContentLoaded', function() {
      const tabBtns = document.querySelectorAll('.tab-btn');
      const tabContents = document.querySelectorAll('.tab-content');

      tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const targetTab = this.getAttribute('data-tab');

          // Remove active class from all buttons and contents
          tabBtns.forEach(b => b.classList.remove('active'));
          tabContents.forEach(content => content.classList.remove('active'));

          // Add active class to clicked button and corresponding content
          this.classList.add('active');
          document.getElementById(targetTab).classList.add('active');
        });
      });

      // Mobile menu functionality
      const mobileMenuBtn = document.getElementById('mobileMenuBtn');
      const navLinks = document.getElementById('navLinks');

      if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.addEventListener('click', function() {
          navLinks.classList.toggle('active');
        });
      }

      // Get Started button functionality
      const getStartedBtn = document.querySelector('.book-now');
      if (getStartedBtn) {
        getStartedBtn.addEventListener('click', function() {
          window.location.href = '/signup';
        });
      }

      // Check if user is admin and show admin panel
      checkNutritionAdminStatus();
    });

    // Check admin status for nutrition page
    function checkNutritionAdminStatus() {
      const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
      if (userData.role === 'admin') {
        document.getElementById('nutrition-admin-panel').style.display = 'block';
      }
    }

    // Add Nutrition Item
    function addNutritionItem() {
      const modal = createModal('Add Meal Plan Item', `
        <form id="add-nutrition-form">
          <div style="margin: 10px 0;">
            <label>Trimester:</label>
            <select id="nutrition-trimester" required style="width: 100%; padding: 8px; margin: 5px 0;">
              <option value="first">First Trimester</option>
              <option value="second">Second Trimester</option>
              <option value="third">Third Trimester</option>
            </select>
          </div>
          <div style="margin: 10px 0;">
            <label>Meal Type:</label>
            <select id="nutrition-meal-type" required style="width: 100%; padding: 8px; margin: 5px 0;">
              <option value="breakfast">Breakfast</option>
              <option value="mid-morning">Mid-Morning Snack</option>
              <option value="lunch">Lunch</option>
              <option value="afternoon">Afternoon Snack</option>
              <option value="dinner">Dinner</option>
            </select>
          </div>
          <div style="margin: 10px 0;">
            <label>Food Item:</label>
            <input type="text" id="nutrition-food" required style="width: 100%; padding: 8px; margin: 5px 0;" placeholder="e.g., Greek Yogurt with Berries">
          </div>
          <div style="margin: 10px 0;">
            <label>Benefits:</label>
            <textarea id="nutrition-benefits" required style="width: 100%; padding: 8px; margin: 5px 0; height: 60px;" placeholder="Nutritional benefits..."></textarea>
          </div>
          <div style="margin: 10px 0;">
            <label>Icon (Font Awesome class):</label>
            <input type="text" id="nutrition-icon" placeholder="fas fa-apple-alt" required style="width: 100%; padding: 8px; margin: 5px 0;">
          </div>
          <button type="submit" style="background: #d63384; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">Add Meal Item</button>
        </form>
      `);

      document.getElementById('add-nutrition-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const nutritionData = {
          trimester: document.getElementById('nutrition-trimester').value,
          mealType: document.getElementById('nutrition-meal-type').value,
          food: document.getElementById('nutrition-food').value,
          benefits: document.getElementById('nutrition-benefits').value,
          icon: document.getElementById('nutrition-icon').value
        };
        saveNutritionItem(nutritionData);
        modal.remove();
      });
    }

    // Manage Nutrition Content
    function manageNutritionContent() {
      const modal = createModal('Manage Nutrition Content', `
        <div style="max-height: 500px; overflow-y: auto;">
          <h4>Existing Meal Plans</h4>
          <div id="nutrition-content-list">
            <div style="margin: 1rem 0;">
              <h5>First Trimester</h5>
              <div id="first-trimester-list">Loading...</div>
            </div>
            <div style="margin: 1rem 0;">
              <h5>Second Trimester</h5>
              <div id="second-trimester-list">Loading...</div>
            </div>
            <div style="margin: 1rem 0;">
              <h5>Third Trimester</h5>
              <div id="third-trimester-list">Loading...</div>
            </div>
          </div>
        </div>
      `);
      loadNutritionContent();
    }

    // Add Nutrition Tip
    function addNutritionTip() {
      const modal = createModal('Add Nutrition Tip', `
        <form id="add-tip-form">
          <div style="margin: 10px 0;">
            <label>Tip Title:</label>
            <input type="text" id="tip-title" required style="width: 100%; padding: 8px; margin: 5px 0;" placeholder="e.g., Stay Hydrated">
          </div>
          <div style="margin: 10px 0;">
            <label>Tip Content:</label>
            <textarea id="tip-content" required style="width: 100%; padding: 8px; margin: 5px 0; height: 80px;" placeholder="Detailed nutrition tip..."></textarea>
          </div>
          <div style="margin: 10px 0;">
            <label>Icon (Font Awesome class):</label>
            <input type="text" id="tip-icon" placeholder="fas fa-lightbulb" required style="width: 100%; padding: 8px; margin: 5px 0;">
          </div>
          <button type="submit" style="background: #d63384; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">Add Tip</button>
        </form>
      `);

      document.getElementById('add-tip-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const tipData = {
          title: document.getElementById('tip-title').value,
          content: document.getElementById('tip-content').value,
          icon: document.getElementById('tip-icon').value
        };
        saveNutritionTip(tipData);
        modal.remove();
      });
    }

    // Save Nutrition Item
    async function saveNutritionItem(data) {
      try {
        const response = await fetch('/api/admin/add-nutrition-item', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(data)
        });

        if (response.ok) {
          showNotification('Meal plan item added successfully!', 'success');
          addNutritionItemToDOM(data);
        } else {
          throw new Error('Failed to save nutrition item');
        }
      } catch (error) {
        console.error('Error saving nutrition item:', error);
        showNotification('Error saving nutrition item', 'error');
      }
    }

    // Save Nutrition Tip
    async function saveNutritionTip(data) {
      try {
        const response = await fetch('/api/admin/add-nutrition-tip', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(data)
        });

        if (response.ok) {
          showNotification('Nutrition tip added successfully!', 'success');
          addNutritionTipToDOM(data);
        } else {
          throw new Error('Failed to save nutrition tip');
        }
      } catch (error) {
        console.error('Error saving nutrition tip:', error);
        showNotification('Error saving nutrition tip', 'error');
      }
    }

    // Add nutrition item to DOM
    function addNutritionItemToDOM(data) {
      const trimesterContent = document.getElementById(data.trimester);
      if (trimesterContent) {
        const mealCard = trimesterContent.querySelector('.meal-card');
        if (mealCard) {
          const newItem = document.createElement('li');
          newItem.innerHTML = `<i class="${data.icon}"></i> ${data.food} - ${data.benefits}`;

          // Find the appropriate meal section or create it
          let mealSection = mealCard.querySelector(`h4:contains("${data.mealType}")`);
          if (!mealSection) {
            const newSection = document.createElement('div');
            newSection.innerHTML = `
              <h4><i class="${data.icon}"></i> ${data.mealType.charAt(0).toUpperCase() + data.mealType.slice(1)}</h4>
              <ul></ul>
            `;
            mealCard.appendChild(newSection);
            mealSection = newSection.querySelector('ul');
          } else {
            mealSection = mealSection.nextElementSibling;
          }

          if (mealSection && mealSection.tagName === 'UL') {
            mealSection.appendChild(newItem);
          }
        }
      }
    }

    // Create modal helper function
    function createModal(title, content) {
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.5); z-index: 10000; display: flex;
        align-items: center; justify-content: center;
      `;

      modal.innerHTML = `
        <div style="background: white; padding: 20px; border-radius: 10px; max-width: 600px; width: 90%; max-height: 80%; overflow-y: auto;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="margin: 0; color: #d63384;">${title}</h3>
            <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
          </div>
          ${content}
        </div>
      `;

      modal.className = 'modal';
      document.body.appendChild(modal);
      return modal;
    }

    // Show notification
    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed; top: 20px; right: 20px; z-index: 10001;
        padding: 15px 20px; border-radius: 5px; color: white; font-weight: bold;
        background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
      `;
      notification.textContent = message;
      document.body.appendChild(notification);

      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    // Load nutrition data when page loads
    document.addEventListener('DOMContentLoaded', function() {
      loadNutritionData();
    });

    // Load nutrition data from admin-managed database
    async function loadNutritionData() {
      try {
        // Show loading state
        showLoadingState();

        // Fetch nutrition data from backend
        const response = await fetch('/api/nutrition-data', {
          credentials: 'include'
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data && result.data.length > 0) {
            renderNutritionData(result.data);
            console.log(`✅ Loaded ${result.count} nutrition items`);
          } else {
            console.log('No nutrition data available yet - admin needs to add content');
            showEmptyState();
          }
        } else {
          console.log('No nutrition data available yet - admin needs to add content');
          showEmptyState();
        }
      } catch (error) {
        console.error('Error loading nutrition data:', error);
        showEmptyState();
      }
    }

    function showEmptyState() {
      const trimesters = ['first', 'second', 'third'];
      trimesters.forEach(trimester => {
        const container = document.getElementById(`${trimester}-trimester-content`);
        if (container) {
          container.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-utensils" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
              <h4>No meal plans available</h4>
              <p>Meal plans for the ${trimester} trimester will be added by our nutrition experts.</p>
            </div>
          `;
        }
      });
    }

    function showLoadingState() {
      const trimesters = ['first', 'second', 'third'];
      trimesters.forEach(trimester => {
        const container = document.getElementById(`${trimester}-trimester-content`);
        if (container) {
          container.innerHTML = `
            <div class="loading">
              <i class="fas fa-spinner"></i>
              <p>Loading meal plans...</p>
            </div>
          `;
        }
      });
    }

    function renderNutritionData(data) {
      const trimesters = ['first', 'second', 'third'];

      trimesters.forEach(trimester => {
        const container = document.getElementById(`${trimester}-trimester-content`);
        if (!container) return;

        // Filter data for this trimester (include 'all' trimester items)
        const trimesterData = data.filter(item =>
          item.trimester === trimester || item.trimester === 'all'
        );

        if (trimesterData.length === 0) {
          // Show empty state if no data
          container.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-utensils" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
              <h4>No meal plans available</h4>
              <p>Meal plans for the ${trimester} trimester will be added by our nutrition experts.</p>
            </div>
          `;
          return;
        }

        // Group data by category (admin content uses 'category' not 'meal_type')
        const categories = ['breakfast', 'lunch', 'dinner', 'snacks', 'pregnancy'];
        let html = '';

        categories.forEach(category => {
          const categoryData = trimesterData.filter(item =>
            item.category === category
          );

          if (categoryData.length > 0) {
            const categoryIcons = {
              'breakfast': 'fas fa-sun',
              'lunch': 'fas fa-utensils',
              'dinner': 'fas fa-moon',
              'snacks': 'fas fa-cookie',
              'pregnancy': 'fas fa-heart'
            };

            html += `
              <div class="meal-card" style="background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h4 style="color: #e91e63; margin-bottom: 15px; display: flex; align-items: center; gap: 10px;">
                  <i class="${categoryIcons[category] || 'fas fa-utensils'}"></i>
                  ${formatMealType(category)}
                </h4>
                <div class="meal-items">
            `;

            categoryData.forEach(item => {
              // Admin content structure: title, description, foods (array), tips
              const foodsList = Array.isArray(item.foods) ? item.foods.join(', ') : (item.foods || 'No foods listed');

              html += `
                <div class="meal-item" style="padding: 15px; border-left: 3px solid #e91e63; margin-bottom: 15px; background: #f9f9f9;">
                  <strong style="color: #333; font-size: 16px;">${item.title}</strong>
                  <p style="margin: 8px 0; color: #666; font-size: 14px;">${item.description || ''}</p>
                  <div style="margin: 8px 0;">
                    <strong style="color: #e91e63;">Foods:</strong>
                    <span style="color: #555;">${foodsList}</span>
                  </div>
                  ${item.tips ? `
                    <div style="margin: 8px 0; padding: 8px; background: #e8f5e8; border-radius: 4px;">
                      <strong style="color: #2e7d32;">💡 Tips:</strong>
                      <span style="color: #2e7d32;">${item.tips}</span>
                    </div>
                  ` : ''}
                </div>
              `;
            });

            html += `
                </div>
              </div>
            `;
          }
        });

        if (html) {
          container.innerHTML = html;
        } else {
          // Show empty state if no data
          container.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-utensils" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
              <h4>No meal plans available</h4>
              <p>Meal plans for the ${trimester} trimester will be added by our nutrition experts.</p>
            </div>
          `;
        }
      });
    }

    function formatMealType(mealType) {
      const formatted = mealType.replace(/[-_]/g, ' ');
      return formatted.charAt(0).toUpperCase() + formatted.slice(1);
    }

    // Real-time update functionality
    let lastUpdateCheck = Date.now();
    let updateCheckInterval;

    function startRealTimeUpdates() {
      // Check for updates every 10 seconds
      updateCheckInterval = setInterval(checkForNutritionUpdates, 10000);

      // Also check when page becomes visible
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
          checkForNutritionUpdates();
        }
      });

      console.log('🔄 Real-time nutrition updates started');
    }

    async function checkForNutritionUpdates() {
      try {
        const response = await fetch('/api/nutrition-data', {
          credentials: 'include'
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            const serverLastUpdated = new Date(result.last_updated).getTime();

            if (serverLastUpdated > lastUpdateCheck) {
              console.log('🔄 New nutrition content detected, refreshing...');
              renderNutritionData(result.data);
              lastUpdateCheck = serverLastUpdated;

              // Show notification
              showNotification('📱 Nutrition content updated!', 'success');
            }
          }
        }
      } catch (error) {
        console.error('Error checking for nutrition updates:', error);
      }
    }

    // Enhanced page initialization
    document.addEventListener('DOMContentLoaded', function() {
      loadNutritionData();
      startRealTimeUpdates();

      // Set initial update timestamp
      lastUpdateCheck = Date.now();
    });

    // Stop real-time updates when page is unloaded
    window.addEventListener('beforeunload', () => {
      if (updateCheckInterval) {
        clearInterval(updateCheckInterval);
      }
    });

    // Listen for storage events (cross-tab updates)
    window.addEventListener('storage', function(e) {
      if (e.key === 'nutritionUpdate') {
        console.log('📡 Cross-tab nutrition update detected');
        loadNutritionData();
      }
    });
  </script>

  <!-- Real-time Content Updates -->
  <script src="{{ url_for('static', filename='js/real-time-content.js') }}"></script>
  <script>
    // Initialize real-time content updates specifically for nutrition
    document.addEventListener('DOMContentLoaded', function() {
      if (window.contentManager) {
        // Subscribe to nutrition content updates
        window.contentManager.subscribe('nutrition', function(updateData) {
          console.log('📡 Real-time nutrition update received:', updateData);

          if (updateData && updateData.data && updateData.data.length > 0) {
            renderNutritionData(updateData.data);
            showNotification(`📱 Nutrition plans ${updateData.action}! (${updateData.count} items)`, 'success');
          } else if (updateData && updateData.action === 'deleted') {
            // Reload data after deletion
            loadNutritionData();
            showNotification('📱 Nutrition content updated!', 'info');
          }
        });

        // Start the content manager
        window.contentManager.start();
      }
    });
  </script>
</body>
</html>
