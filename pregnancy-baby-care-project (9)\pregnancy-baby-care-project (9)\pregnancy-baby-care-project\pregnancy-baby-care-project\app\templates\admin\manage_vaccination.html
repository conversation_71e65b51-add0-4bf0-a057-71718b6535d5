<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vaccination Management - Admin Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .admin-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-header h1 {
            color: #667eea;
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .back-btn {
            background: linear-gradient(135deg, #4ecdc4, #87ceeb);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .management-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .section-title {
            color: #667eea;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4ecdc4, #87ceeb);
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .vaccination-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .vaccination-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #4ecdc4;
        }

        .vaccination-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .vaccination-card.overdue {
            border-left-color: #ff6b6b;
        }

        .vaccination-card.completed {
            border-left-color: #51cf66;
        }

        .vaccination-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .vaccination-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .vaccination-status {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-scheduled {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-completed {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-overdue {
            background: #ffebee;
            color: #c62828;
        }

        .vaccination-info {
            margin-bottom: 1rem;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .info-label {
            color: #666;
            font-weight: 500;
        }

        .info-value {
            color: #333;
        }

        .vaccination-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-small {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background: #ffc107;
            color: #333;
        }

        .btn-complete {
            background: #28a745;
            color: white;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .btn-small:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            color: #667eea;
            font-size: 1.5rem;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #4ecdc4;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .error {
            background: #fee;
            color: #c33;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }

        @media (max-width: 768px) {
            .admin-container {
                padding: 1rem;
            }

            .admin-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .vaccination-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <h1>
                <i class="fas fa-syringe"></i>
                Vaccination Management
            </h1>
            <a href="/admin" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Dashboard
            </a>
        </div>

        <!-- Vaccination Management Section -->
        <div class="management-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-calendar-alt"></i>
                    Vaccination Schedule
                </h2>
                <button class="btn-primary" onclick="showAddVaccinationModal()">
                    <i class="fas fa-plus"></i>
                    Add Vaccination
                </button>
            </div>

            <div id="vaccination-list">
                <div class="loading">Loading vaccination records...</div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Vaccination Modal -->
    <div id="vaccination-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">Add Vaccination</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>

            <form id="vaccination-form">
                <div class="form-group">
                    <label class="form-label">Baby</label>
                    <select id="baby-select" class="form-input" required>
                        <option value="">Select a baby</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Vaccine Name</label>
                    <input type="text" id="vaccine-name" class="form-input" required>
                </div>

                <div class="form-group">
                    <label class="form-label">Scheduled Date</label>
                    <input type="date" id="scheduled-date" class="form-input" required>
                </div>

                <div class="form-group">
                    <label class="form-label">Status</label>
                    <select id="status" class="form-input" required>
                        <option value="scheduled">Scheduled</option>
                        <option value="completed">Completed</option>
                        <option value="missed">Missed</option>
                        <option value="postponed">Postponed</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Doctor Name</label>
                    <input type="text" id="doctor-name" class="form-input">
                </div>

                <div class="form-group">
                    <label class="form-label">Notes</label>
                    <textarea id="notes" class="form-input" rows="3"></textarea>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn-primary">Save Vaccination</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentVaccinationId = null;

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadVaccinations();
            loadBabies();
        });

        async function loadVaccinations() {
            try {
                const response = await fetch('/admin/api/vaccination-schedule');
                const data = await response.json();

                if (data.success) {
                    displayVaccinations(data.vaccinations);
                } else {
                    showError('Failed to load vaccination records');
                }
            } catch (error) {
                console.error('Error loading vaccinations:', error);
                showError('Error loading vaccination records');
            }
        }

        async function loadBabies() {
            try {
                const response = await fetch('/babycare/api/admin/all-babies');
                const data = await response.json();

                if (data.success) {
                    const babySelect = document.getElementById('baby-select');
                    babySelect.innerHTML = '<option value="">Select a baby</option>';

                    data.babies.forEach(baby => {
                        const option = document.createElement('option');
                        option.value = baby.id;
                        option.textContent = `${baby.name} (${baby.parent_name})`;
                        babySelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading babies:', error);
            }
        }

        function displayVaccinations(vaccinations) {
            const vaccinationList = document.getElementById('vaccination-list');

            if (vaccinations.length === 0) {
                vaccinationList.innerHTML = '<div class="loading">No vaccination records found</div>';
                return;
            }

            const vaccinationHTML = vaccinations.map(vaccination => {
                const statusClass = getStatusClass(vaccination.status, vaccination.is_overdue);
                const cardClass = vaccination.is_overdue ? 'overdue' : (vaccination.status === 'completed' ? 'completed' : '');

                return `
                    <div class="vaccination-card ${cardClass}">
                        <div class="vaccination-header">
                            <div>
                                <div class="vaccination-name">${vaccination.vaccine_name}</div>
                                <span class="vaccination-status ${statusClass}">
                                    ${vaccination.is_overdue ? 'Overdue' : vaccination.status}
                                </span>
                            </div>
                        </div>

                        <div class="vaccination-info">
                            <div class="info-row">
                                <span class="info-label">Baby:</span>
                                <span class="info-value">${vaccination.baby_name}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Scheduled Date:</span>
                                <span class="info-value">${formatDate(vaccination.scheduled_date)}</span>
                            </div>
                            ${vaccination.administered_date ? `
                                <div class="info-row">
                                    <span class="info-label">Administered:</span>
                                    <span class="info-value">${formatDate(vaccination.administered_date)}</span>
                                </div>
                            ` : ''}
                            ${vaccination.doctor_name ? `
                                <div class="info-row">
                                    <span class="info-label">Doctor:</span>
                                    <span class="info-value">${vaccination.doctor_name}</span>
                                </div>
                            ` : ''}
                        </div>

                        <div class="vaccination-actions">
                            <button class="btn-small btn-edit" onclick="editVaccination(${vaccination.id})">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            ${vaccination.status !== 'completed' ? `
                                <button class="btn-small btn-complete" onclick="markCompleted(${vaccination.id})">
                                    <i class="fas fa-check"></i> Complete
                                </button>
                            ` : ''}
                            <button class="btn-small btn-delete" onclick="deleteVaccination(${vaccination.id})">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            vaccinationList.innerHTML = `<div class="vaccination-grid">${vaccinationHTML}</div>`;
        }

        function getStatusClass(status, isOverdue) {
            if (isOverdue) return 'status-overdue';
            switch (status) {
                case 'completed': return 'status-completed';
                case 'scheduled': return 'status-scheduled';
                default: return 'status-scheduled';
            }
        }

        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString();
        }

        function showAddVaccinationModal() {
            currentVaccinationId = null;
            document.getElementById('modal-title').textContent = 'Add Vaccination';
            document.getElementById('vaccination-form').reset();
            document.getElementById('vaccination-modal').style.display = 'flex';
        }

        function editVaccination(id) {
            // In a real application, you would fetch the vaccination details
            currentVaccinationId = id;
            document.getElementById('modal-title').textContent = 'Edit Vaccination';
            document.getElementById('vaccination-modal').style.display = 'flex';
            // Load vaccination data into form
        }

        function closeModal() {
            document.getElementById('vaccination-modal').style.display = 'none';
            currentVaccinationId = null;
        }

        async function markCompleted(id) {
            if (confirm('Mark this vaccination as completed?')) {
                try {
                    // In a real application, you would make an API call to update the status
                    showNotification('Vaccination marked as completed', 'success');
                    loadVaccinations();
                } catch (error) {
                    showNotification('Error updating vaccination status', 'error');
                }
            }
        }

        async function deleteVaccination(id) {
            if (confirm('Are you sure you want to delete this vaccination record?')) {
                try {
                    // In a real application, you would make an API call to delete
                    showNotification('Vaccination record deleted', 'success');
                    loadVaccinations();
                } catch (error) {
                    showNotification('Error deleting vaccination record', 'error');
                }
            }
        }

        // Handle form submission
        document.getElementById('vaccination-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                baby_id: document.getElementById('baby-select').value,
                vaccine_name: document.getElementById('vaccine-name').value,
                scheduled_date: document.getElementById('scheduled-date').value,
                status: document.getElementById('status').value,
                doctor_name: document.getElementById('doctor-name').value,
                notes: document.getElementById('notes').value
            };

            try {
                const url = currentVaccinationId
                    ? `/babycare/api/vaccinations/${currentVaccinationId}`
                    : `/babycare/api/babies/${formData.baby_id}/vaccinations`;

                const method = currentVaccinationId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();

                if (data.success) {
                    showNotification(currentVaccinationId ? 'Vaccination updated successfully' : 'Vaccination added successfully', 'success');
                    closeModal();
                    loadVaccinations();
                } else {
                    showNotification(data.error || 'Error saving vaccination', 'error');
                }
            } catch (error) {
                console.error('Error saving vaccination:', error);
                showNotification('Error saving vaccination', 'error');
            }
        });

        function showError(message) {
            document.getElementById('vaccination-list').innerHTML = `<div class="error">${message}</div>`;
        }

        function showNotification(message, type) {
            // Simple notification system
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : '#dc3545'};
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                z-index: 1001;
                max-width: 300px;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }
    </script>
</body>
</html>