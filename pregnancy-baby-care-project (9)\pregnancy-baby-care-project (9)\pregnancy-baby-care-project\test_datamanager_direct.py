#!/usr/bin/env python3
"""
Test script to test DataManager methods directly
"""

import sys
import os
sys.path.insert(0, 'pregnancy-baby-care-project')

def test_datamanager_direct():
    """Test DataManager methods directly"""
    
    print("🧪 TESTING DATAMANAGER METHODS DIRECTLY")
    print("=" * 60)
    
    try:
        from app.data_manager import DataManager
        
        # Test 1: Test nutrition content
        print("1. Testing DataManager.get_all_nutrition_content()...")
        nutrition_data = DataManager.get_all_nutrition_content()
        print(f"   ✅ Nutrition data retrieved: {len(nutrition_data)} items")
        if nutrition_data:
            print(f"   📋 Sample item: {nutrition_data[0]['title']}")
        
        # Test 2: Test FAQ content
        print("2. Testing DataManager.get_all_faqs()...")
        faq_data = DataManager.get_all_faqs()
        print(f"   ✅ FAQ data retrieved: {len(faq_data)} items")
        if faq_data:
            print(f"   📋 Sample FAQ: {faq_data[0]['question'][:50]}...")
        
        # Test 3: Test schemes content
        print("3. Testing DataManager.get_all_schemes()...")
        schemes_data = DataManager.get_all_schemes()
        print(f"   ✅ Schemes data retrieved: {len(schemes_data)} items")
        if schemes_data:
            print(f"   📋 Sample scheme: {schemes_data[0].get('name', 'No name field')}")
            print(f"   📋 Scheme keys: {list(schemes_data[0].keys())}")
        
        # Test 4: Test vaccination content
        print("4. Testing DataManager.get_all_vaccination_schedules()...")
        vaccination_data = DataManager.get_all_vaccination_schedules()
        print(f"   ✅ Vaccination data retrieved: {len(vaccination_data)} items")
        if vaccination_data:
            print(f"   📋 Sample vaccine: {vaccination_data[0]['vaccine_name']}")
        
        print("\n🎉 DATAMANAGER DIRECT TESTING COMPLETE!")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_datamanager_direct()
