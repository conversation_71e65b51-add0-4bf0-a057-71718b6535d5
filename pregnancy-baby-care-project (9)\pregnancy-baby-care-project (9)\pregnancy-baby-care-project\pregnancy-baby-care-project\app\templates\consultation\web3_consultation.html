<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web3 Doctor Consultation - Maternal and Child Health Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/web3@latest/dist/web3.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        :root {
            --primary: #d63384;
            --secondary: #6f42c1;
            --success: #198754;
            --info: #0dcaf0;
            --warning: #ffc107;
            --danger: #dc3545;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --light-gray: #e9ecef;
            --gradient-primary: linear-gradient(135deg, #d63384 0%, #6f42c1 100%);
            --gradient-secondary: linear-gradient(135deg, #0dcaf0 0%, #198754 100%);
        }

        body {
            background: linear-gradient(135deg, #f9f0ff 0%, #f0f9ff 100%);
            color: var(--dark);
            min-height: 100vh;
            padding-top: 80px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 0.5rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--primary);
            font-weight: 700;
            font-size: 1.5rem;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 0.5rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-link:hover {
            color: var(--primary);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .page-header {
            text-align: center;
            padding: 3rem 0;
            background: var(--gradient-primary);
            color: white;
            margin-bottom: 3rem;
        }

        .page-header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .page-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .web3-status {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 2px solid var(--light-gray);
        }

        .web3-status.connected {
            border-color: var(--success);
            background: linear-gradient(135deg, #f8fff9 0%, #f0fff4 100%);
        }

        .web3-status.disconnected {
            border-color: var(--warning);
            background: linear-gradient(135deg, #fffbf0 0%, #fff8e1 100%);
        }

        .status-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .status-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .status-icon.connected {
            background: var(--success);
            color: white;
        }

        .status-icon.disconnected {
            background: var(--warning);
            color: white;
        }

        .consultation-form {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .form-section {
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(214, 51, 132, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(214, 51, 132, 0.3);
        }

        .btn-secondary {
            background: var(--gradient-secondary);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(13, 202, 240, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .blockchain-info {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid var(--info);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .blockchain-info h3 {
            color: var(--info);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .notification {
            position: fixed;
            top: 100px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success);
        }

        .notification.error {
            background: var(--danger);
        }

        .notification.warning {
            background: var(--warning);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .nav-container {
                padding: 0 1rem;
            }
            
            .container {
                padding: 0 1rem;
            }
            
            .page-header h1 {
                font-size: 2.5rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="/" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-stethoscope"></i>
                </div>
                <span>Web3 Consultation</span>
            </a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="/" class="nav-link"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="/pregnancy" class="nav-link"><i class="fas fa-heart"></i> Pregnancy</a></li>
                    <li><a href="/babycare" class="nav-link"><i class="fas fa-baby"></i> Baby Care</a></li>
                    <li><a href="/doctor" class="nav-link"><i class="fas fa-user-md"></i> Doctor</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <div class="page-header">
            <div class="container">
                <h1><i class="fas fa-cube"></i> Web3 Doctor Consultation</h1>
                <p>Secure, blockchain-verified medical consultations with automated email notifications</p>
            </div>
        </div>

        <div class="container">
            <!-- Web3 Connection Status -->
            <div class="web3-status" id="web3Status">
                <div class="status-header">
                    <div class="status-icon" id="statusIcon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div>
                        <h3 id="statusTitle">Connect Your Web3 Wallet</h3>
                        <p id="statusMessage">Please connect your MetaMask wallet to proceed with blockchain-verified consultation booking.</p>
                    </div>
                </div>
                <button class="btn btn-primary" id="connectWalletBtn" onclick="connectWallet()">
                    <i class="fas fa-plug"></i> Connect Wallet
                </button>
            </div>

            <!-- Blockchain Information -->
            <div class="blockchain-info">
                <h3><i class="fas fa-info-circle"></i> How Web3 Consultation Works</h3>
                <ul style="margin-left: 1.5rem; line-height: 1.8;">
                    <li><strong>Secure Booking:</strong> Your consultation request is recorded on the blockchain for transparency</li>
                    <li><strong>Smart Contracts:</strong> Automated appointment confirmation and payment processing</li>
                    <li><strong>Email Notifications:</strong> Both you and the doctor receive instant email confirmations</li>
                    <li><strong>Immutable Records:</strong> All consultation details are permanently stored and verifiable</li>
                </ul>
            </div>

            <!-- Consultation Form -->
            <div class="consultation-form" id="consultationForm" style="display: none;">
                <form id="web3ConsultationForm">
                    <!-- Patient Information -->
                    <div class="form-section">
                        <h2 class="section-title">
                            <i class="fas fa-user"></i> Patient Information
                        </h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="patientName">Full Name *</label>
                                <input type="text" id="patientName" name="patientName" required>
                            </div>
                            <div class="form-group">
                                <label for="patientEmail">Email Address *</label>
                                <input type="email" id="patientEmail" name="patientEmail" required>
                            </div>
                            <div class="form-group">
                                <label for="patientPhone">Phone Number *</label>
                                <input type="tel" id="patientPhone" name="patientPhone" required>
                            </div>
                            <div class="form-group">
                                <label for="patientAge">Age</label>
                                <input type="number" id="patientAge" name="patientAge" min="1" max="120">
                            </div>
                        </div>
                    </div>

                    <!-- Doctor Selection -->
                    <div class="form-section">
                        <h2 class="section-title">
                            <i class="fas fa-user-md"></i> Doctor Selection
                        </h2>
                        <div class="form-group">
                            <label for="doctorSelect">Choose Doctor *</label>
                            <select id="doctorSelect" name="doctorSelect" required>
                                <option value="">Loading doctors...</option>
                            </select>
                        </div>
                    </div>

                    <!-- Consultation Details -->
                    <div class="form-section">
                        <h2 class="section-title">
                            <i class="fas fa-calendar-alt"></i> Consultation Details
                        </h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="consultationType">Consultation Type *</label>
                                <select id="consultationType" name="consultationType" required>
                                    <option value="">Select consultation type...</option>
                                    <option value="general">General Consultation</option>
                                    <option value="prenatal">Prenatal Checkup</option>
                                    <option value="postnatal">Postnatal Care</option>
                                    <option value="pediatric">Pediatric Consultation</option>
                                    <option value="emergency">Emergency Consultation</option>
                                    <option value="follow-up">Follow-up Visit</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="preferredDate">Preferred Date *</label>
                                <input type="date" id="preferredDate" name="preferredDate" required>
                            </div>
                            <div class="form-group">
                                <label for="preferredTime">Preferred Time *</label>
                                <select id="preferredTime" name="preferredTime" required>
                                    <option value="">Select time...</option>
                                    <option value="09:00">09:00 AM</option>
                                    <option value="09:30">09:30 AM</option>
                                    <option value="10:00">10:00 AM</option>
                                    <option value="10:30">10:30 AM</option>
                                    <option value="11:00">11:00 AM</option>
                                    <option value="11:30">11:30 AM</option>
                                    <option value="14:00">02:00 PM</option>
                                    <option value="14:30">02:30 PM</option>
                                    <option value="15:00">03:00 PM</option>
                                    <option value="15:30">03:30 PM</option>
                                    <option value="16:00">04:00 PM</option>
                                    <option value="16:30">04:30 PM</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="urgencyLevel">Urgency Level *</label>
                                <select id="urgencyLevel" name="urgencyLevel" required>
                                    <option value="">Select urgency...</option>
                                    <option value="routine">Routine</option>
                                    <option value="urgent">Urgent</option>
                                    <option value="emergency">Emergency</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="symptoms">Symptoms / Reason for Consultation *</label>
                            <textarea id="symptoms" name="symptoms" required placeholder="Please describe your symptoms, concerns, or reason for consultation..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="medicalHistory">Relevant Medical History</label>
                            <textarea id="medicalHistory" name="medicalHistory" placeholder="Any relevant medical history, current medications, allergies..."></textarea>
                        </div>
                    </div>

                    <!-- Blockchain Verification -->
                    <div class="form-section">
                        <h2 class="section-title">
                            <i class="fas fa-shield-alt"></i> Blockchain Verification
                        </h2>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="blockchainConsent" name="blockchainConsent" required>
                                I consent to having my consultation request recorded on the blockchain for verification and transparency
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="emailConsent" name="emailConsent" required>
                                I consent to receiving email notifications about my consultation status
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div style="text-align: center; margin-top: 2rem;">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-paper-plane"></i> Submit Web3 Consultation Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <script>
        let web3;
        let userAccount;
        let isWeb3Connected = false;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            checkWeb3Connection();
            loadDoctors();
            setupForm();
            setMinDate();
        });

        // Check if Web3 is available and connected
        async function checkWeb3Connection() {
            if (typeof window.ethereum !== 'undefined') {
                web3 = new Web3(window.ethereum);
                
                // Check if already connected
                const accounts = await web3.eth.getAccounts();
                if (accounts.length > 0) {
                    userAccount = accounts[0];
                    updateWeb3Status(true);
                }
            } else {
                updateWeb3Status(false, 'MetaMask not detected. Please install MetaMask to use Web3 features.');
            }
        }

        // Connect to Web3 wallet
        async function connectWallet() {
            if (typeof window.ethereum !== 'undefined') {
                try {
                    const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
                    userAccount = accounts[0];
                    web3 = new Web3(window.ethereum);
                    updateWeb3Status(true);
                    showNotification('Wallet connected successfully!', 'success');
                } catch (error) {
                    console.error('Error connecting wallet:', error);
                    showNotification('Failed to connect wallet. Please try again.', 'error');
                }
            } else {
                showNotification('Please install MetaMask to connect your wallet.', 'warning');
                window.open('https://metamask.io/', '_blank');
            }
        }

        // Update Web3 connection status
        function updateWeb3Status(connected, message = '') {
            const statusDiv = document.getElementById('web3Status');
            const statusIcon = document.getElementById('statusIcon');
            const statusTitle = document.getElementById('statusTitle');
            const statusMessage = document.getElementById('statusMessage');
            const connectBtn = document.getElementById('connectWalletBtn');
            const consultationForm = document.getElementById('consultationForm');

            if (connected) {
                isWeb3Connected = true;
                statusDiv.className = 'web3-status connected';
                statusIcon.className = 'status-icon connected';
                statusIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
                statusTitle.textContent = 'Web3 Wallet Connected';
                statusMessage.textContent = `Connected to: ${userAccount}`;
                connectBtn.style.display = 'none';
                consultationForm.style.display = 'block';
            } else {
                isWeb3Connected = false;
                statusDiv.className = 'web3-status disconnected';
                statusIcon.className = 'status-icon disconnected';
                statusIcon.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                statusTitle.textContent = 'Web3 Wallet Not Connected';
                statusMessage.textContent = message || 'Please connect your MetaMask wallet to proceed.';
                connectBtn.style.display = 'inline-flex';
                consultationForm.style.display = 'none';
            }
        }

        // Load doctors from API
        async function loadDoctors() {
            try {
                const response = await fetch('/doctor/api/doctors');
                if (response.ok) {
                    const result = await response.json();
                    const doctorSelect = document.getElementById('doctorSelect');
                    
                    doctorSelect.innerHTML = '<option value="">Select a doctor...</option>';
                    
                    if (result.success && result.doctors.length > 0) {
                        result.doctors.forEach(doctor => {
                            const option = document.createElement('option');
                            option.value = doctor.id;
                            option.textContent = `Dr. ${doctor.full_name} - ${doctor.specialization || 'General Practice'}`;
                            option.dataset.email = doctor.email;
                            doctorSelect.appendChild(option);
                        });
                    } else {
                        doctorSelect.innerHTML = '<option value="">No doctors available</option>';
                    }
                } else {
                    console.error('Failed to load doctors');
                    document.getElementById('doctorSelect').innerHTML = '<option value="">Error loading doctors</option>';
                }
            } catch (error) {
                console.error('Error loading doctors:', error);
                document.getElementById('doctorSelect').innerHTML = '<option value="">Error loading doctors</option>';
            }
        }

        // Set minimum date to today
        function setMinDate() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('preferredDate').min = today;
        }

        // Setup form submission
        function setupForm() {
            document.getElementById('web3ConsultationForm').addEventListener('submit', handleFormSubmission);
        }

        // Handle form submission
        async function handleFormSubmission(e) {
            e.preventDefault();
            
            if (!isWeb3Connected) {
                showNotification('Please connect your Web3 wallet first.', 'warning');
                return;
            }

            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;
            
            try {
                // Show loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<div class="loading"></div> Processing...';

                // Collect form data
                const formData = collectFormData();
                
                // Create blockchain transaction hash (simulated)
                const transactionHash = await createBlockchainRecord(formData);
                
                // Submit to backend with blockchain verification
                const response = await submitConsultationRequest({
                    ...formData,
                    blockchainHash: transactionHash,
                    walletAddress: userAccount
                });

                if (response.success) {
                    showNotification('Consultation request submitted successfully! Email notifications sent.', 'success');
                    document.getElementById('web3ConsultationForm').reset();
                    setMinDate(); // Reset min date after form reset
                } else {
                    throw new Error(response.message || 'Failed to submit consultation request');
                }

            } catch (error) {
                console.error('Error submitting consultation:', error);
                showNotification('Failed to submit consultation request. Please try again.', 'error');
            } finally {
                // Restore button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }

        // Collect form data
        function collectFormData() {
            const form = document.getElementById('web3ConsultationForm');
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // Get doctor email
            const doctorSelect = document.getElementById('doctorSelect');
            const selectedOption = doctorSelect.options[doctorSelect.selectedIndex];
            data.doctorEmail = selectedOption.dataset.email;
            data.doctorName = selectedOption.textContent;
            
            return data;
        }

        // Create blockchain record (simulated)
        async function createBlockchainRecord(formData) {
            // In a real implementation, this would create an actual blockchain transaction
            // For demo purposes, we'll simulate this with a hash
            const dataString = JSON.stringify(formData) + Date.now();
            const hash = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(dataString));
            const hashArray = Array.from(new Uint8Array(hash));
            const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
            
            // Simulate blockchain transaction delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            return '0x' + hashHex.substring(0, 64); // Simulate Ethereum transaction hash
        }

        // Submit consultation request to backend
        async function submitConsultationRequest(data) {
            const response = await fetch('/api/web3-consultation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify(data)
            });
            
            return await response.json();
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i> ${message}`;
            
            document.body.appendChild(notification);
            
            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);
            
            // Hide and remove notification
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 5000);
        }

        // Listen for account changes
        if (window.ethereum) {
            window.ethereum.on('accountsChanged', function (accounts) {
                if (accounts.length === 0) {
                    updateWeb3Status(false, 'Please connect your wallet.');
                } else {
                    userAccount = accounts[0];
                    updateWeb3Status(true);
                }
            });
        }
    </script>
</body>
</html>
