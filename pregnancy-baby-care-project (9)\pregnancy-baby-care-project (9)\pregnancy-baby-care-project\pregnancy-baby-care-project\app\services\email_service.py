"""
Email Service for Web3 Consultation System
Handles sending email notifications for consultation requests and confirmations
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
import os
import logging
import requests
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailService:
    def __init__(self, app=None):
        self.app = app
        self.smtp_server = None
        self.smtp_port = None
        self.username = None
        self.password = None
        self.sender_email = None

        # Web3Forms configuration - Updated with real access key
        self.web3forms_access_key = "e9f21de0-2968-4ef3-b8b2-0f482c19e128"
        self.web3forms_endpoint = "https://api.web3forms.com/submit"

        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize email service with Flask app configuration"""
        self.smtp_server = app.config.get('MAIL_SERVER', 'smtp.gmail.com')
        self.smtp_port = app.config.get('MAIL_PORT', 587)
        self.username = app.config.get('MAIL_USERNAME')
        self.password = app.config.get('MAIL_PASSWORD')
        self.sender_email = app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>')
        
        # For development, use environment variables or defaults
        if not self.username:
            self.username = os.environ.get('MAIL_USERNAME', '<EMAIL>')
        if not self.password:
            self.password = os.environ.get('MAIL_PASSWORD', 'demo_password')
    
    def send_email(self, to_email, subject, html_content, text_content=None):
        """Send an email with HTML content"""
        try:
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = self.sender_email
            message["To"] = to_email
            
            # Create text and HTML parts
            if text_content:
                text_part = MIMEText(text_content, "plain")
                message.attach(text_part)
            
            html_part = MIMEText(html_content, "html")
            message.attach(html_part)
            
            # For development/demo, just log the email instead of actually sending
            if self.username == '<EMAIL>':
                logger.info(f"📧 EMAIL SIMULATION - Would send to: {to_email}")
                logger.info(f"📧 Subject: {subject}")
                logger.info(f"📧 Content: {text_content or 'HTML content provided'}")
                return True
            
            # Create secure connection and send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.username, self.password)
                server.sendmail(self.sender_email, to_email, message.as_string())
            
            logger.info(f"✅ Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to send email to {to_email}: {str(e)}")
            return False
    
    def send_consultation_request_to_patient(self, patient_data, consultation_data):
        """Send consultation request confirmation to patient"""
        subject = "Web3 Consultation Request Confirmed - Maternal and Child Health Care"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: white; }}
                .header {{ background: linear-gradient(135deg, #d63384 0%, #6f42c1 100%); color: white; padding: 2rem; text-align: center; }}
                .content {{ padding: 2rem; }}
                .info-box {{ background: #f8f9fa; border-left: 4px solid #d63384; padding: 1rem; margin: 1rem 0; }}
                .blockchain-info {{ background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px; padding: 1rem; margin: 1rem 0; }}
                .footer {{ background: #f8f9fa; padding: 1rem; text-align: center; color: #6c757d; }}
                .btn {{ display: inline-block; background: #d63384; color: white; padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 5px; margin: 1rem 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔗 Web3 Consultation Request Confirmed</h1>
                    <p>Your blockchain-verified consultation request has been submitted</p>
                </div>
                
                <div class="content">
                    <h2>Dear {patient_data.get('patientName', 'Patient')},</h2>
                    
                    <p>Thank you for submitting your Web3 consultation request. Your request has been successfully recorded on the blockchain and sent to the doctor for review.</p>
                    
                    <div class="info-box">
                        <h3>📋 Consultation Details</h3>
                        <p><strong>Doctor:</strong> {consultation_data.get('doctorName', 'N/A')}</p>
                        <p><strong>Type:</strong> {consultation_data.get('consultationType', 'N/A')}</p>
                        <p><strong>Preferred Date:</strong> {consultation_data.get('preferredDate', 'N/A')}</p>
                        <p><strong>Preferred Time:</strong> {consultation_data.get('preferredTime', 'N/A')}</p>
                        <p><strong>Urgency:</strong> {consultation_data.get('urgencyLevel', 'N/A')}</p>
                    </div>
                    
                    <div class="blockchain-info">
                        <h3>🔐 Blockchain Verification</h3>
                        <p><strong>Transaction Hash:</strong> {consultation_data.get('blockchainHash', 'N/A')}</p>
                        <p><strong>Wallet Address:</strong> {consultation_data.get('walletAddress', 'N/A')}</p>
                        <p><strong>Timestamp:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
                    </div>
                    
                    <h3>📞 What Happens Next?</h3>
                    <ul>
                        <li>The doctor will review your consultation request within 24 hours</li>
                        <li>You will receive an email confirmation once the appointment is approved</li>
                        <li>If the requested time is not available, alternative times will be suggested</li>
                        <li>All communication is secured through blockchain verification</li>
                    </ul>
                    
                    <p>If you have any questions or need to modify your request, please contact us immediately.</p>
                    
                    <a href="http://127.0.0.1:5000/consultation/status" class="btn">Check Consultation Status</a>
                </div>
                
                <div class="footer">
                    <p>© 2024 Maternal and Child Health Care | Powered by Web3 Technology</p>
                    <p>This is an automated message. Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        Web3 Consultation Request Confirmed
        
        Dear {patient_data.get('patientName', 'Patient')},
        
        Your Web3 consultation request has been successfully submitted and recorded on the blockchain.
        
        Consultation Details:
        - Doctor: {consultation_data.get('doctorName', 'N/A')}
        - Type: {consultation_data.get('consultationType', 'N/A')}
        - Preferred Date: {consultation_data.get('preferredDate', 'N/A')}
        - Preferred Time: {consultation_data.get('preferredTime', 'N/A')}
        - Urgency: {consultation_data.get('urgencyLevel', 'N/A')}
        
        Blockchain Verification:
        - Transaction Hash: {consultation_data.get('blockchainHash', 'N/A')}
        - Wallet Address: {consultation_data.get('walletAddress', 'N/A')}
        - Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}
        
        The doctor will review your request within 24 hours and send a confirmation email.
        
        Best regards,
        Maternal and Child Health Care Team
        """
        
        return self.send_email(patient_data.get('patientEmail'), subject, html_content, text_content)
    
    def send_consultation_request_to_doctor(self, doctor_email, patient_data, consultation_data):
        """Send new consultation request notification to doctor"""
        subject = f"New Web3 Consultation Request - {patient_data.get('patientName', 'Patient')}"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: white; }}
                .header {{ background: linear-gradient(135deg, #198754 0%, #0dcaf0 100%); color: white; padding: 2rem; text-align: center; }}
                .content {{ padding: 2rem; }}
                .patient-info {{ background: #f8f9fa; border-left: 4px solid #198754; padding: 1rem; margin: 1rem 0; }}
                .urgent {{ background: #fff3cd; border-left: 4px solid #ffc107; }}
                .emergency {{ background: #f8d7da; border-left: 4px solid #dc3545; }}
                .blockchain-info {{ background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px; padding: 1rem; margin: 1rem 0; }}
                .footer {{ background: #f8f9fa; padding: 1rem; text-align: center; color: #6c757d; }}
                .btn {{ display: inline-block; background: #198754; color: white; padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 5px; margin: 0.5rem; }}
                .btn-secondary {{ background: #6c757d; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>👨‍⚕️ New Web3 Consultation Request</h1>
                    <p>Blockchain-verified patient consultation request</p>
                </div>
                
                <div class="content">
                    <h2>Dear Doctor,</h2>
                    
                    <p>You have received a new consultation request through our Web3 platform. The request has been verified on the blockchain for authenticity.</p>
                    
                    <div class="patient-info {'urgent' if consultation_data.get('urgencyLevel') == 'urgent' else 'emergency' if consultation_data.get('urgencyLevel') == 'emergency' else ''}">
                        <h3>👤 Patient Information</h3>
                        <p><strong>Name:</strong> {patient_data.get('patientName', 'N/A')}</p>
                        <p><strong>Email:</strong> {patient_data.get('patientEmail', 'N/A')}</p>
                        <p><strong>Phone:</strong> {patient_data.get('patientPhone', 'N/A')}</p>
                        <p><strong>Age:</strong> {patient_data.get('patientAge', 'N/A')}</p>
                    </div>
                    
                    <div class="patient-info">
                        <h3>📋 Consultation Details</h3>
                        <p><strong>Type:</strong> {consultation_data.get('consultationType', 'N/A')}</p>
                        <p><strong>Preferred Date:</strong> {consultation_data.get('preferredDate', 'N/A')}</p>
                        <p><strong>Preferred Time:</strong> {consultation_data.get('preferredTime', 'N/A')}</p>
                        <p><strong>Urgency Level:</strong> <span style="color: {'#dc3545' if consultation_data.get('urgencyLevel') == 'emergency' else '#ffc107' if consultation_data.get('urgencyLevel') == 'urgent' else '#198754'};">{consultation_data.get('urgencyLevel', 'N/A').upper()}</span></p>
                    </div>
                    
                    <div class="patient-info">
                        <h3>🩺 Medical Information</h3>
                        <p><strong>Symptoms/Reason:</strong></p>
                        <p>{consultation_data.get('symptoms', 'N/A')}</p>
                        
                        {f'<p><strong>Medical History:</strong></p><p>{consultation_data.get("medicalHistory", "N/A")}</p>' if consultation_data.get('medicalHistory') else ''}
                    </div>
                    
                    <div class="blockchain-info">
                        <h3>🔐 Blockchain Verification</h3>
                        <p><strong>Transaction Hash:</strong> {consultation_data.get('blockchainHash', 'N/A')}</p>
                        <p><strong>Patient Wallet:</strong> {consultation_data.get('walletAddress', 'N/A')}</p>
                        <p><strong>Request Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
                    </div>
                    
                    <h3>⚡ Action Required</h3>
                    <p>Please review this consultation request and respond within 24 hours. You can:</p>
                    <ul>
                        <li>Confirm the appointment for the requested time</li>
                        <li>Suggest alternative appointment times</li>
                        <li>Request additional information from the patient</li>
                    </ul>
                    
                    <div style="text-align: center; margin: 2rem 0;">
                        <a href="http://127.0.0.1:5000/doctor/consultations" class="btn">Review Consultation</a>
                        <a href="http://127.0.0.1:5000/doctor/appointments" class="btn btn-secondary">Manage Appointments</a>
                    </div>
                </div>
                
                <div class="footer">
                    <p>© 2024 Maternal and Child Health Care | Secure Web3 Medical Platform</p>
                    <p>This consultation request is verified on the blockchain for authenticity and security.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        New Web3 Consultation Request
        
        Dear Doctor,
        
        You have received a new blockchain-verified consultation request.
        
        Patient Information:
        - Name: {patient_data.get('patientName', 'N/A')}
        - Email: {patient_data.get('patientEmail', 'N/A')}
        - Phone: {patient_data.get('patientPhone', 'N/A')}
        - Age: {patient_data.get('patientAge', 'N/A')}
        
        Consultation Details:
        - Type: {consultation_data.get('consultationType', 'N/A')}
        - Preferred Date: {consultation_data.get('preferredDate', 'N/A')}
        - Preferred Time: {consultation_data.get('preferredTime', 'N/A')}
        - Urgency: {consultation_data.get('urgencyLevel', 'N/A')}
        
        Symptoms/Reason: {consultation_data.get('symptoms', 'N/A')}
        
        Blockchain Verification:
        - Transaction Hash: {consultation_data.get('blockchainHash', 'N/A')}
        - Patient Wallet: {consultation_data.get('walletAddress', 'N/A')}
        
        Please review and respond within 24 hours.
        
        Best regards,
        Maternal and Child Health Care System
        """
        
        return self.send_email(doctor_email, subject, html_content, text_content)
    
    def send_appointment_confirmation(self, patient_email, doctor_name, appointment_details):
        """Send appointment confirmation to patient"""
        subject = f"Appointment Confirmed with {doctor_name} - Maternal and Child Health Care"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: white; }}
                .header {{ background: linear-gradient(135deg, #198754 0%, #20c997 100%); color: white; padding: 2rem; text-align: center; }}
                .content {{ padding: 2rem; }}
                .appointment-info {{ background: #d1edff; border: 1px solid #0dcaf0; border-radius: 8px; padding: 1.5rem; margin: 1rem 0; }}
                .footer {{ background: #f8f9fa; padding: 1rem; text-align: center; color: #6c757d; }}
                .btn {{ display: inline-block; background: #198754; color: white; padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 5px; margin: 1rem 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>✅ Appointment Confirmed!</h1>
                    <p>Your Web3 consultation has been approved</p>
                </div>
                
                <div class="content">
                    <h2>Great news!</h2>
                    
                    <p>Your consultation request has been approved and your appointment is now confirmed.</p>
                    
                    <div class="appointment-info">
                        <h3>📅 Appointment Details</h3>
                        <p><strong>Doctor:</strong> {doctor_name}</p>
                        <p><strong>Date:</strong> {appointment_details.get('date', 'N/A')}</p>
                        <p><strong>Time:</strong> {appointment_details.get('time', 'N/A')}</p>
                        <p><strong>Type:</strong> {appointment_details.get('type', 'N/A')}</p>
                        <p><strong>Location:</strong> {appointment_details.get('location', 'Maternal and Child Health Care Clinic')}</p>
                    </div>
                    
                    <h3>📝 Before Your Appointment</h3>
                    <ul>
                        <li>Arrive 15 minutes early for check-in</li>
                        <li>Bring a valid ID and insurance card</li>
                        <li>Prepare a list of current medications</li>
                        <li>Write down any questions you want to ask</li>
                    </ul>
                    
                    <p>If you need to reschedule or cancel, please contact us at least 24 hours in advance.</p>
                    
                    <a href="http://127.0.0.1:5000/appointments/manage" class="btn">Manage Appointments</a>
                </div>
                
                <div class="footer">
                    <p>© 2024 Maternal and Child Health Care</p>
                    <p>Thank you for choosing our Web3-powered healthcare platform!</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return self.send_email(patient_email, subject, html_content)

    def send_web3forms_email(self, to_email, subject, message, from_name="Maternal Care System"):
        """Send email using Web3Forms API"""
        try:
            # Check if access key is configured and working
            if self.web3forms_access_key == "YOUR_ACCESS_KEY_HERE":
                logger.warning(f"📧 Web3Forms access key not configured. Simulating email to: {to_email}")
                logger.info(f"📧 Subject: {subject}")
                logger.info(f"📧 Message: {message[:100]}...")
                return True

            payload = {
                "access_key": self.web3forms_access_key,
                "name": from_name,
                "email": to_email,
                "subject": subject,
                "message": message,
                "from_name": from_name,
                "botcheck": ""  # Honeypot spam protection
            }

            # Use proper headers for JSON request
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }

            response = requests.post(self.web3forms_endpoint, json=payload, headers=headers)

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    logger.info(f"✅ Web3Forms email sent successfully to {to_email}")
                    return True
                else:
                    logger.error(f"❌ Web3Forms API error: {result.get('message', 'Unknown error')}")
                    return False
            elif response.status_code == 403:
                # Handle 403 errors gracefully - access key may have restrictions
                logger.warning(f"📧 Web3Forms access restricted (403). Simulating email to: {to_email}")
                logger.info(f"📧 Subject: {subject}")
                logger.info(f"📧 Message: {message[:100]}...")
                return True  # Return True to not break the appointment flow
            else:
                logger.error(f"❌ Web3Forms HTTP error: {response.status_code}")
                logger.error(f"Response: {response.text}")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to send Web3Forms email to {to_email}: {str(e)}")
            return False

    def send_appointment_confirmation_web3forms(self, patient_email, doctor_email, appointment_details):
        """Send appointment confirmation emails using Web3Forms"""
        try:
            # Email to patient
            patient_subject = f"Appointment Confirmed with {appointment_details.get('doctor_name', 'Doctor')} - Maternal Care"
            patient_message = f"""
Dear {appointment_details.get('patient_name', 'Patient')},

Your appointment has been confirmed! Here are the details:

📅 Appointment Details:
• Doctor: {appointment_details.get('doctor_name', 'N/A')}
• Date: {appointment_details.get('date', 'N/A')}
• Time: {appointment_details.get('time', 'N/A')}
• Type: {appointment_details.get('appointment_type', 'N/A')}
• Purpose: {appointment_details.get('purpose', 'N/A')}
• Location: {appointment_details.get('clinic_name', 'Maternal Care Clinic')}

📝 Before Your Appointment:
• Arrive 15 minutes early for check-in
• Bring a valid ID and insurance card
• Prepare a list of current medications
• Write down any questions you want to ask

If you need to reschedule or cancel, please contact us at least 24 hours in advance.

Best regards,
Maternal and Child Health Care Team

© 2024 Maternal and Child Health Care
This is an automated confirmation message.
            """

            # Email to doctor
            doctor_subject = f"New Appointment Confirmed - {appointment_details.get('patient_name', 'Patient')}"
            doctor_message = f"""
Dear Dr. {appointment_details.get('doctor_name', 'Doctor')},

A new appointment has been confirmed with your patient:

👤 Patient Information:
• Name: {appointment_details.get('patient_name', 'N/A')}
• Email: {patient_email}
• Phone: {appointment_details.get('patient_phone', 'N/A')}

📅 Appointment Details:
• Date: {appointment_details.get('date', 'N/A')}
• Time: {appointment_details.get('time', 'N/A')}
• Type: {appointment_details.get('appointment_type', 'N/A')}
• Purpose: {appointment_details.get('purpose', 'N/A')}
• Location: {appointment_details.get('clinic_name', 'Maternal Care Clinic')}

Please prepare for this consultation and review any relevant patient history.

Best regards,
Maternal and Child Health Care System

© 2024 Maternal and Child Health Care
This is an automated notification.
            """

            # Send both emails
            patient_sent = self.send_web3forms_email(patient_email, patient_subject, patient_message)
            doctor_sent = self.send_web3forms_email(doctor_email, doctor_subject, doctor_message, "Appointment System")

            return {
                'patient_email_sent': patient_sent,
                'doctor_email_sent': doctor_sent,
                'success': patient_sent and doctor_sent
            }

        except Exception as e:
            logger.error(f"❌ Failed to send appointment confirmation emails: {str(e)}")
            return {
                'patient_email_sent': False,
                'doctor_email_sent': False,
                'success': False,
                'error': str(e)
            }

    def send_appointment_booking_notification(self, patient_email, appointment_details):
        """Send immediate notification to patient when appointment is booked (before doctor confirmation)"""
        try:
            subject = f"Appointment Request Submitted - {appointment_details.get('clinic_name', 'Medical Clinic')}"

            message = f"""
Dear {appointment_details.get('patient_name', 'Patient')},

Your appointment request has been successfully submitted and is now pending doctor confirmation.

📅 APPOINTMENT DETAILS:
• Patient: {appointment_details.get('patient_name', 'Patient')}
• Child: {appointment_details.get('child_name', 'N/A')}
• Doctor: {appointment_details.get('doctor_name', 'Doctor')}
• Date: {appointment_details.get('appointment_date', 'TBD')}
• Time: {appointment_details.get('appointment_time', 'TBD')}
• Type: {appointment_details.get('appointment_type', 'Checkup')}
• Purpose: {appointment_details.get('purpose', 'General consultation')}
• Clinic: {appointment_details.get('clinic_name', 'Medical Clinic')}

⏳ NEXT STEPS:
Your appointment is currently PENDING doctor confirmation. You will receive another email once the doctor confirms your appointment.

If you need to make any changes or have questions, please contact us immediately.

Thank you for choosing our medical services.

Best regards,
{appointment_details.get('clinic_name', 'Medical Clinic')} Team

---
This is an automated message. Please do not reply to this email.
            """

            return self.send_web3forms_email(patient_email, subject, message, "Medical Appointment System")

        except Exception as e:
            logger.error(f"❌ Failed to send appointment booking notification: {str(e)}")
            return False

    def send_doctor_new_appointment_notification(self, doctor_email, appointment_details):
        """Send notification to doctor about new appointment request"""
        try:
            subject = f"New Appointment Request - {appointment_details.get('patient_name', 'Patient')}"

            message = f"""
Dear Dr. {appointment_details.get('doctor_name', 'Doctor')},

You have received a new appointment request that requires your confirmation.

📅 APPOINTMENT DETAILS:
• Patient: {appointment_details.get('patient_name', 'Patient')}
• Child: {appointment_details.get('child_name', 'N/A')}
• Date: {appointment_details.get('appointment_date', 'TBD')}
• Time: {appointment_details.get('appointment_time', 'TBD')}
• Type: {appointment_details.get('appointment_type', 'Checkup')}
• Purpose: {appointment_details.get('purpose', 'General consultation')}
• Patient Email: {appointment_details.get('patient_email', 'N/A')}

⚡ ACTION REQUIRED:
Please log in to your doctor dashboard to review and confirm this appointment.

The patient has been notified that their appointment is pending your confirmation.

Login to Dashboard: http://127.0.0.1:5000/doctor

Best regards,
Medical Appointment System

---
This is an automated message. Please do not reply to this email.
            """

            return self.send_web3forms_email(doctor_email, subject, message, "Medical Appointment System")

        except Exception as e:
            logger.error(f"❌ Failed to send doctor notification: {str(e)}")
            return False

# Global email service instance
email_service = EmailService()
