from flask import Blueprint, render_template, redirect, url_for, session, jsonify
from app.data_manager import DataManager
from datetime import datetime

main_bp = Blueprint('main', __name__)

def login_required(f):
    """Simple login required decorator"""
    def decorated_function(*args, **kwargs):
        if not session.get('user_id'):
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@main_bp.route('/')
def index():
    """Main landing page - show home page with user context if logged in"""
    user_data = None
    if session.get('user_id'):
        try:
            user_data = DataManager.get_user_by_id(session['user_id'])
        except:
            pass
    return render_template('home.html', user=user_data)

@main_bp.route('/home')
def home():
    """Home page - show home page with user context if logged in"""
    user_data = None
    if session.get('user_id'):
        try:
            user_data = DataManager.get_user_by_id(session['user_id'])
        except:
            pass
    return render_template('home.html', user=user_data)

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """Unified dashboard - shows content based on user role without automatic redirect"""
    try:
        user_data = DataManager.get_user_by_id(session['user_id'])
        if user_data:
            # Pass user data to template so it can show appropriate content/navigation
            return render_template('dashboard.html', user=user_data)
        else:
            # If no user data, redirect to login
            return redirect('/auth/login')
    except Exception as e:
        # If there's any error, redirect to login
        print(f"Dashboard error: {e}")
        return redirect('/auth/login')

@main_bp.route('/pregnancy-care')
@login_required
def pregnancy_care():
    """Pregnancy care main page"""
    return redirect('/pregnancy')

@main_bp.route('/baby-care')
@login_required
def baby_care():
    """Baby care main page"""
    return redirect('/babycare')

@main_bp.route('/nutrition')
def nutrition():
    """Nutrition guide page"""
    return render_template('pregnancy/nutrition.html')

@main_bp.route('/vaccination')
def vaccination():
    """Vaccination schedule page"""
    return render_template('vaccination/vaccination.html')

@main_bp.route('/faq')
def faq():
    """FAQ page"""
    return render_template('faq/faq.html')

@main_bp.route('/government-schemes')
def government_schemes():
    """Government schemes page"""
    return render_template('schemes/schemes.html')

@main_bp.route('/exercises')
def exercises():
    """Exercise guide page"""
    return render_template('exercises/exercises.html')

@main_bp.route('/meditation')
def meditation():
    """Meditation guide page"""
    return render_template('meditation/meditation.html')

@main_bp.route('/test-appointments-api')
def test_appointments_api():
    """Test route for appointments API"""
    return jsonify({
        'success': True,
        'message': 'Appointments API test route is working!',
        'timestamp': datetime.now().isoformat()
    })
