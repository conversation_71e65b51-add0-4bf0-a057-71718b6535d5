
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Pregnancy Exercises - Mat<PERSON>al and Child Health Care</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: "Arial", sans-serif;
    }

    body {
      color: #333;
      background-color: #fff5f7;
    }

    .navbar {
      position: fixed;
      top: 0;
      width: 100%;
      background-color: rgba(255, 255, 255, 0.95);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1rem 2rem;
      z-index: 1000;
      box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    }

    .logo {
      font-size: 1.8rem;
      font-weight: bold;
      color: #d63384;
      display: flex;
      align-items: center;
    }

    .logo i {
      margin-right: 10px;
      font-size: 1.5rem;
    }

    .nav-links a {
      margin: 0 1rem;
      color: #333;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s;
      position: relative;
    }

    .nav-links a:hover {
      color: #d63384;
    }

    .nav-links a::after {
      content: "";
      position: absolute;
      width: 0;
      height: 2px;
      bottom: -5px;
      left: 0;
      background-color: #d63384;
      transition: width 0.3s;
    }

    .nav-links a:hover::after {
      width: 100%;
    }

    .book-now {
      background-color: #d63384;
      border: none;
      padding: 0.7rem 1.5rem;
      border-radius: 25px;
      cursor: pointer;
      font-weight: bold;
      color: white;
      transition: all 0.3s;
      box-shadow: 0 4px 8px rgba(214, 51, 132, 0.3);
    }

    .book-now:hover {
      background-color: #b52a6f;
      transform: translateY(-2px);
    }

    .page-header {
      background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
        url("https://images.unsplash.com/photo-1518611012118-696072aa579a?auto=format&fit=crop&w=1470&q=80")
          no-repeat center center/cover;
      height: 50vh;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      color: white;
      margin-top: 70px;
    }

    .page-header h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .page-header p {
      font-size: 1.2rem;
      max-width: 800px;
      margin: 0 auto;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }

    .content-section {
      padding: 5rem 2rem;
      background-color: white;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .section-title {
      text-align: center;
      margin-bottom: 2rem;
      color: #d63384;
      font-size: 2.5rem;
    }

    .exercise-tabs {
      display: flex;
      justify-content: center;
      margin-bottom: 2rem;
      flex-wrap: wrap;
    }

    .tab-btn {
      padding: 1rem 2rem;
      background-color: #f8f9fa;
      border: none;
      margin: 0 0.5rem 1rem;
      border-radius: 30px;
      cursor: pointer;
      font-weight: bold;
      color: #333;
      transition: all 0.3s;
    }

    .tab-btn.active {
      background-color: #d63384;
      color: white;
      box-shadow: 0 4px 8px rgba(214, 51, 132, 0.3);
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
      animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    .exercise-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
    }

    .exercise-card {
      background-color: #fff;
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s, box-shadow 0.3s;
    }

    .exercise-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }

    .card-img {
      height: 200px;
      overflow: hidden;
    }

    .card-img img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s;
    }

    .exercise-card:hover .card-img img {
      transform: scale(1.1);
    }

    .card-content {
      padding: 1.5rem;
    }

    .card-content h3 {
      color: #d63384;
      margin-bottom: 1rem;
    }

    .card-content p {
      color: #666;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .read-more {
      display: inline-block;
      color: #d63384;
      font-weight: bold;
      text-decoration: none;
      transition: color 0.3s;
    }

    .read-more:hover {
      color: #b52a6f;
    }

    .safety-tips {
      background-color: #f8f9fa;
      border-radius: 15px;
      padding: 2rem;
      margin-top: 3rem;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .safety-tips h3 {
      color: #d63384;
      margin-bottom: 1.5rem;
      font-size: 1.8rem;
      text-align: center;
    }

    .safety-tips ul {
      list-style-type: none;
      padding-left: 1rem;
    }

    .safety-tips li {
      margin-bottom: 1rem;
      position: relative;
      padding-left: 2rem;
      line-height: 1.6;
    }

    .safety-tips li:before {
      content: "\f058";
      font-family: "Font Awesome 5 Free";
      font-weight: 900;
      color: #d63384;
      position: absolute;
      left: 0;
      top: 2px;
    }

    /* Chatbot Button */
    .chatbot-btn {
      position: fixed;
      bottom: 30px;
      right: 30px;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: #d63384;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 15px rgba(214, 51, 132, 0.4);
      cursor: pointer;
      transition: all 0.3s;
      z-index: 999;
    }

    .chatbot-btn:hover {
      background-color: #b52a6f;
      transform: scale(1.1);
    }

    .chatbot-btn i {
      font-size: 1.5rem;
    }

    /* Footer Styles */
    .footer {
      background-color: #333;
      color: #fff;
      padding: 4rem 2rem 1rem;
    }

    .footer-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      max-width: 1200px;
      margin: 0 auto;
      gap: 2rem;
    }

    .footer-column {
      flex: 1 1 250px;
    }

    .footer-logo {
      font-size: 1.5rem;
      font-weight: bold;
      color: #d63384;
      display: flex;
      align-items: center;
      margin-bottom: 1rem;
    }

    .footer-logo i {
      margin-right: 10px;
    }

    .footer-column p {
      color: #ccc;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .social-icons {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .social-icons a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      color: #fff;
      transition: all 0.3s;
    }

    .social-icons a:hover {
      background-color: #d63384;
      transform: translateY(-3px);
    }

    .footer-column h3 {
      color: #d63384;
      margin-bottom: 1.2rem;
      font-size: 1.2rem;
    }

    .footer-links,
    .contact-info {
      list-style: none;
    }

    .footer-links li,
    .contact-info li {
      margin-bottom: 0.8rem;
    }

    .footer-links a {
      color: #ccc;
      text-decoration: none;
      transition: color 0.3s;
    }

    .footer-links a:hover {
      color: #d63384;
    }

    .contact-info li {
      display: flex;
      align-items: flex-start;
      color: #ccc;
    }

    .contact-info i {
      margin-right: 10px;
      color: #d63384;
    }

    .footer-bottom {
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      padding-top: 1.5rem;
      margin-top: 3rem;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 1rem;
      max-width: 1200px;
      margin-left: auto;
      margin-right: auto;
    }

    .footer-bottom p {
      color: #999;
    }

    .footer-bottom-links {
      display: flex;
      gap: 1.5rem;
    }

    .footer-bottom-links a {
      color: #999;
      text-decoration: none;
      transition: color 0.3s;
    }

    .footer-bottom-links a:hover {
      color: #d63384;
    }

    /* Mobile Menu */
    .mobile-menu-btn {
      display: none;
      background: none;
      border: none;
      color: #d63384;
      font-size: 1.5rem;
      cursor: pointer;
    }

    @media (max-width: 768px) {
      .page-header h1 {
        font-size: 2rem;
      }

      .nav-links {
        display: none;
        position: absolute;
        top: 70px;
        left: 0;
        width: 100%;
        background-color: white;
        flex-direction: column;
        padding: 1rem 0;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
      }

      .nav-links.active {
        display: flex;
      }

      .nav-links a {
        padding: 1rem;
        text-align: center;
      }

      .mobile-menu-btn {
        display: block;
      }
    }

    /* Empty State Styles */
    .empty-state {
      text-align: center;
      padding: 3rem 2rem;
      color: #666;
      grid-column: 1 / -1;
    }

    .empty-state h4 {
      margin-bottom: 0.5rem;
      color: #333;
    }

    .empty-state p {
      color: #999;
      font-style: italic;
    }

    /* Loading State */
    .loading {
      text-align: center;
      padding: 2rem;
      color: #666;
      grid-column: 1 / -1;
    }

    .loading i {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <header class="navbar">
    <div class="logo">
      <i class="fas fa-baby-carriage"></i>
      Maternal and Child Health Care
    </div>
    <nav class="nav-links" id="navLinks">
      <a href="/">Home</a>
      <a href="/pregnancy">Pregnancy Care</a>
      <a href="/pregnancy/exercise" class="active">Exercise Guides</a>
      <a href="/pregnancy/nutrition">Nutrition</a>
      <a href="/pregnancy/weight-tracker">Weight Tracker</a>
      <a href="/pregnancy/meditation">Meditation</a>
      <a href="/pregnancy/appointments">Appointments</a>
      <a href="/pregnancy/reports">Reports</a>
      <a href="/pregnancy/faq">FAQ</a>
      <a href="/babycare">Baby Care</a>
      <a href="/auth/logout">Logout</a>
    </nav>
    <button class="book-now">Get Started</button>
    <button class="mobile-menu-btn" id="mobileMenuBtn">
      <i class="fas fa-bars"></i>
    </button>
  </header>

  <section class="page-header">
    <div>
      <h1>Pregnancy Exercise Guides</h1>
      <p>Safe and effective exercises for each stage of your pregnancy journey</p>
    </div>
  </section>

  <section class="content-section">
    <div class="container">
      <h2 class="section-title">Exercise By Trimester</h2>

      <div class="exercise-tabs">
        <button class="tab-btn active" data-tab="first">
          First Trimester
        </button>
        <button class="tab-btn" data-tab="second">Second Trimester</button>
        <button class="tab-btn" data-tab="third">Third Trimester</button>
      </div>

      <div id="first" class="tab-content active">
        <div id="first-trimester-exercises" class="exercise-grid">
          <!-- Content will be loaded dynamically from admin-added data -->
          <div class="empty-state">
            <i class="fas fa-dumbbell" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
            <h4>No exercise guides available</h4>
            <p>Exercise guides for the first trimester will be added by our fitness experts.</p>
          </div>
        </div>
      </div>

      <div id="second" class="tab-content">
        <div id="second-trimester-exercises" class="exercise-grid">
          <!-- Content will be loaded dynamically from admin-added data -->
          <div class="empty-state">
            <i class="fas fa-dumbbell" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
            <h4>No exercise guides available</h4>
            <p>Exercise guides for the second trimester will be added by our fitness experts.</p>
          </div>
        </div>
      </div>

      <div id="third" class="tab-content">
        <div id="third-trimester-exercises" class="exercise-grid">
          <!-- Content will be loaded dynamically from admin-added data -->
          <div class="empty-state">
            <i class="fas fa-dumbbell" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
            <h4>No exercise guides available</h4>
            <p>Exercise guides for the third trimester will be added by our fitness experts.</p>
          </div>
        </div>
      </div>

      <div class="safety-tips">
        <h3>Exercise Safety Guidelines</h3>
        <ul>
          <li>Always consult with your healthcare provider before starting any exercise program during pregnancy.</li>
          <li>Stay well-hydrated and avoid exercising in hot, humid conditions to prevent overheating.</li>
          <li>Wear supportive footwear and comfortable clothing that allows for movement.</li>
          <li>Stop exercising and contact your doctor if you experience dizziness, shortness of breath, chest pain, headache, muscle weakness, calf pain or swelling, or fluid leaking from the vagina.</li>
          <li>Avoid exercises that require lying flat on your back after the first trimester.</li>
          <li>Avoid contact sports and activities with a high risk of falling or abdominal trauma.</li>
          <li>Listen to your body and don't push yourself to exhaustion. Pregnancy is not the time to train for athletic competition.</li>
        </ul>
      </div>
    </div>
  </section>

  <!-- Floating Chatbot Button -->
  <div class="chatbot-btn" onclick="openChatbot()">
    <i class="fas fa-comment-dots"></i>
  </div>

  <!-- Footer Styles -->
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-column">
        <div class="footer-logo">
          <i class="fas fa-baby-carriage"></i>
          Maternal and Child Health Care
        </div>
        <p>
          Your trusted companion for pregnancy and baby care, providing expert
          guidance and support every step of the way.
        </p>
        <div class="social-icons">
          <a href="#"><i class="fab fa-facebook-f"></i></a>
          <a href="#"><i class="fab fa-twitter"></i></a>
          <a href="#"><i class="fab fa-instagram"></i></a>
          <a href="#"><i class="fab fa-youtube"></i></a>
        </div>
      </div>

      <div class="footer-column">
        <h3>Quick Links</h3>
        <ul class="footer-links">
          <li><a href="/home.html">Home</a></li>
          <li><a href="/pages/Preg/pregcare.html">Pregnancy Care</a></li>
          <li><a href="/pages/baby/baby-care.html">Baby Care</a></li>
          <li><a href="/pages/doctor/dashboard.html">Consult Doctor</a></li>
        </ul>
      </div>

      <div class="footer-column">
        <h3>Services</h3>
        <ul class="footer-links">
          <li><a href="/pages/Preg/nutrition.html">Nutrition Plans</a></li>
          <li><a href="/pages/Preg/exercise.html">Exercise Guides</a></li>
          <li><a href="/pages/Preg/schemes.html">Government Schemes</a></li>
          <li><a href="/pages/contact.html">Contact Us</a></li>
        </ul>
      </div>

      <div class="footer-column">
        <h3>Contact Info</h3>
        <ul class="contact-info">
          <li><i class="fas fa-phone"></i> +****************</li>
          <li><i class="fas fa-envelope"></i> <EMAIL></li>
          <li><i class="fas fa-map-marker-alt"></i> 123 Health Street, Care City</li>
        </ul>
      </div>
    </div>

    <div class="footer-bottom">
      <p>&copy; 2024 Maternal and Child Health Care. All rights reserved.</p>
      <div class="footer-bottom-links">
        <a href="#">Privacy Policy</a>
        <a href="#">Terms of Service</a>
        <a href="#">Cookie Policy</a>
      </div>
    </div>
  </footer>

  <script>
    // Tab functionality
    document.addEventListener('DOMContentLoaded', function() {
      const tabBtns = document.querySelectorAll('.tab-btn');
      const tabContents = document.querySelectorAll('.tab-content');

      tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const targetTab = this.getAttribute('data-tab');

          // Remove active class from all buttons and contents
          tabBtns.forEach(b => b.classList.remove('active'));
          tabContents.forEach(content => content.classList.remove('active'));

          // Add active class to clicked button and corresponding content
          this.classList.add('active');
          document.getElementById(targetTab).classList.add('active');
        });
      });

      // Mobile menu functionality
      const mobileMenuBtn = document.getElementById('mobileMenuBtn');
      const navLinks = document.getElementById('navLinks');

      if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.addEventListener('click', function() {
          navLinks.classList.toggle('active');
        });
      }

      // Get Started button functionality
      const getStartedBtn = document.querySelector('.book-now');
      if (getStartedBtn) {
        getStartedBtn.addEventListener('click', function() {
          window.location.href = '/signup';
        });
      }

      // Load exercise data
      loadExerciseData();
    });

    // Load exercise data from admin-managed database
    async function loadExerciseData() {
      try {
        // Show loading state
        showExerciseLoadingState();

        // Fetch exercise data from backend
        const response = await fetch('/api/exercises-data');

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            renderExerciseData(result.data);
            console.log(`✅ Loaded ${result.count} exercises from admin`);
          } else {
            console.log('No exercise data available yet - admin needs to add content');
            renderExerciseData([]); // Pass empty array to show empty state
          }
        } else {
          console.log('No exercise data available yet - admin needs to add content');
          renderExerciseData([]); // Pass empty array to show empty state
        }
      } catch (error) {
        console.error('Error loading exercise data:', error);
        // Keep empty state visible
      }
    }

    function showExerciseLoadingState() {
      const trimesters = ['first', 'second', 'third'];
      trimesters.forEach(trimester => {
        const container = document.getElementById(`${trimester}-trimester-exercises`);
        if (container) {
          container.innerHTML = `
            <div class="loading">
              <i class="fas fa-spinner"></i>
              <p>Loading exercise guides...</p>
            </div>
          `;
        }
      });
    }

    function renderExerciseData(data) {
      const trimesters = ['first', 'second', 'third'];

      trimesters.forEach(trimester => {
        const container = document.getElementById(`${trimester}-trimester-exercises`);
        if (!container) return;

        // Ensure data is an array before filtering
        const dataArray = Array.isArray(data) ? data : [];

        // Filter data for this trimester (include 'all' trimester items)
        const trimesterData = dataArray.filter(item =>
          item.trimester === trimester || item.trimester === 'all'
        );

        if (trimesterData.length === 0) {
          // Show empty state if no data
          container.innerHTML = `
            <div class="empty-state">
              <i class="fas fa-dumbbell" style="font-size: 3rem; color: #ddd; margin-bottom: 1rem;"></i>
              <h4>No exercise guides available</h4>
              <p>Exercise guides for the ${trimester} trimester will be added by our fitness experts.</p>
            </div>
          `;
          return;
        }

        // Render exercise cards
        let html = '';
        trimesterData.forEach(exercise => {
          html += `
            <div class="exercise-card">
              <div class="card-img">
                <img src="https://images.unsplash.com/photo-1518611012118-696072aa579a?auto=format&fit=crop&w=1470&q=80" alt="${exercise.title}" />
              </div>
              <div class="card-content">
                <h3>${exercise.title}</h3>
                <p>${exercise.description}</p>
                ${exercise.duration ? `<p><strong>Duration:</strong> ${exercise.duration}</p>` : ''}
                ${exercise.difficulty ? `<p><strong>Difficulty:</strong> ${exercise.difficulty}</p>` : ''}
                <a href="#" class="read-more">View exercise guide <i class="fas fa-arrow-right"></i></a>
              </div>
            </div>
          `;
        });

        container.innerHTML = html;
      });
    }

    // Chatbot functionality
    function openChatbot() {
      // Redirect to chat page or open chat modal
      window.location.href = '/pages/chat.html';
    }
  </script>
</body>
</html>
