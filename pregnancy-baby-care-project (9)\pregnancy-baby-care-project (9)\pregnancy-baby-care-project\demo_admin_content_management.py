#!/usr/bin/env python3
"""
Demonstration of Admin Content Management functionality
Shows how admins can update content and users can see the changes
"""

import sys
import os
import sqlite3

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'pregnancy-baby-care-project'))

def demo_admin_content_management():
    """Demonstrate admin content management functionality"""
    
    print("🎯 ADMIN CONTENT MANAGEMENT DEMONSTRATION")
    print("=" * 70)
    
    # Create app context for database operations
    from app import create_app
    app = create_app()
    
    with app.app_context():
        from app.data_manager import DataManager
        from datetime import datetime
        
        print("📋 Current Content Status:")
        print("-" * 40)
        
        # Check current content
        nutrition_items = DataManager.get_all_nutrition_content()
        faq_items = DataManager.get_all_faqs()
        vaccination_schedules = DataManager.get_all_vaccination_schedules()
        schemes = DataManager.get_all_schemes()
        
        print(f"   📊 Nutrition Content: {len(nutrition_items)} items")
        print(f"   📊 FAQ Items: {len(faq_items)} items")
        print(f"   📊 Vaccination Schedules: {len(vaccination_schedules)} items")
        print(f"   📊 Government Schemes: {len(schemes)} items")
        
        print("\n🏗️  ADMIN ADDING NEW CONTENT:")
        print("-" * 40)
        
        # Admin adds nutrition content
        print("1. Admin adding nutrition content...")
        nutrition_id = DataManager.create_nutrition_content(
            title="Healthy Pregnancy Diet",
            description="Essential nutrition guidelines for expecting mothers",
            category="pregnancy",
            trimester="all",
            foods=["leafy greens", "lean proteins", "whole grains", "dairy products"],
            tips="Eat small frequent meals and stay hydrated"
        )
        print(f"   ✅ Created nutrition content with ID: {nutrition_id}")
        
        # Admin adds FAQ
        print("2. Admin adding FAQ...")
        faq_id = DataManager.create_faq(
            question="How much weight should I gain during pregnancy?",
            answer="Weight gain depends on your pre-pregnancy BMI. Generally, 25-35 pounds for normal weight women.",
            category="pregnancy"
        )
        print(f"   ✅ Created FAQ with ID: {faq_id}")
        
        # Admin adds vaccination schedule
        print("3. Admin adding vaccination schedule...")
        vaccination_id = DataManager.create_vaccination_schedule(
            vaccine_name="Hepatitis B",
            age_months=0,
            description="First dose given at birth",
            side_effects="Mild soreness at injection site",
            precautions="Inform doctor of any allergies"
        )
        print(f"   ✅ Created vaccination schedule with ID: {vaccination_id}")
        
        # Admin adds government scheme
        print("4. Admin adding government scheme...")
        scheme_id = DataManager.create_scheme(
            name="Janani Suraksha Yojana",
            description="Cash assistance for institutional delivery",
            eligibility="Pregnant women below poverty line",
            benefits="Cash incentive for hospital delivery",
            how_to_apply="Contact local ASHA worker or health center"
        )
        print(f"   ✅ Created government scheme with ID: {scheme_id}")
        
        print("\n📊 UPDATED CONTENT STATUS:")
        print("-" * 40)
        
        # Check updated content
        updated_nutrition = DataManager.get_all_nutrition_content()
        updated_faqs = DataManager.get_all_faqs()
        updated_vaccinations = DataManager.get_all_vaccination_schedules()
        updated_schemes = DataManager.get_all_schemes()
        
        print(f"   📈 Nutrition Content: {len(updated_nutrition)} items (+{len(updated_nutrition) - len(nutrition_items)})")
        print(f"   📈 FAQ Items: {len(updated_faqs)} items (+{len(updated_faqs) - len(faq_items)})")
        print(f"   📈 Vaccination Schedules: {len(updated_vaccinations)} items (+{len(updated_vaccinations) - len(vaccination_schedules)})")
        print(f"   📈 Government Schemes: {len(updated_schemes)} items (+{len(updated_schemes) - len(schemes)})")
        
        print("\n👥 USERS CAN NOW SEE THE CONTENT:")
        print("-" * 40)
        
        # Show what users can see
        if updated_nutrition:
            item = updated_nutrition[-1]  # Latest item
            print(f"   🍎 Latest Nutrition: {item['title']}")
            print(f"      📝 {item['description']}")
        
        if updated_faqs:
            item = updated_faqs[-1]  # Latest item
            print(f"   ❓ Latest FAQ: {item['question']}")
            print(f"      💬 {item['answer'][:100]}...")
        
        if updated_vaccinations:
            item = updated_vaccinations[-1]  # Latest item
            print(f"   💉 Latest Vaccination: {item['vaccine_name']} at {item['age_months']} months")
            print(f"      📝 {item['description']}")
        
        if updated_schemes:
            item = updated_schemes[-1]  # Latest item
            print(f"   🏛️  Latest Scheme: {item['name']}")
            print(f"      📝 {item['description']}")
        
        print("\n" + "=" * 70)
        print("🎉 ADMIN CONTENT MANAGEMENT DEMONSTRATION COMPLETE!")
        print("\n📋 Key Features Demonstrated:")
        print("   ✅ Admin can create nutrition content")
        print("   ✅ Admin can create FAQ items")
        print("   ✅ Admin can create vaccination schedules")
        print("   ✅ Admin can create government schemes")
        print("   ✅ All content is stored in the database")
        print("   ✅ Users can access the updated content")
        print("   ✅ Changes are immediately available to users")
        
        print("\n🌐 Admin Interface Available At:")
        print("   📱 http://127.0.0.1:5000/admin/manage-content")
        print("   🔑 Login: <EMAIL> / admin123")

if __name__ == "__main__":
    demo_admin_content_management()
