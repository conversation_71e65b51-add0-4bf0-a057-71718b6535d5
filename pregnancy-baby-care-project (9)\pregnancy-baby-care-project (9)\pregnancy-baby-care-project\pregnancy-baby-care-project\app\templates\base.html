<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Maternal and Child Health Monitoring System{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary: #ff6b9d;
            --primary-dark: #e91e63;
            --secondary: #4ecdc4;
            --secondary-dark: #26a69a;
            --accent: #ffd93d;
            --accent-dark: #ffb300;
            --light: #f8fafc;
            --dark: #2d3748;
            --gray: #718096;
            --light-gray: #e2e8f0;
            --baby-blue: #87ceeb;
            --baby-pink: #ffb6c1;
            --baby-green: #98fb98;
            --baby-yellow: #ffffe0;
            --success: #48bb78;
            --warning: #ed8936;
            --danger: #f56565;
            --info: #4299e1;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --shadow: 0 4px 20px rgba(0,0,0,0.1);
            --shadow-hover: 0 8px 30px rgba(0,0,0,0.15);
            --border-radius: 20px;
            --gradient-primary: linear-gradient(135deg, #ff6b9d, #ffd93d);
            --gradient-secondary: linear-gradient(135deg, #4ecdc4, #87ceeb);
            --gradient-accent: linear-gradient(135deg, #98fb98, #4ecdc4);
            --gradient-admin: linear-gradient(135deg, #667eea, #764ba2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }

        /* Navigation Styles */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 1000;
            padding: 1rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
            list-style: none;
        }

        .nav-links a {
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: 0.5rem 1rem;
            border-radius: 10px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: var(--primary);
            color: white;
        }

        .nav-user {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--gradient-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .dropdown {
            position: relative;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background: white;
            min-width: 200px;
            box-shadow: var(--shadow);
            border-radius: 10px;
            padding: 0.5rem 0;
            z-index: 1001;
        }

        .dropdown:hover .dropdown-content {
            display: block;
        }

        .dropdown-content a {
            display: block;
            padding: 0.75rem 1rem;
            color: var(--dark);
            text-decoration: none;
            transition: var(--transition);
        }

        .dropdown-content a:hover {
            background: var(--light);
        }

        /* Mobile Navigation */
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--dark);
            cursor: pointer;
        }

        /* Main Content */
        .main-content {
            min-height: calc(100vh - 80px);
            padding: 2rem 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        /* Footer */
        .footer {
            background: var(--dark);
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section ul li a {
            color: #cbd5e0;
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-section ul li a:hover {
            color: var(--primary);
        }

        .footer-bottom {
            border-top: 1px solid #4a5568;
            margin-top: 2rem;
            padding-top: 1rem;
            text-align: center;
            color: #cbd5e0;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                flex-direction: column;
                padding: 1rem;
                box-shadow: var(--shadow);
            }

            .nav-links.active {
                display: flex;
            }

            .mobile-menu-toggle {
                display: block;
            }

            .container {
                padding: 0 1rem;
            }

            .footer-content {
                grid-template-columns: 1fr;
                text-align: center;
            }
        }

        /* Alert Styles */
        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* Loading Spinner */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--light-gray);
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--light);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gray);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--dark);
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- Logo -->
            <a href="/" class="nav-logo">
                <i class="fas fa-baby"></i>
                <span>{% block nav_title %}Health Monitor{% endblock %}</span>
            </a>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                <i class="fas fa-bars"></i>
            </button>

            <!-- Navigation Links -->
            <ul class="nav-links" id="nav-links">
                {% block nav_links %}
                <li><a href="/"><i class="fas fa-home"></i> Home</a></li>
                <li><a href="/pregnancy"><i class="fas fa-heart"></i> Pregnancy</a></li>
                <li><a href="/baby-care"><i class="fas fa-baby"></i> Baby Care</a></li>
                {% endblock %}
            </ul>

            <!-- User Section -->
            <div class="nav-user">
                {% if session.user_id %}
                    <div class="dropdown">
                        <div class="user-avatar">
                            {{ session.user_data.name[0].upper() if session.user_data and session.user_data.name else 'U' }}
                        </div>
                        <div class="dropdown-content">
                            <a href="/profile"><i class="fas fa-user"></i> Profile</a>
                            {% if session.user_data and session.user_data.role == 'admin' %}
                                <a href="/admin"><i class="fas fa-crown"></i> Admin Panel</a>
                            {% endif %}
                            <a href="/settings"><i class="fas fa-cog"></i> Settings</a>
                            <a href="/auth/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                {% else %}
                    <a href="/auth/login" class="nav-links">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container" style="padding-top: 1rem;">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'error' if category == 'error' else category }}">
                        <i class="fas fa-{{ 'exclamation-circle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="main-content">
        {% block content %}
        <div class="container">
            <h1>Welcome to Maternal and Child Health Monitoring System</h1>
            <p>Your comprehensive platform for pregnancy and baby care management.</p>
        </div>
        {% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3><i class="fas fa-baby"></i> Health Monitor</h3>
                <p>Comprehensive maternal and child health monitoring platform designed to support families throughout their journey.</p>
                <div style="margin-top: 1rem;">
                    <a href="#" style="color: var(--primary); margin-right: 1rem;"><i class="fab fa-facebook"></i></a>
                    <a href="#" style="color: var(--primary); margin-right: 1rem;"><i class="fab fa-twitter"></i></a>
                    <a href="#" style="color: var(--primary); margin-right: 1rem;"><i class="fab fa-instagram"></i></a>
                    <a href="#" style="color: var(--primary);"><i class="fab fa-linkedin"></i></a>
                </div>
            </div>

            <div class="footer-section">
                <h3>Services</h3>
                <ul>
                    <li><a href="/pregnancy">Pregnancy Care</a></li>
                    <li><a href="/baby-care">Baby Care</a></li>
                    <li><a href="/nutrition">Nutrition Guidance</a></li>
                    <li><a href="/exercise">Exercise Programs</a></li>
                    <li><a href="/meditation">Meditation</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>Resources</h3>
                <ul>
                    <li><a href="/faq">FAQ</a></li>
                    <li><a href="/schemes">Government Schemes</a></li>
                    <li><a href="/reports">Health Reports</a></li>
                    <li><a href="/appointments">Appointments</a></li>
                    <li><a href="/chatbot">AI Assistant</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>Contact</h3>
                <ul>
                    <li><i class="fas fa-phone"></i> +****************</li>
                    <li><i class="fas fa-envelope"></i> <EMAIL></li>
                    <li><i class="fas fa-map-marker-alt"></i> 123 Health St, Medical City</li>
                    <li><a href="/contact">Contact Form</a></li>
                </ul>
            </div>
        </div>

        <div class="footer-bottom">
            <p>&copy; 2024 Maternal and Child Health Monitoring System. All rights reserved.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const navLinks = document.getElementById('nav-links');
            navLinks.classList.toggle('active');
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const navLinks = document.getElementById('nav-links');
            const toggle = document.querySelector('.mobile-menu-toggle');

            if (!navLinks.contains(event.target) && !toggle.contains(event.target)) {
                navLinks.classList.remove('active');
            }
        });

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }, 5000);
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>