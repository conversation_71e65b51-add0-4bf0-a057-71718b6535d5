<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maternal and Child Health Monitoring - Your Trusted Maternal & Child Health Assistant</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/config.js"></script>
    <script src="js/api-integration.js"></script>
    <script src="js/unified-auth.js"></script>
    <script src="js/real-time-content.js"></script>
    <script src="js/common-header.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary: #e91e63;
            --primary-dark: #c2185b;
            --secondary: #4caf50;
            --secondary-dark: #388e3c;
            --accent: #2196f3;
            --light: #f8fafc;
            --dark: #2d3748;
            --gray: #718096;
            --light-gray: #e2e8f0;
            --transition: all 0.3s ease;
            --shadow: 0 4px 20px rgba(0,0,0,0.1);
            --border-radius: 16px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fafafa;
            overflow-x: hidden;
        }
        
        /* Header Styles */
        .header {
            background: white;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 0.5rem 0;
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 70px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }
        
        .logo-icon {
            background: linear-gradient(135deg, var(--primary), #ff8a80);
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1.8rem;
            align-items: center;
        }
        
        .nav-menu a {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            transition: var(--transition);
            position: relative;
            padding: 0.5rem 0;
        }
        
        .nav-menu a:hover,
        .nav-menu a.active {
            color: var(--primary);
        }
        
        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 3px;
            background: var(--primary);
            transition: var(--transition);
            border-radius: 10px;
        }
        
        .nav-menu a:hover::after,
        .nav-menu a.active::after {
            width: 100%;
        }
        
        .nav-actions {
            display: flex;
            gap: 1.2rem;
            align-items: center;
        }
        
        .btn-login {
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            padding: 0.5rem 1.2rem;
            border-radius: 50px;
        }
        
        .btn-login:hover {
            background: rgba(233, 30, 99, 0.1);
        }

        /* Quick Login Section */
        .quick-login {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            max-width: 400px;
            width: 90%;
        }

        .login-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            text-align: center;
        }

        .login-card h3 {
            color: var(--primary);
            margin-bottom: 10px;
            font-size: 24px;
        }

        .login-card p {
            color: #666;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .btn-login-submit {
            width: 100%;
            background: var(--primary);
            color: white;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .btn-login-submit:hover {
            background: #c2185b;
            transform: translateY(-2px);
        }

        .login-help {
            border-top: 1px solid #eee;
            padding-top: 20px;
            font-size: 14px;
        }

        .login-help p {
            margin-bottom: 15px;
            color: #888;
        }

        .btn-cancel {
            background: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-cancel:hover {
            background: #5a6268;
        }
        
        .btn-signup {
            background: var(--primary);
            color: white;
            padding: 0.7rem 1.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
        }
        
        .btn-signup:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        /* User Navigation Styles */
        .user-nav {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            margin-right: 1rem;
        }

        .user-welcome {
            font-size: 0.9rem;
            color: #333;
            font-weight: 500;
        }

        .user-role {
            font-size: 0.75rem;
            color: #666;
            text-transform: uppercase;
            background: #f0f0f0;
            padding: 2px 8px;
            border-radius: 10px;
            margin-top: 2px;
        }

        .user-role.admin {
            background: #ff5722;
            color: white;
        }

        .user-role.doctor {
            background: #2196f3;
            color: white;
        }

        .user-role.user {
            background: #4caf50;
            color: white;
        }

        .user-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }



        .btn-logout {
            background: #f44336;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 25px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-logout:hover {
            background: #d32f2f;
            transform: translateY(-2px);
        }
        
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--dark);
            cursor: pointer;
        }
        
        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, rgba(107, 70, 193, 0.85), rgba(233, 30, 99, 0.85)), 
                        url('https://images.unsplash.com/photo-1516726817505-f5ed825624d8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            color: white;
            padding: 180px 0 100px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
            opacity: 0.2;
        }
        
        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            position: relative;
            z-index: 1;
        }
        
        .hero-content {
            max-width: 750px;
            background: rgba(255,255,255,0.08);
            backdrop-filter: blur(12px);
            border-radius: var(--border-radius);
            padding: 3.5rem;
            border: 1px solid rgba(255,255,255,0.15);
            box-shadow: var(--shadow);
            animation: fadeInUp 1s ease-out;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.2rem;
            line-height: 1.2;
            text-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .hero .subtitle {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1.8rem;
            color: #ffc1cc;
            text-shadow: 0 2px 5px rgba(0,0,0,0.15);
        }
        
        .hero p {
            font-size: 1.25rem;
            margin-bottom: 2.5rem;
            opacity: 0.92;
            line-height: 1.7;
            max-width: 600px;
        }
        
        .hero-buttons {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;
        }
        
        .btn-pregnancy {
            background: var(--primary);
            color: white;
            padding: 1.2rem 2.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            transition: var(--transition);
            box-shadow: 0 5px 20px rgba(233, 30, 99, 0.35);
            animation: pulse 2s infinite;
        }
        
        .btn-pregnancy:hover {
            background: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.5);
        }
        
        .btn-baby {
            background: rgba(255,255,255,0.15);
            color: white;
            padding: 1.2rem 2.5rem;
            border: 2px solid var(--secondary);
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            transition: var(--transition);
            backdrop-filter: blur(5px);
        }
        
        .btn-baby:hover {
            background: var(--secondary);
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(76, 175, 80, 0.3);
        }
        
        .hero-image {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 40%;
            max-width: 500px;
            z-index: 0;
            opacity: 0.95;
            transform: translateY(30px);
            animation: float 6s ease-in-out infinite;
        }
        
        /* Features Section */
        .features {
            padding: 100px 0;
            background: var(--light);
            position: relative;
        }
        
        .features::before {
            content: '';
            position: absolute;
            top: -50px;
            left: 0;
            width: 100%;
            height: 100px;
            background: url('data:image/svg+xml,&lt;svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"&gt;&lt;path fill="%23f8fafc" fill-opacity="1" d="M0,224L80,192C160,160,320,96,480,90.7C640,85,800,139,960,165.3C1120,192,1280,192,1360,192L1440,192L1440,320L1360,320C1280,320,1120,320,960,320C800,320,640,320,480,320C320,320,160,320,80,320L0,320Z"&gt;&lt;/path&gt;&lt;/svg&gt;');
            background-size: cover;
            background-position: center;
        }
        
        .features-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: center;
            position: relative;
            z-index: 1;
        }
        
        .section-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary);
            margin-bottom: 1rem;
            position: relative;
            display: inline-block;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 70px;
            height: 4px;
            background: var(--primary);
            border-radius: 2px;
        }
        
        .section-subtitle {
            font-size: 1.2rem;
            color: var(--gray);
            max-width: 700px;
            margin: 0 auto 4rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2.5rem;
            margin-bottom: 2rem;
        }
        
        .feature-card {
            background: white;
            padding: 2.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            border-bottom: 3px solid var(--primary);
            text-align: left;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.5s ease;
        }
        
        .feature-card:hover::before {
            transform: scaleX(1);
        }
        
        .feature-icon {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            color: var(--primary);
            display: inline-block;
            background: rgba(233, 30, 99, 0.1);
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--dark);
        }
        
        .feature-card p {
            color: var(--gray);
            line-height: 1.7;
        }
        
        /* Testimonials */
        .testimonials {
            padding: 100px 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4e7f1 100%);
            position: relative;
        }
        
        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .testimonial-card {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            position: relative;
        }
        
        .testimonial-card::before {
            content: '"';
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 5rem;
            color: rgba(233, 30, 99, 0.1);
            font-family: Georgia, serif;
            line-height: 1;
        }
        
        .testimonial-text {
            font-style: italic;
            color: var(--dark);
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 1;
        }
        
        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .author-info h4 {
            font-weight: 600;
            color: var(--dark);
        }
        
        .author-info p {
            color: var(--gray);
            font-size: 0.9rem;
        }
        
        /* Footer */
        .footer {
            background: var(--dark);
            color: white;
            padding: 4rem 0 2rem;
            position: relative;
        }
        
        .footer::before {
            content: '';
            position: absolute;
            top: -50px;
            left: 0;
            width: 100%;
            height: 100px;
            background: url('data:image/svg+xml,&lt;svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"&gt;&lt;path fill="%232d3748" fill-opacity="1" d="M0,224L80,192C160,160,320,96,480,90.7C640,85,800,139,960,165.3C1120,192,1280,192,1360,192L1440,192L1440,320L1360,320C1280,320,1120,320,960,320C800,320,640,320,480,320C320,320,160,320,80,320L0,320Z"&gt;&lt;/path&gt;&lt;/svg&gt;');
            background-size: cover;
            background-position: center;
        }
        
        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 3rem;
            margin-bottom: 2rem;
        }
        
        .footer-brand {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .footer-logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.8rem;
            font-weight: 700;
            color: white;
        }
        
        .footer-logo .logo-icon {
            width: 50px;
            height: 50px;
            font-size: 2rem;
        }
        
        .footer-desc {
            color: #cbd5e0;
            line-height: 1.7;
        }
        
        .footer-section h4 {
            margin-bottom: 1.5rem;
            color: white;
            font-size: 1.3rem;
            position: relative;
            padding-bottom: 0.5rem;
        }
        
        .footer-section h4::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--primary);
            border-radius: 2px;
        }
        
        .footer-section ul {
            list-style: none;
        }
        
        .footer-section ul li {
            margin-bottom: 0.8rem;
        }
        
        .footer-section ul li a {
            color: #cbd5e0;
            text-decoration: none;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .footer-section ul li a:hover {
            color: var(--primary);
            transform: translateX(5px);
        }
        
        .footer-section ul li a i {
            font-size: 0.9rem;
        }
        
        .footer-bottom {
            border-top: 1px solid #4a5568;
            padding-top: 2rem;
            text-align: center;
            color: #a0aec0;
            font-size: 0.9rem;
        }
        
        .social-links {
            display: flex;
            gap: 1.2rem;
            margin-top: 1.5rem;
        }
        
        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.1);
            color: white;
            border-radius: 50%;
            font-size: 1.2rem;
            transition: var(--transition);
        }
        
        .social-links a:hover {
            background: var(--primary);
            transform: translateY(-3px);
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }
        
        .footer-links a {
            color: #a0aec0;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .footer-links a:hover {
            color: var(--primary);
        }
        
        /* Newsletter */
        .newsletter {
            background: rgba(255,255,255,0.05);
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 1.5rem;
        }
        
        .newsletter h5 {
            margin-bottom: 1rem;
            color: white;
        }
        
        .newsletter-form {
            display: flex;
            gap: 0.5rem;
        }
        
        .newsletter-input {
            flex: 1;
            padding: 0.8rem 1.2rem;
            border: none;
            border-radius: 50px;
            background: rgba(255,255,255,0.1);
            color: white;
            outline: none;
        }
        
        .newsletter-input::placeholder {
            color: #cbd5e0;
        }
        
        .newsletter-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 50px;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .newsletter-btn:hover {
            background: var(--primary-dark);
        }
        
        /* Responsive */
        @media (max-width: 992px) {
            .hero h1 {
                font-size: 3rem;
            }
            
            .hero .subtitle {
                font-size: 1.8rem;
            }
        }
        
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .mobile-menu-btn {
                display: block;
            }
            
            .hero {
                padding: 150px 0 80px;
                text-align: center;
            }
            
            .hero-content {
                padding: 2rem;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero .subtitle {
                font-size: 1.5rem;
            }
            
            .hero p {
                font-size: 1.1rem;
            }
            
            .hero-buttons {
                justify-content: center;
            }
            
            .section-title {
                font-size: 2rem;
            }
        }
        
        @media (max-width: 576px) {
            .nav-container {
                padding: 0 1.5rem;
            }
            
            .hero-content {
                padding: 1.5rem;
            }
            
            .hero h1 {
                font-size: 2.2rem;
            }
            
            .hero-buttons {
                flex-direction: column;
            }
            
            .feature-card {
                padding: 1.8rem;
            }
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(233, 30, 99, 0.4);
            }
            70% {
                box-shadow: 0 0 0 15px rgba(233, 30, 99, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(233, 30, 99, 0);
            }
        }
        
        @keyframes float {
            0% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
            100% {
                transform: translateY(0px);
            }
        }
        
        /* Back to top button */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: var(--primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            cursor: pointer;
            transition: var(--transition);
            opacity: 0;
            visibility: hidden;
            z-index: 999;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }
        
        .back-to-top:hover {
            background: var(--primary-dark);
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <!-- Common Header -->
    <div id="common-header"></div>

    <!-- Original header content for fallback -->
    <header class="header" style="display: none;">
        <div class="nav-container">
            <a href="#" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-baby"></i>
                </div>
                <span>Maternal and Child Health Monitoring</span>
            </a>
            <nav class="nav-menu">
                <a href="#" class="active">Home</a>
                <a href="pages/Preg/pregcare.html" onclick="navigateToPage(event, 'pages/Preg/pregcare.html')">Pregnancy Care</a>
                <a href="pages/baby/baby-care.html" onclick="navigateToPage(event, 'pages/baby/baby-care.html')">Baby Care</a>
                <a href="/consultation" onclick="navigateToPage(event, '/consultation')">Web3 Consultation</a>
                <a href="#features">Features</a>
            </nav>
            <div class="nav-actions">
                <!-- Guest Navigation -->
                <div id="guest-nav" class="guest-nav">
                    <a href="#" class="btn-login" onclick="showQuickLogin(); return false;">
                        <i class="fas fa-lock"></i> Login
                    </a>
                    <a href="/signup" class="btn-signup">
                        <i class="fas fa-user-plus"></i> Sign Up
                    </a>
                </div>

                <!-- Authenticated User Navigation -->
                <div id="user-nav" class="user-nav" style="display: none;">
                    <div class="user-info">
                        <span class="user-welcome">Welcome, <span id="user-name">User</span></span>
                        <span class="user-role" id="user-role">user</span>
                    </div>
                    <div class="user-actions">
                        <button id="logout-btn" class="btn-logout">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </div>
                </div>
            </div>
            <button class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-container">
            <div class="hero-content">
                <h1>Your Trusted Maternal & Child Health Monitoring System</h1>
                <div class="subtitle">From Pregnancy to Parenthood, We're With You</div>
                <p>Maternal and Child Health Monitoring is your complete guide for a safe pregnancy and healthy baby. Get expert tips, week-by-week tracking, doctor consultations, diet plans, and baby growth insights — all in one place.</p>
                <div class="hero-buttons">
                    <a href="pages/Preg/pregcare.html" class="btn-pregnancy" onclick="navigateToPage(event, 'pages/Preg/pregcare.html')">
                        <i class="fas fa-baby-carriage"></i> Pregnancy Care
                    </a>
                    <a href="pages/baby/baby-care.html" class="btn-baby" onclick="navigateToPage(event, 'pages/baby/baby-care.html')">
                        <i class="fas fa-baby"></i> Explore Baby Care
                    </a>
                </div>
            </div>
        </div>
        <img src="https://images.unsplash.com/photo-1534367507877-0edd93bd013b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Happy mother with baby" class="hero-image">
    </section>

    <!-- Quick Login Section -->
    <section class="quick-login" id="quick-login" style="display: none;">
        <div class="login-container">
            <div class="login-card">
                <h3><i class="fas fa-lock"></i> Quick Login</h3>
                <p>Login once to access both Pregnancy Care and Baby Care sections</p>
                <form id="quick-login-form" onsubmit="handleQuickLogin(event)">
                    <div class="form-group">
                        <input type="email" id="quick-email" name="email" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="quick-password" name="password" placeholder="Password" required>
                    </div>
                    <button type="submit" class="btn-login-submit">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                </form>
                <div class="login-help">
                    <p>Test credentials: <strong><EMAIL></strong> / <strong>user123</strong></p>
                    <button onclick="hideQuickLogin()" class="btn-cancel">Cancel</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="features-container">
            <h2 class="section-title">How We Support You</h2>
            <p class="section-subtitle">Comprehensive tools and resources to guide you through your pregnancy journey and beyond</p>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h3>Pregnancy Tracker</h3>
                    <p>Personalized week-by-week pregnancy tracking with developmental milestones, health tips, and personalized checklists.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <h3>Expert Consultations</h3>
                    <p>Connect with certified obstetricians and pediatricians for professional advice anytime, anywhere via video or chat.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <h3>Nutrition Plans</h3>
                    <p>Customized diet plans for each trimester and postpartum period with easy-to-follow recipes and shopping lists.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-book-medical"></i>
                    </div>
                    <h3>Health Records</h3>
                    <p>Securely store and access all your medical records, appointment schedules, and vaccination trackers in one place.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Community Support</h3>
                    <p>Join a supportive community of parents to share experiences, ask questions, and find encouragement.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Baby Development</h3>
                    <p>Track your baby's growth and development with milestone alerts, activity suggestions, and health monitoring.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials" id="testimonials">
        <div class="features-container">
            <h2 class="section-title">What Parents Say</h2>
            <p class="section-subtitle">Real stories from our community of happy parents</p>
            
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <p class="testimonial-text">This app has been a lifesaver during my pregnancy! The week-by-week updates helped me understand what to expect, and the nutrition plans kept me healthy throughout.</p>
                    <div class="testimonial-author">
                        <div class="author-avatar">SJ</div>
                        <div class="author-info">
                            <h4>Sarah Johnson</h4>
                            <p>First-time mom</p>
                        </div>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <p class="testimonial-text">The doctor consultation feature saved us multiple trips to the clinic. Being able to quickly chat with a pediatrician at 2 AM when our baby had a fever was invaluable.</p>
                    <div class="testimonial-author">
                        <div class="author-avatar">MR</div>
                        <div class="author-info">
                            <h4>Michael Rodriguez</h4>
                            <p>Father of twins</p>
                        </div>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <p class="testimonial-text">As an expecting mother living far from family, the community support group became my go-to for advice and encouragement. It felt like having a virtual village!</p>
                    <div class="testimonial-author">
                        <div class="author-avatar">PC</div>
                        <div class="author-info">
                            <h4>Priya Chandrasekhar</h4>
                            <p>Expecting mother</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" id="contact">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-brand">
                    <a href="#" class="footer-logo">
                        <div class="logo-icon">
                            <i class="fas fa-baby"></i>
                        </div>
                        <span>Maternal and Child Health Monitoring</span>
                    </a>
                    <p class="footer-desc">Your trusted companion for pregnancy and baby care, providing expert guidance and support every step of the way.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#"><i class="fas fa-chevron-right"></i> Home</a></li>
                        <li><a href="#"><i class="fas fa-chevron-right"></i> Pregnancy Care</a></li>
                        <li><a href="#"><i class="fas fa-chevron-right"></i> Baby Care</a></li>
                        <li><a href="#"><i class="fas fa-chevron-right"></i> Consult Doctor</a></li>
                        <li><a href="#"><i class="fas fa-chevron-right"></i> Schemes</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="#"><i class="fas fa-chevron-right"></i> Pregnancy Tracking</a></li>
                        <li><a href="#"><i class="fas fa-chevron-right"></i> Baby Development</a></li>
                        <li><a href="#"><i class="fas fa-chevron-right"></i> Doctor Consultations</a></li>
                        <li><a href="#"><i class="fas fa-chevron-right"></i> Nutrition Plans</a></li>
                        <li><a href="#"><i class="fas fa-chevron-right"></i> Community Support</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Contact Us</h4>
                    <ul>
                        <li><a href="#"><i class="fas fa-map-marker-alt"></i> 123 Health Street, Medical City</a></li>
                        <li><a href="tel:+15551234567"><i class="fas fa-phone"></i> +****************</a></li>
                        <li><a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a></li>
                    </ul>
                    
                    <div class="newsletter">
                        <h5>Subscribe to our Newsletter</h5>
                        <form class="newsletter-form">
                            <input type="email" class="newsletter-input" placeholder="Your email address">
                            <button type="submit" class="newsletter-btn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 Maternal and Child Health Monitoring. All rights reserved.</p>
                <div class="footer-links">
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                    <a href="#">Cookie Policy</a>
                    <a href="#">Sitemap</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <div class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </div>

    <!-- Scripts -->
    <script>
        // Back to top button functionality
        const backToTopBtn = document.getElementById('backToTop');
        
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });
        
        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Mobile menu toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navMenu = document.querySelector('.nav-menu');
        
        mobileMenuBtn.addEventListener('click', () => {
            navMenu.style.display = navMenu.style.display === 'flex' ? 'none' : 'flex';
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!navMenu.contains(e.target) && !mobileMenuBtn.contains(e.target) && window.innerWidth <= 768) {
                navMenu.style.display = 'none';
            }
        });
        
        // Animation on scroll
        const observerOptions = {
            root: null,
            rootMargin: '0px',
            threshold: 0.1
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, observerOptions);
        
        document.querySelectorAll('.feature-card, .testimonial-card').forEach(card => {
            observer.observe(card);
        });

        // Initialize API connection
        async function initializeApp() {
            try {
                // Test backend connection
                const healthCheck = await window.apiClient.healthCheck();
                console.log('✅ Backend connection successful:', healthCheck);

                // Show connection status
                const statusIndicator = document.createElement('div');
                statusIndicator.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #48bb78;
                    color: white;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 12px;
                    z-index: 10000;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                `;
                statusIndicator.innerHTML = '<i class="fas fa-check-circle"></i> Connected';
                document.body.appendChild(statusIndicator);

                // Remove status indicator after 3 seconds
                setTimeout(() => {
                    statusIndicator.remove();
                }, 3000);

            } catch (error) {
                console.error('❌ Backend connection failed:', error);

                // Show error status
                const errorIndicator = document.createElement('div');
                errorIndicator.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #e53e3e;
                    color: white;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 12px;
                    z-index: 10000;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                `;
                errorIndicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Connection Error';
                document.body.appendChild(errorIndicator);
            }
        }

        // Authentication Management
        async function checkAuthStatus() {
            const guestNav = document.getElementById('guest-nav');
            const userNav = document.getElementById('user-nav');
            const userName = document.getElementById('user-name');
            const userRole = document.getElementById('user-role');

            try {
                // Check session-based authentication
                const response = await fetch('/api/auth/status');
                const data = await response.json();

                if (data.authenticated && data.user) {
                    // User is logged in
                    guestNav.style.display = 'none';
                    userNav.style.display = 'flex';
                    userName.textContent = data.user.full_name || data.user.email;
                    userRole.textContent = data.user.role || 'user';
                    userRole.className = `user-role ${data.user.role || 'user'}`;

                    // Store user data for other scripts that might need it
                    localStorage.setItem('mchs_user', JSON.stringify(data.user));
                    localStorage.setItem('mchs_token', 'session-based'); // Placeholder for compatibility
                } else {
                    // User is not logged in
                    guestNav.style.display = 'flex';
                    userNav.style.display = 'none';

                    // Clear any stored data
                    localStorage.removeItem('mchs_user');
                    localStorage.removeItem('mchs_token');
                }
            } catch (error) {
                console.error('Error checking auth status:', error);
                // Default to not logged in
                guestNav.style.display = 'flex';
                userNav.style.display = 'none';
            }
        }

        async function logout() {
            try {
                // Call backend logout endpoint
                await fetch('/logout', { method: 'GET' });

                // Clear authentication data
                localStorage.removeItem('mchs_token');
                localStorage.removeItem('mchs_user');
                localStorage.removeItem('refresh_token');

                // Show success message
                const logoutMessage = document.createElement('div');
                logoutMessage.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #48bb78;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 25px;
                    font-size: 14px;
                    z-index: 10000;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                    animation: slideIn 0.3s ease;
                `;
                logoutMessage.innerHTML = '<i class="fas fa-check-circle"></i> Logged out successfully';
                document.body.appendChild(logoutMessage);

                // Remove message after 3 seconds
                setTimeout(() => {
                    logoutMessage.remove();
                }, 3000);

                // Update UI
                await checkAuthStatus();

                // Redirect to home page
                setTimeout(() => {
                    window.location.href = '/';
                }, 1500);
            } catch (error) {
                console.error('Logout error:', error);
                // Still clear local data and update UI
                localStorage.removeItem('mchs_token');
                localStorage.removeItem('mchs_user');
                await checkAuthStatus();
            }
        }

        // Simple navigation function - no authentication checks
        function navigateToPage(event, url) {
            console.log(`🔗 Navigating to: ${url}`);

            // Prevent any default behavior that might interfere
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            try {
                // Navigate directly - pages are now public
                console.log('Navigating to:', url);
                window.location.href = url;
            } catch (error) {
                console.error('Navigation error:', error);
                // Fallback navigation
                try {
                    console.log('Trying fallback navigation...');
                    window.open(url, '_self');
                } catch (fallbackError) {
                    console.error('Fallback navigation error:', fallbackError);
                    // Last resort: manual URL construction
                    const baseUrl = window.location.origin + window.location.pathname.replace('home.html', '');
                    const fullUrl = baseUrl + url;
                    console.log('Full URL:', fullUrl);
                    window.location.href = fullUrl;
                }
            }
        }

        // Alternative navigation function with authentication (for later use)
        async function navigateToPageWithAuth(event, url) {
            console.log(`🔗 Navigating with auth to: ${url}`);

            // Prevent any default behavior that might interfere
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            try {
                // Check if this is a protected page that requires authentication
                const protectedPages = ['pages/Preg/pregcare.html', 'pages/baby/baby-care.html'];
                const isProtectedPage = protectedPages.some(page => url.includes(page));

                if (isProtectedPage) {
                    console.log('Protected page detected, checking authentication...');

                    // Wait for unified auth to be ready
                    await waitForAuthService();

                    // Check authentication with improved logic
                    let isAuthenticated = false;
                    try {
                        if (window.unifiedAuth && window.unifiedAuth.isInitialized) {
                            // Check session with server first
                            isAuthenticated = await window.unifiedAuth.checkSession();
                            console.log('Server authentication status:', isAuthenticated);

                            // If server check fails, try local check
                            if (!isAuthenticated) {
                                isAuthenticated = window.unifiedAuth.isAuthenticated();
                                console.log('Local authentication status:', isAuthenticated);
                            }
                        } else {
                            // Fallback: direct session check
                            try {
                                const response = await fetch('/api/auth/status', { credentials: 'include' });
                                const data = await response.json();
                                isAuthenticated = data.authenticated && data.user;
                                console.log('Fallback authentication status:', isAuthenticated);
                            } catch (fallbackError) {
                                console.error('Fallback auth check failed:', fallbackError);
                                isAuthenticated = false;
                            }
                        }
                    } catch (authError) {
                        console.error('Authentication check error:', authError);
                        isAuthenticated = false;
                    }

                    // Only redirect to login if definitely not authenticated
                    if (!isAuthenticated) {
                        console.log('User not authenticated, redirecting to login...');
                        window.location.href = '/pages/simple_login.html';
                        return;
                    }

                    console.log('User authenticated, proceeding with navigation...');
                }

                // Navigate to the page
                window.location.href = url;

            } catch (error) {
                console.error('Navigation error:', error);

                // Fallback navigation
                try {
                    window.open(url, '_self');
                } catch (fallbackError) {
                    console.error('Fallback navigation error:', fallbackError);

                    // Last resort: manual URL construction
                    const baseUrl = window.location.origin + window.location.pathname.replace('home.html', '');
                    window.location.href = baseUrl + url;
                }
            }
        }

        // Wait for auth service to be ready
        async function waitForAuthService(maxAttempts = 10) {
            for (let attempt = 1; attempt <= maxAttempts; attempt++) {
                if (window.unifiedAuth && window.unifiedAuth.isInitialized) {
                    console.log('Auth service is ready');
                    return true;
                }
                console.log(`Waiting for auth service... attempt ${attempt}/${maxAttempts}`);
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            console.warn('Auth service not ready, proceeding anyway...');
            return false;
        }

        // Quick Login Functions
        function showQuickLogin() {
            document.getElementById('quick-login').style.display = 'flex';
        }

        function hideQuickLogin() {
            document.getElementById('quick-login').style.display = 'none';
        }

        async function handleQuickLogin(event) {
            event.preventDefault();

            const email = document.getElementById('quick-email').value;
            const password = document.getElementById('quick-password').value;

            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    console.log('Login successful:', data);
                    hideQuickLogin();

                    // Show success message
                    showNotification('Login successful! You can now access all sections.', 'success');

                    // Update UI to show logged in state
                    updateUIForLoggedInUser(data.data.user);

                } else {
                    console.error('Login failed:', data);
                    showNotification(data.error || 'Login failed. Please try again.', 'error');
                }

            } catch (error) {
                console.error('Login error:', error);
                showNotification('Login failed. Please check your connection and try again.', 'error');
            }
        }

        function updateUIForLoggedInUser(user) {
            // Hide guest navigation and show user info
            const guestNav = document.getElementById('guest-nav');
            if (guestNav) {
                guestNav.style.display = 'none';
            }

            // You can add more UI updates here
            console.log('User logged in:', user);
        }

        function showNotification(message, type = 'info') {
            // Simple notification system
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                z-index: 1001;
                max-width: 300px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // Navigation functions for login and signup
        function navigateToLogin(event) {
            console.log('🔐 Showing quick login...');
            if (event) event.preventDefault();
            showQuickLogin();
        }

        function navigateToSignup(event) {
            console.log('📝 Navigating to signup page...');
            navigateToPage(event, 'pages/signup.html');
        }

        // Initialize authentication status check
        async function initializeAuth() {
            try {
                // Wait for unified auth to be ready
                await waitForAuthService();

                // Check authentication status
                await checkAuthStatus();

                // Add logout button event listener
                const logoutBtn = document.getElementById('logout-btn');
                if (logoutBtn) {
                    logoutBtn.addEventListener('click', logout);
                }

                console.log('Authentication initialization complete');
            } catch (error) {
                console.error('Error during authentication initialization:', error);
            }
        }

        // Debug navigation issues
        function debugNavigation() {
            console.log('🔍 Navigation Debug Info:');
            console.log('Current URL:', window.location.href);
            console.log('Base URL:', window.location.origin);
            console.log('Pathname:', window.location.pathname);

            // Test if login and signup pages are accessible
            const testUrls = ['pages/login.html', 'pages/signup.html'];

            testUrls.forEach(url => {
                fetch(url, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            console.log(`✅ ${url} is accessible`);
                        } else {
                            console.log(`❌ ${url} returned status ${response.status}`);
                        }
                    })
                    .catch(error => {
                        console.log(`❌ ${url} error:`, error.message);
                    });
            });
        }

        // Initialize app when DOM is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', async () => {
                await initializeApp();
                await initializeAuth();
                debugNavigation();
                initializeRealTimeContent();
            });
        } else {
            (async () => {
                await initializeApp();
                await initializeAuth();
                debugNavigation();
                initializeRealTimeContent();
            })();
        }

        // Initialize real-time content updates
        function initializeRealTimeContent() {
            if (window.contentManager) {
                // Start real-time content monitoring
                window.contentManager.start();

                // Subscribe to content updates
                window.contentManager.subscribe('all', function(content) {
                    console.log('📡 Real-time content update received on home page:', content);

                    // Show update notification
                    showContentUpdateNotification();
                });

                console.log('✅ Real-time content monitoring initialized for home page');
            }

            // Listen for localStorage updates from admin panel
            window.addEventListener('storage', function(e) {
                if (e.key === 'contentUpdate') {
                    const updateData = JSON.parse(e.newValue);
                    console.log('📡 Received cross-tab content update on home page:', updateData);

                    // Show notification about the update
                    showContentUpdateNotification(updateData.type);

                    // Force refresh content manager
                    if (window.contentManager) {
                        window.contentManager.forceRefresh(updateData.type);
                    }
                }
            });

            // Listen for custom content update events
            window.addEventListener('contentUpdated', function(e) {
                console.log('📡 Received content update event:', e.detail);
                showContentUpdateNotification(e.detail.contentType);
            });
        }

        // Show content update notification
        function showContentUpdateNotification(contentType = 'content') {
            // Remove any existing notifications
            const existingNotifications = document.querySelectorAll('.content-update-notification');
            existingNotifications.forEach(n => n.remove());

            const notification = document.createElement('div');
            notification.className = 'content-update-notification';

            const contentTypeNames = {
                'nutrition': 'Nutrition Plans',
                'exercise': 'Exercise Guides',
                'schemes': 'Government Schemes',
                'meditation': 'Meditation Guides',
                'vaccination': 'Vaccination Schedules'
            };

            const displayName = contentTypeNames[contentType] || 'Health Information';

            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-sync-alt fa-spin"></i>
                    <span>🆕 New ${displayName} available! Content has been updated by admin.</span>
                    <button onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                padding: 1rem;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                animation: slideInRight 0.3s ease-out;
                max-width: 350px;
            `;

            // Add animation styles if not already present
            if (!document.getElementById('notification-styles')) {
                const styles = document.createElement('style');
                styles.id = 'notification-styles';
                styles.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    .notification-content {
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                    }
                    .notification-content button {
                        background: none;
                        border: none;
                        color: white;
                        cursor: pointer;
                        padding: 0.25rem;
                        border-radius: 4px;
                        margin-left: auto;
                    }
                    .notification-content button:hover {
                        background: rgba(255, 255, 255, 0.2);
                    }
                `;
                document.head.appendChild(styles);
            }

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>

    <!-- Additional scripts loaded in head section -->
</body>
</html>
