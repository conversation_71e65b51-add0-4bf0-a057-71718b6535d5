<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Consultation Management - Maternal and Child Health Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        :root {
            --primary: #d63384;
            --secondary: #6f42c1;
            --success: #198754;
            --info: #0dcaf0;
            --warning: #ffc107;
            --danger: #dc3545;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --light-gray: #e9ecef;
            --gradient-primary: linear-gradient(135deg, #d63384 0%, #6f42c1 100%);
        }

        body {
            background: linear-gradient(135deg, #f9f0ff 0%, #f0f9ff 100%);
            color: var(--dark);
            min-height: 100vh;
        }

        .header {
            background: var(--gradient-primary);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .logo i {
            margin-right: 0.5rem;
            font-size: 2rem;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }

        .nav-links a.active {
            background: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-header h1 {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .page-header p {
            color: var(--gray);
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.pending {
            background: var(--warning);
        }

        .stat-icon.confirmed {
            background: var(--success);
        }

        .stat-icon.completed {
            background: var(--info);
        }

        .stat-icon.cancelled {
            background: var(--danger);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--gray);
            font-weight: 600;
        }

        .controls-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .controls-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .search-filters {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding: 0.8rem 1rem 0.8rem 3rem;
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            font-size: 1rem;
            width: 300px;
            transition: border-color 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .search-box i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray);
        }

        .filter-select {
            padding: 0.8rem 1rem;
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            font-size: 1rem;
            background: white;
            cursor: pointer;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary);
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(214, 51, 132, 0.3);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-success:hover {
            background: #157347;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: var(--warning);
            color: var(--dark);
        }

        .btn-warning:hover {
            background: #ffca2c;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: var(--danger);
            color: white;
        }

        .btn-danger:hover {
            background: #bb2d3b;
            transform: translateY(-2px);
        }

        .btn-info {
            background: var(--info);
            color: white;
        }

        .btn-info:hover {
            background: #0aa2c0;
            transform: translateY(-2px);
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .consultations-table-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow-x: auto;
        }

        .consultations-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .consultations-table th,
        .consultations-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--light-gray);
        }

        .consultations-table th {
            background: var(--light);
            font-weight: 600;
            color: var(--dark);
            position: sticky;
            top: 0;
        }

        .consultations-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-confirmed {
            background: #d4edda;
            color: #155724;
        }

        .status-completed {
            background: #cce7ff;
            color: #004085;
        }

        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }

        .urgency-badge {
            padding: 0.2rem 0.6rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .urgency-low {
            background: #d4edda;
            color: #155724;
        }

        .urgency-medium {
            background: #fff3cd;
            color: #856404;
        }

        .urgency-high {
            background: #f8d7da;
            color: #721c24;
        }

        .urgency-emergency {
            background: #dc3545;
            color: white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .patient-info {
            display: flex;
            align-items: center;
        }

        .patient-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 1rem;
        }

        .patient-details h4 {
            margin: 0;
            color: var(--dark);
        }

        .patient-details p {
            margin: 0;
            color: var(--gray);
            font-size: 0.9rem;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: var(--gray);
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--gray);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: var(--dark);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--light-gray);
        }

        .modal-header h2 {
            color: var(--primary);
            margin: 0;
        }

        .close {
            color: var(--gray);
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            border: none;
            background: none;
        }

        .close:hover {
            color: var(--danger);
        }

        .consultation-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .detail-section {
            background: var(--light);
            padding: 1.5rem;
            border-radius: 10px;
        }

        .detail-section h3 {
            color: var(--primary);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .detail-item {
            margin-bottom: 1rem;
        }

        .detail-label {
            font-weight: 600;
            color: var(--dark);
            display: block;
            margin-bottom: 0.3rem;
        }

        .detail-value {
            color: var(--gray);
        }

        .blockchain-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 1rem;
        }

        .blockchain-info h3 {
            color: white;
            margin-bottom: 1rem;
        }

        .blockchain-hash {
            font-family: 'Courier New', monospace;
            background: rgba(255,255,255,0.2);
            padding: 0.5rem;
            border-radius: 5px;
            word-break: break-all;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .controls-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-filters {
                flex-direction: column;
            }
            
            .search-box input {
                width: 100%;
            }
            
            .consultation-details {
                grid-template-columns: 1fr;
            }
            
            .consultations-table-container {
                padding: 1rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">
                <i class="fas fa-stethoscope"></i>
                <span>Consultation Management</span>
            </div>
            <div class="nav-links">
                <a href="/admin"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/admin/manage-consultations" class="active"><i class="fas fa-stethoscope"></i> Consultations</a>
                <a href="/admin/manage-patients"><i class="fas fa-user-injured"></i> Patients</a>
                <a href="/admin/manage-users"><i class="fas fa-users"></i> Users</a>
                <a href="/auth/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="page-header">
            <h1><i class="fas fa-stethoscope"></i> Consultation Management</h1>
            <p>Manage Web3 consultation requests, appointments, and blockchain-verified medical consultations</p>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-icon pending">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number" id="pendingCount">0</div>
                <div class="stat-label">Pending Consultations</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon confirmed">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number" id="confirmedCount">0</div>
                <div class="stat-label">Confirmed Consultations</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon completed">
                    <i class="fas fa-clipboard-check"></i>
                </div>
                <div class="stat-number" id="completedCount">0</div>
                <div class="stat-label">Completed Consultations</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon cancelled">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-number" id="cancelledCount">0</div>
                <div class="stat-label">Cancelled Consultations</div>
            </div>
        </div>

        <!-- Controls Section -->
        <div class="controls-section">
            <div class="controls-header">
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search by patient name, email, or consultation ID...">
                    </div>
                    <select id="statusFilter" class="filter-select">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="confirmed">Confirmed</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    <select id="urgencyFilter" class="filter-select">
                        <option value="">All Urgency</option>
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="emergency">Emergency</option>
                    </select>
                    <select id="typeFilter" class="filter-select">
                        <option value="">All Types</option>
                        <option value="general">General Consultation</option>
                        <option value="prenatal">Prenatal Care</option>
                        <option value="pediatric">Pediatric Care</option>
                        <option value="emergency">Emergency</option>
                        <option value="follow-up">Follow-up</option>
                    </select>
                </div>
                <div>
                    <button class="btn btn-success" onclick="exportConsultations()">
                        <i class="fas fa-download"></i> Export
                    </button>
                    <button class="btn btn-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Consultations Table -->
        <div class="consultations-table-container">
            <div id="loadingIndicator" class="loading">
                <i class="fas fa-spinner"></i>
                <p>Loading consultations...</p>
            </div>
            
            <div id="consultationsTableContainer" style="display: none;">
                <table class="consultations-table" id="consultationsTable">
                    <thead>
                        <tr>
                            <th>Patient</th>
                            <th>Doctor</th>
                            <th>Type</th>
                            <th>Date & Time</th>
                            <th>Urgency</th>
                            <th>Status</th>
                            <th>Blockchain</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="consultationsTableBody">
                        <!-- Consultations will be loaded here -->
                    </tbody>
                </table>
            </div>

            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="fas fa-stethoscope"></i>
                <h3>No Consultations Found</h3>
                <p>No consultations match your current search criteria.</p>
            </div>
        </div>
    </div>

    <!-- Consultation Details Modal -->
    <div id="consultationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Consultation Details</h2>
                <button class="close" onclick="closeConsultationModal()">&times;</button>
            </div>
            <div id="consultationDetailsContent">
                <!-- Consultation details will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        let consultations = [];
        let filteredConsultations = [];

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            loadConsultations();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Search functionality
            document.getElementById('searchInput').addEventListener('input', filterConsultations);
            document.getElementById('statusFilter').addEventListener('change', filterConsultations);
            document.getElementById('urgencyFilter').addEventListener('change', filterConsultations);
            document.getElementById('typeFilter').addEventListener('change', filterConsultations);

            // Modal close on outside click
            document.getElementById('consultationModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeConsultationModal();
                }
            });
        }

        async function loadConsultations() {
            try {
                showLoading(true);
                
                const response = await fetch('/admin/api/consultations', {
                    credentials: 'include'
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        consultations = result.consultations || [];
                        filteredConsultations = [...consultations];
                        displayConsultations();
                        updateStatistics();
                    } else {
                        showError('Failed to load consultations: ' + result.error);
                    }
                } else {
                    showError('Failed to load consultations. Please try again.');
                }
            } catch (error) {
                console.error('Error loading consultations:', error);
                showError('Error loading consultations. Please check your connection.');
            } finally {
                showLoading(false);
            }
        }

        function updateStatistics() {
            const stats = {
                pending: 0,
                confirmed: 0,
                completed: 0,
                cancelled: 0
            };

            consultations.forEach(consultation => {
                if (stats.hasOwnProperty(consultation.status)) {
                    stats[consultation.status]++;
                }
            });

            document.getElementById('pendingCount').textContent = stats.pending;
            document.getElementById('confirmedCount').textContent = stats.confirmed;
            document.getElementById('completedCount').textContent = stats.completed;
            document.getElementById('cancelledCount').textContent = stats.cancelled;
        }

        function displayConsultations() {
            const tableBody = document.getElementById('consultationsTableBody');
            const tableContainer = document.getElementById('consultationsTableContainer');
            const emptyState = document.getElementById('emptyState');

            if (filteredConsultations.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tableBody.innerHTML = filteredConsultations.map(consultation => {
                const initials = consultation.patient_name.split(' ').map(n => n[0]).join('').toUpperCase();
                const statusClass = `status-${consultation.status}`;
                const urgencyClass = `urgency-${consultation.urgency_level}`;
                const consultationDate = new Date(consultation.preferred_date).toLocaleDateString();
                const consultationTime = consultation.preferred_time;
                const createdDate = new Date(consultation.created_at).toLocaleDateString();

                return `
                    <tr>
                        <td>
                            <div class="patient-info">
                                <div class="patient-avatar">${initials}</div>
                                <div class="patient-details">
                                    <h4>${consultation.patient_name}</h4>
                                    <p>${consultation.patient_email}</p>
                                    <p>${consultation.patient_phone}</p>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>${consultation.doctor_name}</strong><br>
                                <span style="color: var(--gray);">${consultation.doctor_email}</span>
                            </div>
                        </td>
                        <td>
                            <span style="text-transform: capitalize;">${consultation.consultation_type.replace('_', ' ')}</span>
                        </td>
                        <td>
                            <div>
                                <strong>${consultationDate}</strong><br>
                                <span style="color: var(--gray);">${consultationTime}</span><br>
                                <small style="color: var(--gray);">Created: ${createdDate}</small>
                            </div>
                        </td>
                        <td>
                            <span class="urgency-badge ${urgencyClass}">${consultation.urgency_level}</span>
                        </td>
                        <td>
                            <span class="status-badge ${statusClass}">${consultation.status}</span>
                        </td>
                        <td>
                            <div style="font-family: monospace; font-size: 0.8rem;">
                                ${consultation.blockchain_hash.substring(0, 8)}...
                                <br>
                                <small style="color: var(--gray);">${consultation.wallet_address.substring(0, 8)}...</small>
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-info" onclick="viewConsultation('${consultation.id}')" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-success" onclick="updateConsultationStatus('${consultation.id}', 'confirmed')" title="Confirm">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="updateConsultationStatus('${consultation.id}', 'completed')" title="Complete">
                                    <i class="fas fa-clipboard-check"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="updateConsultationStatus('${consultation.id}', 'cancelled')" title="Cancel">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function filterConsultations() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const urgencyFilter = document.getElementById('urgencyFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;

            filteredConsultations = consultations.filter(consultation => {
                const matchesSearch = !searchTerm || 
                    consultation.patient_name.toLowerCase().includes(searchTerm) ||
                    consultation.patient_email.toLowerCase().includes(searchTerm) ||
                    consultation.id.toLowerCase().includes(searchTerm) ||
                    consultation.doctor_name.toLowerCase().includes(searchTerm);

                const matchesStatus = !statusFilter || consultation.status === statusFilter;
                const matchesUrgency = !urgencyFilter || consultation.urgency_level === urgencyFilter;
                const matchesType = !typeFilter || consultation.consultation_type === typeFilter;

                return matchesSearch && matchesStatus && matchesUrgency && matchesType;
            });

            displayConsultations();
        }

        async function viewConsultation(consultationId) {
            try {
                const consultation = consultations.find(c => c.id === consultationId);
                if (!consultation) return;

                document.getElementById('modalTitle').textContent = `Consultation Details - ${consultation.patient_name}`;
                
                const detailsContent = `
                    <div class="consultation-details">
                        <div class="detail-section">
                            <h3><i class="fas fa-user"></i> Patient Information</h3>
                            <div class="detail-item">
                                <span class="detail-label">Full Name:</span>
                                <span class="detail-value">${consultation.patient_name}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Email:</span>
                                <span class="detail-value">${consultation.patient_email}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Phone:</span>
                                <span class="detail-value">${consultation.patient_phone}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Age:</span>
                                <span class="detail-value">${consultation.patient_age || 'Not specified'}</span>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3><i class="fas fa-user-md"></i> Doctor Information</h3>
                            <div class="detail-item">
                                <span class="detail-label">Doctor Name:</span>
                                <span class="detail-value">${consultation.doctor_name}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Email:</span>
                                <span class="detail-value">${consultation.doctor_email}</span>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3><i class="fas fa-calendar"></i> Consultation Details</h3>
                            <div class="detail-item">
                                <span class="detail-label">Type:</span>
                                <span class="detail-value" style="text-transform: capitalize;">${consultation.consultation_type.replace('_', ' ')}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Preferred Date:</span>
                                <span class="detail-value">${new Date(consultation.preferred_date).toLocaleDateString()}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Preferred Time:</span>
                                <span class="detail-value">${consultation.preferred_time}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Urgency Level:</span>
                                <span class="detail-value">
                                    <span class="urgency-badge urgency-${consultation.urgency_level}">${consultation.urgency_level}</span>
                                </span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Status:</span>
                                <span class="detail-value">
                                    <span class="status-badge status-${consultation.status}">${consultation.status}</span>
                                </span>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3><i class="fas fa-notes-medical"></i> Medical Information</h3>
                            <div class="detail-item">
                                <span class="detail-label">Symptoms:</span>
                                <span class="detail-value">${consultation.symptoms}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Medical History:</span>
                                <span class="detail-value">${consultation.medical_history || 'Not provided'}</span>
                            </div>
                        </div>
                    </div>

                    <div class="blockchain-info">
                        <h3><i class="fas fa-link"></i> Blockchain Verification</h3>
                        <div class="detail-item">
                            <span class="detail-label">Blockchain Hash:</span>
                            <div class="blockchain-hash">${consultation.blockchain_hash}</div>
                        </div>
                        <div class="detail-item" style="margin-top: 1rem;">
                            <span class="detail-label">Wallet Address:</span>
                            <div class="blockchain-hash">${consultation.wallet_address}</div>
                        </div>
                        <div class="detail-item" style="margin-top: 1rem;">
                            <span class="detail-label">Blockchain Consent:</span>
                            <span class="detail-value">${consultation.blockchain_consent ? 'Yes' : 'No'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Email Consent:</span>
                            <span class="detail-value">${consultation.email_consent ? 'Yes' : 'No'}</span>
                        </div>
                    </div>

                    <div style="text-align: right; margin-top: 2rem;">
                        <button class="btn btn-success" onclick="updateConsultationStatus('${consultation.id}', 'confirmed')">
                            <i class="fas fa-check"></i> Confirm Consultation
                        </button>
                        <button class="btn btn-warning" onclick="updateConsultationStatus('${consultation.id}', 'completed')">
                            <i class="fas fa-clipboard-check"></i> Mark Complete
                        </button>
                        <button class="btn btn-danger" onclick="updateConsultationStatus('${consultation.id}', 'cancelled')">
                            <i class="fas fa-times"></i> Cancel Consultation
                        </button>
                    </div>
                `;

                document.getElementById('consultationDetailsContent').innerHTML = detailsContent;
                document.getElementById('consultationModal').style.display = 'block';

            } catch (error) {
                console.error('Error viewing consultation:', error);
                showError('Error loading consultation details.');
            }
        }

        async function updateConsultationStatus(consultationId, newStatus) {
            const consultation = consultations.find(c => c.id === consultationId);
            if (!consultation) return;

            const statusMessages = {
                confirmed: 'confirm',
                completed: 'mark as completed',
                cancelled: 'cancel'
            };

            if (!confirm(`Are you sure you want to ${statusMessages[newStatus]} this consultation for ${consultation.patient_name}?`)) {
                return;
            }

            try {
                const response = await fetch(`/admin/api/consultations/${consultationId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ status: newStatus })
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess(`Consultation ${statusMessages[newStatus]}ed successfully!`);
                    closeConsultationModal();
                    loadConsultations(); // Reload the consultations list
                } else {
                    showError('Failed to update consultation status: ' + result.error);
                }
            } catch (error) {
                console.error('Error updating consultation status:', error);
                showError('Error updating consultation status. Please try again.');
            }
        }

        function closeConsultationModal() {
            document.getElementById('consultationModal').style.display = 'none';
        }

        function refreshData() {
            loadConsultations();
        }

        function exportConsultations() {
            // Create CSV content
            const headers = ['ID', 'Patient Name', 'Patient Email', 'Patient Phone', 'Doctor Name', 'Consultation Type', 'Preferred Date', 'Preferred Time', 'Urgency Level', 'Status', 'Symptoms', 'Medical History', 'Blockchain Hash', 'Wallet Address', 'Created At'];
            const csvContent = [
                headers.join(','),
                ...filteredConsultations.map(consultation => [
                    consultation.id,
                    `"${consultation.patient_name}"`,
                    consultation.patient_email,
                    consultation.patient_phone,
                    `"${consultation.doctor_name}"`,
                    consultation.consultation_type,
                    consultation.preferred_date,
                    consultation.preferred_time,
                    consultation.urgency_level,
                    consultation.status,
                    `"${consultation.symptoms}"`,
                    `"${consultation.medical_history || ''}"`,
                    consultation.blockchain_hash,
                    consultation.wallet_address,
                    consultation.created_at
                ].join(','))
            ].join('\n');

            // Download CSV file
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `consultations_export_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showSuccess('Consultations exported successfully!');
        }

        function showLoading(show) {
            document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
        }

        function showSuccess(message) {
            // Simple alert for now - you can replace with a better notification system
            alert('✅ ' + message);
        }

        function showError(message) {
            // Simple alert for now - you can replace with a better notification system
            alert('❌ ' + message);
        }
    </script>
</body>
</html>
