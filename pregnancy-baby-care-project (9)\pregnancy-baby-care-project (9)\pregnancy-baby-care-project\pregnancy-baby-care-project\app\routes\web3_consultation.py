"""
Web3 Consultation Routes
Handles blockchain-verified doctor consultations with email notifications
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from datetime import datetime, date
from app.data_manager import DataManager
from app.services.email_service import email_service
import json
import uuid
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

web3_consultation_bp = Blueprint('web3_consultation', __name__, url_prefix='/consultation')

def login_required(f):
    """Simple login required decorator"""
    def decorated_function(*args, **kwargs):
        if not session.get('user_id'):
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@web3_consultation_bp.route('/')
def index():
    """Web3 consultation main page"""
    return render_template('consultation/web3_consultation.html')

@web3_consultation_bp.route('/status')
@login_required
def consultation_status():
    """View consultation status page"""
    try:
        user_id = session.get('user_id')
        consultations = get_user_consultations(user_id)
        return render_template('consultation/consultation_status.html', consultations=consultations)
    except Exception as e:
        logger.error(f"Error loading consultation status: {e}")
        return render_template('consultation/consultation_status.html', consultations=[])

@web3_consultation_bp.route('/api/submit', methods=['POST'])
def submit_web3_consultation():
    """Submit a new Web3 consultation request"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['patientName', 'patientEmail', 'patientPhone', 'doctorSelect', 
                          'consultationType', 'preferredDate', 'preferredTime', 'symptoms',
                          'blockchainHash', 'walletAddress']
        
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'Missing required field: {field}'
                }), 400
        
        # Generate unique consultation ID
        consultation_id = str(uuid.uuid4())
        
        # Get doctor information
        doctor_info = get_doctor_by_id(data['doctorSelect'])
        if not doctor_info:
            return jsonify({
                'success': False,
                'message': 'Selected doctor not found'
            }), 400
        
        # Create consultation record
        consultation_data = {
            'id': consultation_id,
            'patient_name': data['patientName'],
            'patient_email': data['patientEmail'],
            'patient_phone': data['patientPhone'],
            'patient_age': data.get('patientAge'),
            'doctor_id': data['doctorSelect'],
            'doctor_name': doctor_info['full_name'],
            'doctor_email': doctor_info['email'],
            'consultation_type': data['consultationType'],
            'preferred_date': data['preferredDate'],
            'preferred_time': data['preferredTime'],
            'urgency_level': data['urgencyLevel'],
            'symptoms': data['symptoms'],
            'medical_history': data.get('medicalHistory', ''),
            'blockchain_hash': data['blockchainHash'],
            'wallet_address': data['walletAddress'],
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'blockchain_consent': data.get('blockchainConsent', False),
            'email_consent': data.get('emailConsent', False)
        }
        
        # Save consultation to database
        consultation_saved = save_consultation_request(consultation_data)
        
        if not consultation_saved:
            return jsonify({
                'success': False,
                'message': 'Failed to save consultation request'
            }), 500
        
        # Send email notifications if consent given
        email_success = True
        if data.get('emailConsent', False):
            try:
                # Send confirmation email to patient
                patient_email_sent = email_service.send_consultation_request_to_patient(
                    {
                        'patientName': data['patientName'],
                        'patientEmail': data['patientEmail']
                    },
                    {
                        'doctorName': f"Dr. {doctor_info['full_name']}",
                        'consultationType': data['consultationType'],
                        'preferredDate': data['preferredDate'],
                        'preferredTime': data['preferredTime'],
                        'urgencyLevel': data['urgencyLevel'],
                        'blockchainHash': data['blockchainHash'],
                        'walletAddress': data['walletAddress']
                    }
                )
                
                # Send notification email to doctor
                doctor_email_sent = email_service.send_consultation_request_to_doctor(
                    doctor_info['email'],
                    {
                        'patientName': data['patientName'],
                        'patientEmail': data['patientEmail'],
                        'patientPhone': data['patientPhone'],
                        'patientAge': data.get('patientAge', 'N/A')
                    },
                    {
                        'consultationType': data['consultationType'],
                        'preferredDate': data['preferredDate'],
                        'preferredTime': data['preferredTime'],
                        'urgencyLevel': data['urgencyLevel'],
                        'symptoms': data['symptoms'],
                        'medicalHistory': data.get('medicalHistory', ''),
                        'blockchainHash': data['blockchainHash'],
                        'walletAddress': data['walletAddress']
                    }
                )
                
                email_success = patient_email_sent and doctor_email_sent
                
            except Exception as e:
                logger.error(f"Error sending email notifications: {e}")
                email_success = False
        
        # Log the consultation request
        logger.info(f"✅ Web3 consultation request submitted:")
        logger.info(f"   - ID: {consultation_id}")
        logger.info(f"   - Patient: {data['patientName']}")
        logger.info(f"   - Doctor: Dr. {doctor_info['full_name']}")
        logger.info(f"   - Blockchain Hash: {data['blockchainHash']}")
        logger.info(f"   - Email notifications: {'✅' if email_success else '❌'}")
        
        return jsonify({
            'success': True,
            'message': 'Web3 consultation request submitted successfully',
            'consultation_id': consultation_id,
            'blockchain_hash': data['blockchainHash'],
            'email_sent': email_success
        })
        
    except Exception as e:
        logger.error(f"Error submitting Web3 consultation: {e}")
        return jsonify({
            'success': False,
            'message': 'Internal server error. Please try again.'
        }), 500

@web3_consultation_bp.route('/api/consultations')
@login_required
def get_consultations():
    """Get user's consultation requests"""
    try:
        user_id = session.get('user_id')
        consultations = get_user_consultations(user_id)
        
        return jsonify({
            'success': True,
            'consultations': consultations
        })
        
    except Exception as e:
        logger.error(f"Error fetching consultations: {e}")
        return jsonify({
            'success': False,
            'message': 'Failed to fetch consultations'
        }), 500

@web3_consultation_bp.route('/api/consultation/<consultation_id>')
@login_required
def get_consultation_details(consultation_id):
    """Get detailed information about a specific consultation"""
    try:
        consultation = get_consultation_by_id(consultation_id)
        
        if not consultation:
            return jsonify({
                'success': False,
                'message': 'Consultation not found'
            }), 404
        
        # Check if user owns this consultation
        user_id = session.get('user_id')
        user = DataManager.get_user_by_id(user_id)
        
        if consultation['patient_email'] != user.get('email'):
            return jsonify({
                'success': False,
                'message': 'Access denied'
            }), 403
        
        return jsonify({
            'success': True,
            'consultation': consultation
        })
        
    except Exception as e:
        logger.error(f"Error fetching consultation details: {e}")
        return jsonify({
            'success': False,
            'message': 'Failed to fetch consultation details'
        }), 500

# Helper functions

def get_doctor_by_id(doctor_id):
    """Get doctor information by ID"""
    try:
        users = DataManager.get_all_users()
        doctor = next((u for u in users if u.get('id') == int(doctor_id) and u.get('role') == 'doctor'), None)
        return doctor
    except Exception as e:
        logger.error(f"Error fetching doctor by ID {doctor_id}: {e}")
        return None

def save_consultation_request(consultation_data):
    """Save consultation request to database"""
    try:
        conn = DataManager.get_connection()
        cursor = conn.cursor()
        
        # Create web3_consultations table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS web3_consultations (
                id TEXT PRIMARY KEY,
                patient_name TEXT NOT NULL,
                patient_email TEXT NOT NULL,
                patient_phone TEXT NOT NULL,
                patient_age INTEGER,
                doctor_id INTEGER NOT NULL,
                doctor_name TEXT NOT NULL,
                doctor_email TEXT NOT NULL,
                consultation_type TEXT NOT NULL,
                preferred_date TEXT NOT NULL,
                preferred_time TEXT NOT NULL,
                urgency_level TEXT NOT NULL,
                symptoms TEXT NOT NULL,
                medical_history TEXT,
                blockchain_hash TEXT NOT NULL,
                wallet_address TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                created_at TEXT NOT NULL,
                updated_at TEXT,
                blockchain_consent BOOLEAN DEFAULT 0,
                email_consent BOOLEAN DEFAULT 0,
                FOREIGN KEY (doctor_id) REFERENCES users (id)
            )
        ''')
        
        # Insert consultation request
        cursor.execute('''
            INSERT INTO web3_consultations (
                id, patient_name, patient_email, patient_phone, patient_age,
                doctor_id, doctor_name, doctor_email, consultation_type,
                preferred_date, preferred_time, urgency_level, symptoms,
                medical_history, blockchain_hash, wallet_address, status,
                created_at, blockchain_consent, email_consent
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            consultation_data['id'],
            consultation_data['patient_name'],
            consultation_data['patient_email'],
            consultation_data['patient_phone'],
            consultation_data.get('patient_age'),
            consultation_data['doctor_id'],
            consultation_data['doctor_name'],
            consultation_data['doctor_email'],
            consultation_data['consultation_type'],
            consultation_data['preferred_date'],
            consultation_data['preferred_time'],
            consultation_data['urgency_level'],
            consultation_data['symptoms'],
            consultation_data['medical_history'],
            consultation_data['blockchain_hash'],
            consultation_data['wallet_address'],
            consultation_data['status'],
            consultation_data['created_at'],
            consultation_data['blockchain_consent'],
            consultation_data['email_consent']
        ))
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"Error saving consultation request: {e}")
        return False

def get_user_consultations(user_id):
    """Get all consultations for a user"""
    try:
        user = DataManager.get_user_by_id(user_id)
        if not user:
            return []
        
        conn = DataManager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM web3_consultations 
            WHERE patient_email = ? 
            ORDER BY created_at DESC
        ''', (user['email'],))
        
        rows = cursor.fetchall()
        conn.close()
        
        consultations = []
        for row in rows:
            consultations.append({
                'id': row[0],
                'patient_name': row[1],
                'patient_email': row[2],
                'patient_phone': row[3],
                'patient_age': row[4],
                'doctor_id': row[5],
                'doctor_name': row[6],
                'doctor_email': row[7],
                'consultation_type': row[8],
                'preferred_date': row[9],
                'preferred_time': row[10],
                'urgency_level': row[11],
                'symptoms': row[12],
                'medical_history': row[13],
                'blockchain_hash': row[14],
                'wallet_address': row[15],
                'status': row[16],
                'created_at': row[17],
                'updated_at': row[18],
                'blockchain_consent': row[19],
                'email_consent': row[20]
            })
        
        return consultations
        
    except Exception as e:
        logger.error(f"Error fetching user consultations: {e}")
        return []

def get_consultation_by_id(consultation_id):
    """Get consultation by ID"""
    try:
        conn = DataManager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM web3_consultations WHERE id = ?', (consultation_id,))
        row = cursor.fetchone()
        conn.close()
        
        if not row:
            return None
        
        return {
            'id': row[0],
            'patient_name': row[1],
            'patient_email': row[2],
            'patient_phone': row[3],
            'patient_age': row[4],
            'doctor_id': row[5],
            'doctor_name': row[6],
            'doctor_email': row[7],
            'consultation_type': row[8],
            'preferred_date': row[9],
            'preferred_time': row[10],
            'urgency_level': row[11],
            'symptoms': row[12],
            'medical_history': row[13],
            'blockchain_hash': row[14],
            'wallet_address': row[15],
            'status': row[16],
            'created_at': row[17],
            'updated_at': row[18],
            'blockchain_consent': row[19],
            'email_consent': row[20]
        }
        
    except Exception as e:
        logger.error(f"Error fetching consultation by ID: {e}")
        return None
