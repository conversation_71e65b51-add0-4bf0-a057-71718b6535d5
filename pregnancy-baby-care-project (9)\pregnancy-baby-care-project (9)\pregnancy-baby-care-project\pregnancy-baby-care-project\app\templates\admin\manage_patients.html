<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Management - Maternal and Child Health Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        :root {
            --primary: #d63384;
            --secondary: #6f42c1;
            --success: #198754;
            --info: #0dcaf0;
            --warning: #ffc107;
            --danger: #dc3545;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --light-gray: #e9ecef;
            --gradient-primary: linear-gradient(135deg, #d63384 0%, #6f42c1 100%);
        }

        body {
            background: linear-gradient(135deg, #f9f0ff 0%, #f0f9ff 100%);
            color: var(--dark);
            min-height: 100vh;
        }

        .header {
            background: var(--gradient-primary);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .logo i {
            margin-right: 0.5rem;
            font-size: 2rem;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }

        .nav-links a.active {
            background: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .page-header h1 {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .page-header p {
            color: var(--gray);
            font-size: 1.1rem;
        }

        .controls-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .controls-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .search-filters {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding: 0.8rem 1rem 0.8rem 3rem;
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            font-size: 1rem;
            width: 300px;
            transition: border-color 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .search-box i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray);
        }

        .filter-select {
            padding: 0.8rem 1rem;
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            font-size: 1rem;
            background: white;
            cursor: pointer;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary);
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(214, 51, 132, 0.3);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-success:hover {
            background: #157347;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: var(--warning);
            color: var(--dark);
        }

        .btn-warning:hover {
            background: #ffca2c;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: var(--danger);
            color: white;
        }

        .btn-danger:hover {
            background: #bb2d3b;
            transform: translateY(-2px);
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .patients-table-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow-x: auto;
        }

        .patients-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .patients-table th,
        .patients-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--light-gray);
        }

        .patients-table th {
            background: var(--light);
            font-weight: 600;
            color: var(--dark);
            position: sticky;
            top: 0;
        }

        .patients-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .patient-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 1rem;
        }

        .patient-info {
            display: flex;
            align-items: center;
        }

        .patient-details h4 {
            margin: 0;
            color: var(--dark);
        }

        .patient-details p {
            margin: 0;
            color: var(--gray);
            font-size: 0.9rem;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: var(--gray);
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--gray);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: var(--dark);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--light-gray);
        }

        .modal-header h2 {
            color: var(--primary);
            margin: 0;
        }

        .close {
            color: var(--gray);
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            border: none;
            background: none;
        }

        .close:hover {
            color: var(--danger);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .controls-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-filters {
                flex-direction: column;
            }
            
            .search-box input {
                width: 100%;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .patients-table-container {
                padding: 1rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">
                <i class="fas fa-user-injured"></i>
                <span>Patient Management</span>
            </div>
            <div class="nav-links">
                <a href="/admin"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/admin/manage-patients" class="active"><i class="fas fa-user-injured"></i> Patients</a>
                <a href="/admin/manage-users"><i class="fas fa-users"></i> Users</a>
                <a href="/auth/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="page-header">
            <h1><i class="fas fa-user-injured"></i> Patient Management</h1>
            <p>Manage patient records, medical history, and healthcare information</p>
        </div>

        <!-- Controls Section -->
        <div class="controls-section">
            <div class="controls-header">
                <div class="search-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search patients by name, email, or phone...">
                    </div>
                    <select id="statusFilter" class="filter-select">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="pending">Pending</option>
                    </select>
                    <select id="roleFilter" class="filter-select">
                        <option value="">All Roles</option>
                        <option value="user">Patient</option>
                        <option value="doctor">Doctor</option>
                    </select>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="openAddPatientModal()">
                        <i class="fas fa-plus"></i> Add New Patient
                    </button>
                    <button class="btn btn-success" onclick="exportPatients()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Patients Table -->
        <div class="patients-table-container">
            <div id="loadingIndicator" class="loading">
                <i class="fas fa-spinner"></i>
                <p>Loading patients...</p>
            </div>
            
            <div id="patientsTableContainer" style="display: none;">
                <table class="patients-table" id="patientsTable">
                    <thead>
                        <tr>
                            <th>Patient</th>
                            <th>Contact Info</th>
                            <th>Date of Birth</th>
                            <th>Emergency Contact</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="patientsTableBody">
                        <!-- Patients will be loaded here -->
                    </tbody>
                </table>
            </div>

            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="fas fa-user-injured"></i>
                <h3>No Patients Found</h3>
                <p>No patients match your current search criteria.</p>
                <button class="btn btn-primary" onclick="openAddPatientModal()">
                    <i class="fas fa-plus"></i> Add First Patient
                </button>
            </div>
        </div>
    </div>

    <!-- Add/Edit Patient Modal -->
    <div id="patientModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Add New Patient</h2>
                <button class="close" onclick="closePatientModal()">&times;</button>
            </div>
            <form id="patientForm">
                <input type="hidden" id="patientId" name="patientId">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="fullName">Full Name *</label>
                        <input type="text" id="fullName" name="fullName" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email Address *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="tel" id="phone" name="phone">
                    </div>
                    <div class="form-group">
                        <label for="dateOfBirth">Date of Birth</label>
                        <input type="date" id="dateOfBirth" name="dateOfBirth">
                    </div>
                </div>

                <div class="form-group">
                    <label for="address">Address</label>
                    <textarea id="address" name="address" placeholder="Enter full address..."></textarea>
                </div>

                <div class="form-group">
                    <label for="emergencyContact">Emergency Contact</label>
                    <input type="text" id="emergencyContact" name="emergencyContact" placeholder="Name and phone number">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="role">Role</label>
                        <select id="role" name="role">
                            <option value="user">Patient</option>
                            <option value="doctor">Doctor</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status">
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </div>

                <div class="form-group" id="passwordGroup">
                    <label for="password">Password *</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <div style="text-align: right; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closePatientModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-save"></i> Save Patient
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let patients = [];
        let filteredPatients = [];
        let editingPatientId = null;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            loadPatients();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Search functionality
            document.getElementById('searchInput').addEventListener('input', filterPatients);
            document.getElementById('statusFilter').addEventListener('change', filterPatients);
            document.getElementById('roleFilter').addEventListener('change', filterPatients);

            // Form submission
            document.getElementById('patientForm').addEventListener('submit', handleFormSubmit);

            // Modal close on outside click
            document.getElementById('patientModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closePatientModal();
                }
            });
        }

        async function loadPatients() {
            try {
                showLoading(true);
                
                const response = await fetch('/admin/api/patients', {
                    credentials: 'include'
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        patients = result.patients || [];
                        filteredPatients = [...patients];
                        displayPatients();
                    } else {
                        showError('Failed to load patients: ' + result.error);
                    }
                } else {
                    showError('Failed to load patients. Please try again.');
                }
            } catch (error) {
                console.error('Error loading patients:', error);
                showError('Error loading patients. Please check your connection.');
            } finally {
                showLoading(false);
            }
        }

        function displayPatients() {
            const tableBody = document.getElementById('patientsTableBody');
            const tableContainer = document.getElementById('patientsTableContainer');
            const emptyState = document.getElementById('emptyState');

            if (filteredPatients.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tableBody.innerHTML = filteredPatients.map(patient => {
                const initials = patient.full_name.split(' ').map(n => n[0]).join('').toUpperCase();
                const statusClass = patient.is_active ? 'status-active' : 'status-inactive';
                const statusText = patient.is_active ? 'Active' : 'Inactive';
                const lastLogin = patient.last_login ? new Date(patient.last_login).toLocaleDateString() : 'Never';
                const dateOfBirth = patient.date_of_birth ? new Date(patient.date_of_birth).toLocaleDateString() : 'Not specified';

                return `
                    <tr>
                        <td>
                            <div class="patient-info">
                                <div class="patient-avatar">${initials}</div>
                                <div class="patient-details">
                                    <h4>${patient.full_name}</h4>
                                    <p>ID: ${patient.id} | ${patient.role.charAt(0).toUpperCase() + patient.role.slice(1)}</p>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>${patient.email}</strong><br>
                                <span style="color: var(--gray);">${patient.phone || 'No phone'}</span>
                            </div>
                        </td>
                        <td>${dateOfBirth}</td>
                        <td>${patient.emergency_contact || 'Not specified'}</td>
                        <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                        <td>${lastLogin}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-primary" onclick="editPatient(${patient.id})" title="Edit Patient">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="togglePatientStatus(${patient.id})" title="Toggle Status">
                                    <i class="fas fa-${patient.is_active ? 'pause' : 'play'}"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deletePatient(${patient.id})" title="Delete Patient">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function filterPatients() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const roleFilter = document.getElementById('roleFilter').value;

            filteredPatients = patients.filter(patient => {
                const matchesSearch = !searchTerm || 
                    patient.full_name.toLowerCase().includes(searchTerm) ||
                    patient.email.toLowerCase().includes(searchTerm) ||
                    (patient.phone && patient.phone.includes(searchTerm));

                const matchesStatus = !statusFilter || 
                    (statusFilter === 'active' && patient.is_active) ||
                    (statusFilter === 'inactive' && !patient.is_active);

                const matchesRole = !roleFilter || patient.role === roleFilter;

                return matchesSearch && matchesStatus && matchesRole;
            });

            displayPatients();
        }

        function openAddPatientModal() {
            editingPatientId = null;
            document.getElementById('modalTitle').textContent = 'Add New Patient';
            document.getElementById('patientForm').reset();
            document.getElementById('patientId').value = '';
            document.getElementById('passwordGroup').style.display = 'block';
            document.getElementById('password').required = true;
            document.getElementById('patientModal').style.display = 'block';
        }

        function editPatient(patientId) {
            const patient = patients.find(p => p.id === patientId);
            if (!patient) return;

            editingPatientId = patientId;
            document.getElementById('modalTitle').textContent = 'Edit Patient';
            document.getElementById('patientId').value = patient.id;
            document.getElementById('fullName').value = patient.full_name;
            document.getElementById('email').value = patient.email;
            document.getElementById('phone').value = patient.phone || '';
            document.getElementById('dateOfBirth').value = patient.date_of_birth || '';
            document.getElementById('address').value = patient.address || '';
            document.getElementById('emergencyContact').value = patient.emergency_contact || '';
            document.getElementById('role').value = patient.role;
            document.getElementById('status').value = patient.is_active ? '1' : '0';
            
            // Hide password field for editing
            document.getElementById('passwordGroup').style.display = 'none';
            document.getElementById('password').required = false;
            
            document.getElementById('patientModal').style.display = 'block';
        }

        function closePatientModal() {
            document.getElementById('patientModal').style.display = 'none';
            editingPatientId = null;
        }

        async function handleFormSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const patientData = Object.fromEntries(formData.entries());
            
            // Convert status to boolean
            patientData.is_active = patientData.status === '1';
            delete patientData.status;
            
            try {
                const submitBtn = document.getElementById('submitBtn');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                submitBtn.disabled = true;

                const url = editingPatientId ? 
                    `/admin/api/patients/${editingPatientId}` : 
                    '/admin/api/patients';
                
                const method = editingPatientId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify(patientData)
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess(editingPatientId ? 'Patient updated successfully!' : 'Patient created successfully!');
                    closePatientModal();
                    loadPatients(); // Reload the patients list
                } else {
                    showError('Failed to save patient: ' + result.error);
                }
            } catch (error) {
                console.error('Error saving patient:', error);
                showError('Error saving patient. Please try again.');
            } finally {
                const submitBtn = document.getElementById('submitBtn');
                submitBtn.innerHTML = '<i class="fas fa-save"></i> Save Patient';
                submitBtn.disabled = false;
            }
        }

        async function togglePatientStatus(patientId) {
            const patient = patients.find(p => p.id === patientId);
            if (!patient) return;

            const newStatus = !patient.is_active;
            const action = newStatus ? 'activate' : 'deactivate';

            if (!confirm(`Are you sure you want to ${action} this patient?`)) {
                return;
            }

            try {
                const response = await fetch(`/admin/api/patients/${patientId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ is_active: newStatus })
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess(`Patient ${action}d successfully!`);
                    loadPatients(); // Reload the patients list
                } else {
                    showError('Failed to update patient status: ' + result.error);
                }
            } catch (error) {
                console.error('Error updating patient status:', error);
                showError('Error updating patient status. Please try again.');
            }
        }

        async function deletePatient(patientId) {
            const patient = patients.find(p => p.id === patientId);
            if (!patient) return;

            if (!confirm(`Are you sure you want to delete ${patient.full_name}? This action cannot be undone.`)) {
                return;
            }

            try {
                const response = await fetch(`/admin/api/patients/${patientId}`, {
                    method: 'DELETE',
                    credentials: 'include'
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('Patient deleted successfully!');
                    loadPatients(); // Reload the patients list
                } else {
                    showError('Failed to delete patient: ' + result.error);
                }
            } catch (error) {
                console.error('Error deleting patient:', error);
                showError('Error deleting patient. Please try again.');
            }
        }

        function exportPatients() {
            // Create CSV content
            const headers = ['ID', 'Full Name', 'Email', 'Phone', 'Date of Birth', 'Address', 'Emergency Contact', 'Role', 'Status', 'Created At', 'Last Login'];
            const csvContent = [
                headers.join(','),
                ...filteredPatients.map(patient => [
                    patient.id,
                    `"${patient.full_name}"`,
                    patient.email,
                    patient.phone || '',
                    patient.date_of_birth || '',
                    `"${patient.address || ''}"`,
                    `"${patient.emergency_contact || ''}"`,
                    patient.role,
                    patient.is_active ? 'Active' : 'Inactive',
                    patient.created_at || '',
                    patient.last_login || ''
                ].join(','))
            ].join('\n');

            // Download CSV file
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `patients_export_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showSuccess('Patients exported successfully!');
        }

        function showLoading(show) {
            document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
        }

        function showSuccess(message) {
            // Simple alert for now - you can replace with a better notification system
            alert('✅ ' + message);
        }

        function showError(message) {
            // Simple alert for now - you can replace with a better notification system
            alert('❌ ' + message);
        }
    </script>
</body>
</html>
