<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Government Schemes - Pregnancy & Baby Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: white;
            text-decoration: none;
            margin-bottom: 1rem;
            padding: 0.5rem 1rem;
            background: rgba(255,255,255,0.2);
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .schemes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .scheme-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .scheme-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .scheme-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .scheme-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .scheme-card h3 {
            color: #333;
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }

        .scheme-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .benefits-list, .documents-list {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .benefits-list li, .documents-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-bottom: 1px solid #eee;
        }

        .benefits-list li:last-child, .documents-list li:last-child {
            border-bottom: none;
        }

        .benefits-list i {
            color: #4caf50;
            width: 16px;
        }

        .documents-list i {
            color: #ff9800;
            width: 16px;
        }

        .scheme-section {
            margin-bottom: 1.5rem;
        }

        .scheme-section h4 {
            color: #667eea;
            font-size: 1rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .eligibility-box, .apply-box {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin-bottom: 1rem;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .loading i {
            font-size: 2rem;
            margin-bottom: 1rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .schemes-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Home
        </a>
        
        <div class="header">
            <h1>
                <i class="fas fa-landmark"></i>
                Government Schemes
            </h1>
            <p>Government programs and benefits for maternal and child health</p>
        </div>

        <div class="schemes-grid" id="schemes-grid">
            <div class="loading">
                <i class="fas fa-spinner"></i>
                <p>Loading government schemes...</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadSchemesData();
        });

        async function loadSchemesData() {
            try {
                const response = await fetch('/api/schemes-data');
                const data = await response.json();

                if (data.success) {
                    displaySchemesData(data.data);
                } else {
                    showError('Failed to load schemes data');
                }
            } catch (error) {
                console.error('Error loading schemes data:', error);
                showError('Error loading schemes data');
            }
        }

        function displaySchemesData(schemesData) {
            const container = document.getElementById('schemes-grid');
            
            if (schemesData.length === 0) {
                container.innerHTML = `
                    <div class="scheme-card">
                        <div class="scheme-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <h3>No Schemes Available</h3>
                        <p>Government scheme information will be added soon.</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = schemesData.map(scheme => `
                <div class="scheme-card">
                    <div class="scheme-icon">
                        <i class="fas fa-landmark"></i>
                    </div>
                    <h3>${scheme.name || scheme.title}</h3>
                    <p>${scheme.description}</p>

                    <div class="scheme-section">
                        <h4><i class="fas fa-gift"></i> Benefits</h4>
                        <div class="benefits-content">
                            ${typeof scheme.benefits === 'string' ?
                                `<p><i class="fas fa-check"></i> ${scheme.benefits}</p>` :
                                `<ul class="benefits-list">
                                    ${(scheme.benefits || []).map(benefit => `
                                        <li><i class="fas fa-check"></i> ${benefit}</li>
                                    `).join('')}
                                </ul>`
                            }
                        </div>
                    </div>

                    <div class="scheme-section">
                        <h4><i class="fas fa-user-check"></i> Eligibility</h4>
                        <div class="eligibility-box">
                            ${scheme.eligibility || 'Eligibility criteria will be updated soon'}
                        </div>
                    </div>

                    ${scheme.documents_required ? `
                        <div class="scheme-section">
                            <h4><i class="fas fa-file-alt"></i> Required Documents</h4>
                            <ul class="documents-list">
                                ${(Array.isArray(scheme.documents_required) ? scheme.documents_required : [scheme.documents_required]).map(doc => `
                                    <li><i class="fas fa-file"></i> ${doc}</li>
                                `).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    <div class="scheme-section">
                        <h4><i class="fas fa-hand-point-right"></i> How to Apply</h4>
                        <div class="apply-box">
                            ${scheme.how_to_apply || 'Application process will be updated soon'}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function showError(message) {
            const container = document.getElementById('schemes-grid');
            container.innerHTML = `
                <div class="scheme-card">
                    <div class="scheme-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>Error</h3>
                    <p>${message}</p>
                </div>
            `;
        }
    </script>
</body>
</html>
