<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Meditation & Wellness - Maternal and Child Health Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Arial", sans-serif;
        }

        body {
            color: #333;
            background-color: #fff5f7;
        }

        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 2rem;
            z-index: 1000;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
        }

        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #d63384;
            display: flex;
            align-items: center;
        }

        .logo i {
            margin-right: 10px;
            font-size: 1.5rem;
        }

        .nav-links a {
            margin: 0 1rem;
            color: #333;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
            position: relative;
        }

        .nav-links a:hover {
            color: #d63384;
        }

        .nav-links a::after {
            content: "";
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 0;
            background-color: #d63384;
            transition: width 0.3s;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .book-now {
            background-color: #d63384;
            border: none;
            padding: 0.7rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            color: white;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(214, 51, 132, 0.3);
        }

        .book-now:hover {
            background-color: #b52a6f;
            transform: translateY(-2px);
        }

        .page-header {
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
            url("https://images.unsplash.com/photo-1506905925346-21bda4d32df4?auto=format&fit=crop&w=1470&q=80")
            no-repeat center center/cover;
            height: 50vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            margin-top: 70px;
        }

        .page-header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .page-header p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .content-section {
            padding: 5rem 2rem;
            background-color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            margin-bottom: 2rem;
            color: #d63384;
            font-size: 2.5rem;
        }

        /* Admin Panel Styles */
        .admin-panel {
            background: rgba(214, 51, 132, 0.1);
            border: 2px solid #d63384;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
            box-shadow: 0 8px 32px rgba(214, 51, 132, 0.1);
        }

        .admin-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
            color: #d63384;
        }

        .admin-header i {
            margin-right: 10px;
            color: #ffd700;
        }

        .admin-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-admin {
            background: #d63384;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-admin:hover {
            background: #b52a6f;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(214, 51, 132, 0.3);
        }

        .btn-admin i {
            font-size: 0.9rem;
        }

        .meditation-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .tab-btn {
            padding: 1rem 2rem;
            background-color: #f8f9fa;
            border: none;
            margin: 0 0.5rem 1rem;
            border-radius: 30px;
            cursor: pointer;
            font-weight: bold;
            color: #333;
            transition: all 0.3s;
        }

        .tab-btn.active {
            background-color: #d63384;
            color: white;
            box-shadow: 0 4px 8px rgba(214, 51, 132, 0.3);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .meditation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .meditation-card {
            background-color: #fff;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .meditation-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .card-img {
            height: 200px;
            overflow: hidden;
        }

        .card-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s;
        }

        .meditation-card:hover .card-img img {
            transform: scale(1.1);
        }

        .card-content {
            padding: 1.5rem;
        }

        .card-content h3 {
            color: #d63384;
            margin-bottom: 1rem;
        }

        .card-content p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .read-more {
            display: inline-block;
            color: #d63384;
            font-weight: bold;
            text-decoration: none;
            transition: color 0.3s;
        }

        .read-more:hover {
            color: #b52a6f;
        }

        .wellness-tips {
            background-color: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 3rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .wellness-tips h3 {
            color: #d63384;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
            text-align: center;
        }

        .wellness-tips ul {
            list-style-type: none;
            padding-left: 1rem;
        }

        .wellness-tips li {
            margin-bottom: 1rem;
            position: relative;
            padding-left: 2rem;
            line-height: 1.6;
        }

        .wellness-tips li:before {
            content: "\f058";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            color: #d63384;
            position: absolute;
            left: 0;
            top: 2px;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: white;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .modal-header h2 {
            color: #d63384;
            margin: 0;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #999;
        }

        .close-modal:hover {
            color: #d63384;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 1rem;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-primary {
            background-color: #d63384;
            color: white;
        }

        .btn-primary:hover {
            background-color: #b52a6f;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        /* Chatbot Button */
        .chatbot-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #d63384;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(214, 51, 132, 0.4);
            cursor: pointer;
            transition: all 0.3s;
            z-index: 999;
        }

        .chatbot-btn:hover {
            background-color: #b52a6f;
            transform: scale(1.1);
        }

        .chatbot-btn i {
            font-size: 1.5rem;
        }

        /* Footer Styles */
        .footer {
            background-color: #333;
            color: #fff;
            padding: 4rem 2rem 1rem;
        }

        .footer-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            gap: 2rem;
        }

        .footer-column {
            flex: 1 1 250px;
        }

        .footer-logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #d63384;
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .footer-logo i {
            margin-right: 10px;
        }

        .footer-column p {
            color: #ccc;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .social-icons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: #fff;
            transition: all 0.3s;
        }

        .social-icons a:hover {
            background-color: #d63384;
            transform: translateY(-3px);
        }

        .footer-column h3 {
            color: #d63384;
            margin-bottom: 1.2rem;
            font-size: 1.2rem;
        }

        .footer-links,
        .contact-info {
            list-style: none;
        }

        .footer-links li,
        .contact-info li {
            margin-bottom: 0.8rem;
        }

        .footer-links a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: #d63384;
        }

        .contact-info li {
            display: flex;
            align-items: flex-start;
            color: #ccc;
        }

        .contact-info i {
            margin-right: 10px;
            color: #d63384;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 1.5rem;
            margin-top: 3rem;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .footer-bottom p {
            color: #999;
        }

        .footer-bottom-links {
            display: flex;
            gap: 1.5rem;
        }

        .footer-bottom-links a {
            color: #999;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-bottom-links a:hover {
            color: #d63384;
        }

        /* Mobile Menu */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: #d63384;
            font-size: 1.5rem;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .page-header h1 {
                font-size: 2rem;
            }

            .nav-links {
                display: none;
                position: absolute;
                top: 70px;
                left: 0;
                width: 100%;
                background-color: white;
                flex-direction: column;
                padding: 1rem 0;
                box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
            }

            .nav-links.active {
                display: flex;
            }

            .nav-links a {
                padding: 1rem;
                text-align: center;
            }

            .mobile-menu-btn {
                display: block;
            }

            .footer-container {
                flex-direction: column;
            }

            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }

            .footer-bottom-links {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
<header class="navbar">
    <div class="logo">
        <i class="fas fa-baby-carriage"></i>
        Maternal and Child Health Care
    </div>
    <nav class="nav-links" id="navLinks">
        <a href="/home.html">Home</a>
        <a href="/pages/Preg/pregcare.html">Pregnancy Care</a>
        <a href="/pages/Preg/meditation.html" class="active">Meditation & Wellness</a>
        <a href="/pages/baby/baby-care.html">Baby Care</a>
        <a href="/pages/doctor/dashboard.html">Consult Doctor</a>
        <a href="/pages/Preg/schemes.html">Schemes</a>
        <a href="/pages/contact.html">Contact</a>
    </nav>
    <button class="book-now">Get Started</button>
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>
</header>

<section class="page-header">
    <div>
        <h1>Meditation & Wellness Guides</h1>
        <p>Safe and calming meditation practices for each stage of your pregnancy journey</p>
    </div>
</section>

<section class="content-section">
    <div class="container">
        <!-- Admin Panel -->
        <div id="admin-panel" class="admin-panel" style="display: none;">
            <div class="admin-header">
                <i class="fas fa-crown"></i>
                <span>Meditation Admin Panel</span>
            </div>
            <div class="admin-actions">
                <button class="btn btn-admin" onclick="openAddMeditationModal()">
                    <i class="fas fa-plus"></i> Add Meditation
                </button>
                <button class="btn btn-admin" onclick="openManageModal()">
                    <i class="fas fa-edit"></i> Manage Content
                </button>
                <button class="btn btn-admin" onclick="openTipsModal()">
                    <i class="fas fa-lightbulb"></i> Edit Wellness Tips
                </button>
            </div>
        </div>

        <h2 class="section-title">Wellness By Trimester</h2>

        <div class="meditation-tabs">
            <button class="tab-btn active" data-tab="first">
                First Trimester
            </button>
            <button class="tab-btn" data-tab="second">Second Trimester</button>
            <button class="tab-btn" data-tab="third">Third Trimester</button>
        </div>

        <div id="first" class="tab-content active">
            <div id="first-trimester-content" class="meditation-grid">
                <!-- Content will be loaded dynamically -->
            </div>
        </div>

        <div id="second" class="tab-content">
            <div id="second-trimester-content" class="meditation-grid">
                <!-- Content will be loaded dynamically -->
            </div>
        </div>

        <div id="third" class="tab-content">
            <div id="third-trimester-content" class="meditation-grid">
                <!-- Content will be loaded dynamically -->
            </div>
        </div>

        <div class="wellness-tips">
            <h3>Meditation & Wellness Guidelines</h3>
            <ul id="wellness-tips-list">
                <!-- Tips will be loaded dynamically -->
            </ul>
        </div>
    </div>
</section>

<!-- Add Meditation Modal -->
<div id="add-meditation-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Add New Meditation</h2>
            <button class="close-modal" onclick="closeModal('add-meditation-modal')">&times;</button>
        </div>
        <form id="add-meditation-form">
            <div class="form-group">
                <label for="meditation-title">Title</label>
                <input type="text" id="meditation-title" required>
            </div>
            <div class="form-group">
                <label for="meditation-trimester">Trimester</label>
                <select id="meditation-trimester" required>
                    <option value="first">First Trimester</option>
                    <option value="second">Second Trimester</option>
                    <option value="third">Third Trimester</option>
                </select>
            </div>
            <div class="form-group">
                <label for="meditation-description">Description</label>
                <textarea id="meditation-description" required></textarea>
            </div>
            <div class="form-group">
                <label for="meditation-image">Image URL</label>
                <input type="text" id="meditation-image" required>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('add-meditation-modal')">Cancel</button>
                <button type="submit" class="btn btn-primary">Add Meditation</button>
            </div>
        </form>
    </div>
</div>

<!-- Manage Content Modal -->
<div id="manage-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Manage Meditation Content</h2>
            <button class="close-modal" onclick="closeModal('manage-modal')">&times;</button>
        </div>
        <div id="manage-content">
            <h3>Select a trimester to manage:</h3>
            <div style="display: flex; gap: 1rem; margin: 1rem 0;">
                <button class="btn btn-admin" onclick="loadMeditationList('first')">First Trimester</button>
                <button class="btn btn-admin" onclick="loadMeditationList('second')">Second Trimester</button>
                <button class="btn btn-admin" onclick="loadMeditationList('third')">Third Trimester</button>
            </div>
            <div id="meditation-list"></div>
        </div>
    </div>
</div>

<!-- Edit Tips Modal -->
<div id="tips-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Edit Wellness Tips</h2>
            <button class="close-modal" onclick="closeModal('tips-modal')">&times;</button>
        </div>
        <div id="tips-list">
            <!-- Tips will be loaded here -->
        </div>
        <div style="margin-top: 1.5rem;">
            <button class="btn btn-admin" onclick="addNewTipField()">
                <i class="fas fa-plus"></i> Add New Tip
            </button>
        </div>
        <div class="form-actions" style="margin-top: 1.5rem;">
            <button type="button" class="btn btn-secondary" onclick="closeModal('tips-modal')">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="saveWellnessTips()">Save Tips</button>
        </div>
    </div>
</div>

<!-- Floating Chatbot Button -->
<div class="chatbot-btn" onclick="openChatbot()">
    <i class="fas fa-comment-dots"></i>
</div>

<!-- Footer -->
<footer class="footer">
    <div class="footer-container">
        <div class="footer-column">
            <div class="footer-logo">
                <i class="fas fa-baby-carriage"></i>
                Maternal and Child Health Care
            </div>
            <p>
                Your trusted companion for pregnancy and baby care, providing expert
                guidance and support every step of the way.
            </p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook-f"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
        </div>

        <div class="footer-column">
            <h3>Quick Links</h3>
            <ul class="footer-links">
                <li><a href="/home.html">Home</a></li>
                <li><a href="/pages/Preg/pregcare.html">Pregnancy Care</a></li>
                <li><a href="/pages/baby/baby-care.html">Baby Care</a></li>
                <li><a href="/pages/doctor/dashboard.html">Consult Doctor</a></li>
            </ul>
        </div>

        <div class="footer-column">
            <h3>Wellness Services</h3>
            <ul class="footer-links">
                <li><a href="/pages/Preg/nutrition.html">Nutrition Plans</a></li>
                <li><a href="/pages/Preg/exercise.html">Exercise Guides</a></li>
                <li><a href="/pages/Preg/meditation.html">Meditation & Wellness</a></li>
                <li><a href="/pages/Preg/schemes.html">Government Schemes</a></li>
            </ul>
        </div>

        <div class="footer-column">
            <h3>Contact Info</h3>
            <ul class="contact-info">
                <li><i class="fas fa-phone"></i> +****************</li>
                <li><i class="fas fa-envelope"></i> <EMAIL></li>
                <li><i class="fas fa-map-marker-alt"></i> 123 Health Street, Care City</li>
            </ul>
        </div>
    </div>

    <div class="footer-bottom">
        <p>&copy; 2024 Maternal and Child Health Care. All rights reserved.</p>
        <div class="footer-bottom-links">
            <a href="#">Privacy Policy</a>
            <a href="#">Terms of Service</a>
            <a href="#">Cookie Policy</a>
        </div>
    </div>
</footer>

<script>
    // Meditation data will be loaded from database
    let meditationData = {
        first: [],
        second: [],
        third: []
    };

    // Wellness tips will be loaded from admin-managed database
    const sampleWellnessTips = [];

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
        // Check if user is admin (in a real app, this would be done via authentication)
        const isAdmin = localStorage.getItem('meditation_admin') === 'true';
        if (isAdmin) {
            document.getElementById('admin-panel').style.display = 'block';
        }

        // Load meditation data
        loadMeditationData();
        
        // Load wellness tips
        loadWellnessTips();

        // Set up tab functionality
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');

                // Remove active class from all buttons and contents
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // Add active class to clicked button and corresponding content
                this.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });

        // Mobile menu functionality
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const navLinks = document.getElementById('navLinks');

        if (mobileMenuBtn && navLinks) {
            mobileMenuBtn.addEventListener('click', function() {
                navLinks.classList.toggle('active');
            });
        }

        // Get Started button functionality
        const getStartedBtn = document.querySelector('.book-now');
        if (getStartedBtn) {
            getStartedBtn.addEventListener('click', function() {
                window.location.href = '/signup';
            });
        }

        // Set up form submission
        document.getElementById('add-meditation-form').addEventListener('submit', function(e) {
            e.preventDefault();
            addNewMeditation();
        });
    });

    // Load meditation data from database
    function loadMeditationData() {
        // For now, show empty state - meditation management will be implemented later
        renderMeditationContent('first', []);
        renderMeditationContent('second', []);
        renderMeditationContent('third', []);
    }

    // Render meditation content for a specific trimester
    function renderMeditationContent(trimester, meditations) {
        const container = document.getElementById(`${trimester}-trimester-content`);
        if (!container) return;
        
        if (meditations.length === 0) {
            container.innerHTML = '<div class="empty-state"><p>No meditation content available for this trimester.</p></div>';
            return;
        }
        
        let html = '';
        meditations.forEach(meditation => {
            html += `
                <div class="meditation-card" data-id="${meditation.id}">
                    <div class="card-img">
                        <img src="${meditation.image}" alt="${meditation.title}" />
                    </div>
                    <div class="card-content">
                        <h3>${meditation.title}</h3>
                        <p>${meditation.description}</p>
                        <a href="#" class="read-more">Start meditation <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    // Load wellness tips from localStorage or use sample data
    function loadWellnessTips() {
        let tips = JSON.parse(localStorage.getItem('wellness_tips'));
        
        // If no data exists, use sample data and save it
        if (!tips) {
            tips = sampleWellnessTips;
            localStorage.setItem('wellness_tips', JSON.stringify(tips));
        }
        
        // Render wellness tips
        const tipsList = document.getElementById('wellness-tips-list');
        if (tipsList) {
            tipsList.innerHTML = '';
            tips.forEach(tip => {
                const li = document.createElement('li');
                li.textContent = tip;
                tipsList.appendChild(li);
            });
        }
    }

    // Modal functions
    function openAddMeditationModal() {
        document.getElementById('add-meditation-modal').style.display = 'flex';
    }

    function openManageModal() {
        document.getElementById('manage-modal').style.display = 'flex';
    }

    function openTipsModal() {
        // Load current tips into the modal
        const tips = JSON.parse(localStorage.getItem('wellness_tips')) || sampleWellnessTips;
        const tipsList = document.getElementById('tips-list');
        
        tipsList.innerHTML = '';
        tips.forEach((tip, index) => {
            const div = document.createElement('div');
            div.className = 'form-group';
            div.innerHTML = `
                <label>Tip ${index + 1}</label>
                <textarea class="tip-input" data-index="${index}">${tip}</textarea>
                <button type="button" class="btn btn-secondary" style="margin-top: 0.5rem;" onclick="removeTip(${index})">Remove</button>
            `;
            tipsList.appendChild(div);
        });
        
        document.getElementById('tips-modal').style.display = 'flex';
    }

    function closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }

    // Add new meditation
    function addNewMeditation() {
        const title = document.getElementById('meditation-title').value;
        const trimester = document.getElementById('meditation-trimester').value;
        const description = document.getElementById('meditation-description').value;
        const image = document.getElementById('meditation-image').value;
        
        // Get current data
        const meditations = JSON.parse(localStorage.getItem('meditation_data')) || { first: [], second: [], third: [] };
        
        // Create new meditation object
        const newMeditation = {
            id: Date.now(), // Use timestamp as ID
            title,
            description,
            image
        };
        
        // Add to the appropriate trimester
        meditations[trimester].push(newMeditation);
        
        // Save back to localStorage
        localStorage.setItem('meditation_data', JSON.stringify(meditations));
        
        // Update UI
        renderMeditationContent(trimester, meditations[trimester]);
        
        // Close modal and reset form
        closeModal('add-meditation-modal');
        document.getElementById('add-meditation-form').reset();
        
        alert('Meditation added successfully!');
    }

    // Load meditation list for management
    function loadMeditationList(trimester) {
        const meditations = JSON.parse(localStorage.getItem('meditation_data')) || { first: [], second: [], third: [] };
        const trimesterMeditations = meditations[trimester] || [];
        
        const meditationList = document.getElementById('meditation-list');
        meditationList.innerHTML = '';
        
        if (trimesterMeditations.length === 0) {
            meditationList.innerHTML = '<p>No meditation content for this trimester.</p>';
            return;
        }
        
        trimesterMeditations.forEach(meditation => {
            const div = document.createElement('div');
            div.style.padding = '1rem';
            div.style.borderBottom = '1px solid #eee';
            div.innerHTML = `
                <h4>${meditation.title}</h4>
                <p>${meditation.description}</p>
                <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem;">
                    <button class="btn btn-primary" onclick="editMeditation('${trimester}', ${meditation.id})">Edit</button>
                    <button class="btn btn-secondary" onclick="deleteMeditation('${trimester}', ${meditation.id})">Delete</button>
                </div>
            `;
            meditationList.appendChild(div);
        });
    }

    // Add new tip field
    function addNewTipField() {
        const tipsList = document.getElementById('tips-list');
        const newIndex = tipsList.children.length;
        
        const div = document.createElement('div');
        div.className = 'form-group';
        div.innerHTML = `
            <label>New Tip</label>
            <textarea class="tip-input" data-index="${newIndex}"></textarea>
            <button type="button" class="btn btn-secondary" style="margin-top: 0.5rem;" onclick="removeTip(${newIndex})">Remove</button>
        `;
        tipsList.appendChild(div);
    }

    // Remove tip
    function removeTip(index) {
        const tipsList = document.getElementById('tips-list');
        if (tipsList.children.length > 1) {
            tipsList.removeChild(tipsList.children[index]);
            // Reindex the remaining tips
            const inputs = tipsList.querySelectorAll('.tip-input');
            inputs.forEach((input, i) => {
                input.setAttribute('data-index', i);
            });
        } else {
            alert('You must have at least one wellness tip.');
        }
    }

    // Save wellness tips
    function saveWellnessTips() {
        const tipInputs = document.querySelectorAll('.tip-input');
        const tips = Array.from(tipInputs).map(input => input.value.trim()).filter(tip => tip !== '');
        
        localStorage.setItem('wellness_tips', JSON.stringify(tips));
        loadWellnessTips();
        closeModal('tips-modal');
        alert('Wellness tips saved successfully!');
    }

    // Chatbot functionality
    function openChatbot() {
        // Redirect to chat page or open chat modal
        window.location.href = '/pages/chat.html';
    }

    // For demo purposes - these functions would be implemented in a real application
    function editMeditation(trimester, id) {
        alert('Edit functionality would be implemented in a real application');
    }

    function deleteMeditation(trimester, id) {
        if (confirm('Are you sure you want to delete this meditation?')) {
            const meditations = JSON.parse(localStorage.getItem('meditation_data')) || { first: [], second: [], third: [] };
            meditations[trimester] = meditations[trimester].filter(m => m.id !== id);
            localStorage.setItem('meditation_data', JSON.stringify(meditations));
            loadMeditationList(trimester);
            renderMeditationContent(trimester, meditations[trimester]);
        }
    }

    // Enable admin mode for demonstration
    // In a real application, this would be controlled by user authentication
    localStorage.setItem('meditation_admin', 'true');
</script>
</body>
</html>