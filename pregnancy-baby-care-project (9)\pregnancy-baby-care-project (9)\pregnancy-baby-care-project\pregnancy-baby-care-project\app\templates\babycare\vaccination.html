<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Baby Vaccination Schedule - Maternal and Child Health Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Arial", sans-serif;
        }

        body {
            color: #333;
            background-color: #f9f6ff;
        }

        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 2rem;
            z-index: 1000;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
        }

        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #d63384;
            display: flex;
            align-items: center;
        }

        .logo i {
            margin-right: 10px;
            font-size: 1.5rem;
        }

        .nav-links a {
            margin: 0 1rem;
            color: #333;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
            position: relative;
        }

        .nav-links a:hover {
            color: #d63384;
        }

        .nav-links a::after {
            content: "";
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 0;
            background-color: #d63384;
            transition: width 0.3s;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .book-now {
            background-color: #d63384;
            border: none;
            padding: 0.7rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            color: white;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(214, 51, 132, 0.3);
        }

        .book-now:hover {
            background-color: #b52a6f;
            transform: translateY(-2px);
        }

        .page-header {
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
            url("https://images.unsplash.com/photo-1534367507877-0edd93bd013b?auto=format&fit=crop&w=1470&q=80")
            no-repeat center center/cover;
            height: 50vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            margin-top: 70px;
        }

        .page-header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .page-header p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .content-section {
            padding: 5rem 2rem;
            background-color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            margin-bottom: 2rem;
            color: #d63384;
            font-size: 2.5rem;
        }

        .intro-text {
            text-align: center;
            max-width: 800px;
            margin: 0 auto 3rem;
            line-height: 1.6;
            color: #555;
        }

        /* Baby Profile Section */
        .baby-profile {
            background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 3rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .baby-profile h3 {
            color: #333;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
        }

        .baby-form {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .baby-form input {
            padding: 0.8rem 1rem;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 1rem;
            min-width: 200px;
        }

        .baby-form button {
            background: #d63384;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .baby-form button:hover {
            background: #b52a6f;
        }

        .baby-info {
            display: none;
            background: rgba(255, 255, 255, 0.9);
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 1rem;
        }

        .baby-info.active {
            display: block;
        }

        /* Timeline Section */
        .timeline {
            position: relative;
            max-width: 1000px;
            margin: 0 auto;
        }

        .timeline::after {
            content: '';
            position: absolute;
            width: 6px;
            background-color: #d63384;
            top: 0;
            bottom: 0;
            left: 50%;
            margin-left: -3px;
        }

        .timeline-item {
            padding: 10px 40px;
            position: relative;
            width: 50%;
            box-sizing: border-box;
        }

        .timeline-item:nth-child(odd) {
            left: 0;
        }

        .timeline-item:nth-child(even) {
            left: 50%;
        }

        .timeline-item::after {
            content: '';
            position: absolute;
            width: 25px;
            height: 25px;
            background-color: white;
            border: 4px solid #d63384;
            border-radius: 50%;
            top: 15px;
            z-index: 1;
        }

        .timeline-item:nth-child(odd)::after {
            right: -13px;
        }

        .timeline-item:nth-child(even)::after {
            left: -13px;
        }

        .timeline-content {
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .timeline-content h3 {
            color: #d63384;
            margin-bottom: 1rem;
        }

        .timeline-content ul {
            list-style-type: none;
            margin-bottom: 1rem;
        }

        .timeline-content li {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .timeline-content li i {
            margin-right: 10px;
            color: #d63384;
        }

        .vaccine-status {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 1rem;
        }

        .status-btn {
            background: #d63384;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s;
        }

        .status-btn.completed {
            background: #4caf50;
        }

        .status-btn:hover:not(.completed) {
            background: #b52a6f;
        }

        /* Vaccination Table */
        .vaccine-table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .vaccine-table th, .vaccine-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .vaccine-table th {
            background-color: #d63384;
            color: white;
            font-weight: 500;
        }

        .vaccine-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .vaccine-table tr:hover {
            background-color: #f1f3f5;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-pending {
            background-color: #ff9800;
        }

        .status-completed {
            background-color: #4caf50;
        }

        /* Info Cards */
        .info-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }

        .info-card:hover {
            transform: translateY(-5px);
        }

        .info-card h3 {
            color: #d63384;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .info-card h3 i {
            margin-right: 10px;
        }

        .info-card p {
            color: #555;
            line-height: 1.6;
        }

        /* Footer Styles */
        .footer {
            background-color: #333;
            color: #fff;
            padding: 4rem 2rem 1rem;
        }

        .footer-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            gap: 2rem;
        }

        .footer-column {
            flex: 1 1 250px;
        }

        .footer-logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #d63384;
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .footer-logo i {
            margin-right: 10px;
        }

        .footer-column p {
            color: #ccc;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .social-icons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: #fff;
            transition: all 0.3s;
        }

        .social-icons a:hover {
            background-color: #d63384;
            transform: translateY(-3px);
        }

        .footer-column h3 {
            color: #d63384;
            margin-bottom: 1.2rem;
            font-size: 1.2rem;
        }

        .footer-links,
        .contact-info {
            list-style: none;
        }

        .footer-links li,
        .contact-info li {
            margin-bottom: 0.8rem;
        }

        .footer-links a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: #d63384;
        }

        .contact-info li {
            display: flex;
            align-items: flex-start;
            color: #ccc;
        }

        .contact-info i {
            margin-right: 10px;
            color: #d63384;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 1.5rem;
            margin-top: 3rem;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 1rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .footer-bottom p {
            color: #999;
        }

        .footer-bottom-links {
            display: flex;
            gap: 1.5rem;
        }

        .footer-bottom-links a {
            color: #999;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-bottom-links a:hover {
            color: #d63384;
        }

        /* Mobile Menu */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: #d63384;
            font-size: 1.5rem;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .page-header h1 {
                font-size: 2rem;
            }

            .nav-links {
                display: none;
                position: absolute;
                top: 70px;
                left: 0;
                width: 100%;
                background-color: white;
                flex-direction: column;
                padding: 1rem 0;
                box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
            }

            .nav-links.active {
                display: flex;
            }

            .nav-links a {
                padding: 1rem;
                text-align: center;
            }

            .mobile-menu-btn {
                display: block;
            }

            .timeline::after {
                left: 31px;
            }

            .timeline-item {
                width: 100%;
                padding-left: 70px;
                padding-right: 25px;
            }

            .timeline-item:nth-child(even) {
                left: 0;
            }

            .timeline-item:nth-child(odd)::after, 
            .timeline-item:nth-child(even)::after {
                left: 18px;
            }

            .footer-container {
                flex-direction: column;
            }

            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }

            .footer-bottom-links {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
<header class="navbar">
    <div class="logo">
        <i class="fas fa-baby-carriage"></i>
        Maternal and Child Health Care
    </div>
    <nav class="nav-links" id="navLinks">
        <a href="/home.html">Home</a>
        <a href="/pages/Preg/pregcare.html">Pregnancy Care</a>
        <a href="/pages/Preg/meditation.html">Meditation & Wellness</a>
        <a href="/pages/baby/baby-care.html">Baby Care</a>
        <a href="/pages/baby/vaccination.html" class="active">Vaccination Schedule</a>
        <a href="/pages/doctor/dashboard.html">Consult Doctor</a>
        <a href="/pages/Preg/schemes.html">Schemes</a>
        <a href="/pages/contact.html">Contact</a>
    </nav>
    <button class="book-now">Get Started</button>
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>
</header>

<section class="page-header">
    <div>
        <h1>Baby Vaccination Schedule</h1>
        <p>Track and manage your baby's immunization journey with our comprehensive vaccination guide</p>
    </div>
</section>

<section class="content-section">
    <div class="container">
        <h2 class="section-title">Vaccination Timeline</h2>
        <p class="intro-text">
            Vaccinations are essential for protecting your baby from serious diseases. Follow this schedule to ensure your child receives all recommended vaccines at the right time.
        </p>

        <!-- Baby Profile Section -->
        <div class="baby-profile">
            <h3><i class="fas fa-baby"></i> Create Baby Profile</h3>
            <div class="baby-form">
                <input type="text" id="baby-name" placeholder="Baby's Name">
                <input type="date" id="baby-dob">
                <button onclick="createBabyProfile()">Save Profile</button>
            </div>
            <div class="baby-info" id="baby-info">
                <h4>Baby Profile</h4>
                <p>Name: <span id="profile-name"></span></p>
                <p>Date of Birth: <span id="profile-dob"></span></p>
                <p>Age: <span id="profile-age"></span></p>
            </div>
        </div>

        <!-- Vaccination Timeline -->
        <div class="timeline">
            <!-- Birth -->
            <div class="timeline-item">
                <div class="timeline-content">
                    <h3>At Birth</h3>
                    <ul>
                        <li><i class="fas fa-syringe"></i> Hepatitis B (1st dose)</li>
                        <li><i class="fas fa-syringe"></i> BCG (Tuberculosis)</li>
                        <li><i class="fas fa-syringe"></i> OPV (Oral Polio Vaccine)</li>
                    </ul>
                    <div class="vaccine-status">
                        <span>Status: <span class="status-indicator status-pending"></span> Pending</span>
                        <button class="status-btn" onclick="markVaccineCompleted(this)">Mark as Completed</button>
                    </div>
                </div>
            </div>

            <!-- 6 Weeks -->
            <div class="timeline-item">
                <div class="timeline-content">
                    <h3>6 Weeks</h3>
                    <ul>
                        <li><i class="fas fa-syringe"></i> DTP (1st dose)</li>
                        <li><i class="fas fa-syringe"></i> Hepatitis B (2nd dose)</li>
                        <li><i class="fas fa-syringe"></i> Hib (1st dose)</li>
                        <li><i class="fas fa-syringe"></i> IPV (1st dose)</li>
                        <li><i class="fas fa-syringe"></i> PCV (1st dose)</li>
                        <li><i class="fas fa-syringe"></i> Rotavirus (1st dose)</li>
                    </ul>
                    <div class="vaccine-status">
                        <span>Status: <span class="status-indicator status-pending"></span> Pending</span>
                        <button class="status-btn" onclick="markVaccineCompleted(this)">Mark as Completed</button>
                    </div>
                </div>
            </div>

            <!-- 10 Weeks -->
            <div class="timeline-item">
                <div class="timeline-content">
                    <h3>10 Weeks</h3>
                    <ul>
                        <li><i class="fas fa-syringe"></i> DTP (2nd dose)</li>
                        <li><i class="fas fa-syringe"></i> Hib (2nd dose)</li>
                        <li><i class="fas fa-syringe"></i> IPV (2nd dose)</li>
                        <li><i class="fas fa-syringe"></i> PCV (2nd dose)</li>
                        <li><i class="fas fa-syringe"></i> Rotavirus (2nd dose)</li>
                    </ul>
                    <div class="vaccine-status">
                        <span>Status: <span class="status-indicator status-pending"></span> Pending</span>
                        <button class="status-btn" onclick="markVaccineCompleted(this)">Mark as Completed</button>
                    </div>
                </div>
            </div>

            <!-- 14 Weeks -->
            <div class="timeline-item">
                <div class="timeline-content">
                    <h3>14 Weeks</h3>
                    <ul>
                        <li><i class="fas fa-syringe"></i> DTP (3rd dose)</li>
                        <li><i class="fas fa-syringe"></i> Hib (3rd dose)</li>
                        <li><i class="fas fa-syringe"></i> IPV (3rd dose)</li>
                        <li><i class="fas fa-syringe"></i> PCV (3rd dose)</li>
                        <li><i class="fas fa-syringe"></i> Rotavirus (3rd dose)</li>
                    </ul>
                    <div class="vaccine-status">
                        <span>Status: <span class="status-indicator status-pending"></span> Pending</span>
                        <button class="status-btn" onclick="markVaccineCompleted(this)">Mark as Completed</button>
                    </div>
                </div>
            </div>

            <!-- 6 Months -->
            <div class="timeline-item">
                <div class="timeline-content">
                    <h3>6 Months</h3>
                    <ul>
                        <li><i class="fas fa-syringe"></i> Hepatitis B (3rd dose)</li>
                        <li><i class="fas fa-syringe"></i> Influenza (Yearly)</li>
                    </ul>
                    <div class="vaccine-status">
                        <span>Status: <span class="status-indicator status-pending"></span> Pending</span>
                        <button class="status-btn" onclick="markVaccineCompleted(this)">Mark as Completed</button>
                    </div>
                </div>
            </div>

            <!-- 9 Months -->
            <div class="timeline-item">
                <div class="timeline-content">
                    <h3>9 Months</h3>
                    <ul>
                        <li><i class="fas fa-syringe"></i> MMR (1st dose)</li>
                        <li><i class="fas fa-syringe"></i> Typhoid Conjugate Vaccine</li>
                    </ul>
                    <div class="vaccine-status">
                        <span>Status: <span class="status-indicator status-pending"></span> Pending</span>
                        <button class="status-btn" onclick="markVaccineCompleted(this)">Mark as Completed</button>
                    </div>
                </div>
            </div>

            <!-- 12 Months -->
            <div class="timeline-item">
                <div class="timeline-content">
                    <h3>12 Months</h3>
                    <ul>
                        <li><i class="fas fa-syringe"></i> Hepatitis A (1st dose)</li>
                        <li><i class="fas fa-syringe"></i> PCV Booster</li>
                    </ul>
                    <div class="vaccine-status">
                        <span>Status: <span class="status-indicator status-pending"></span> Pending</span>
                        <button class="status-btn" onclick="markVaccineCompleted(this)">Mark as Completed</button>
                    </div>
                </div>
            </div>

            <!-- 15 Months -->
            <div class="timeline-item">
                <div class="timeline-content">
                    <h3>15 Months</h3>
                    <ul>
                        <li><i class="fas fa-syringe"></i> MMR (2nd dose)</li>
                        <li><i class="fas fa-syringe"></i> Varicella (Chickenpox)</li>
                    </ul>
                    <div class="vaccine-status">
                        <span>Status: <span class="status-indicator status-pending"></span> Pending</span>
                        <button class="status-btn" onclick="markVaccineCompleted(this)">Mark as Completed</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vaccination Table -->
        <h2 style="text-align: center; margin: 3rem 0 1.5rem; color: #d63384;">Detailed Vaccination Schedule</h2>
        <table class="vaccine-table">
            <thead>
                <tr>
                    <th>Vaccine</th>
                    <th>Recommended Age</th>
                    <th>Doses</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Hepatitis B</td>
                    <td>Birth, 1-2 months, 6-18 months</td>
                    <td>3</td>
                    <td><span class="status-indicator status-pending"></span> Pending</td>
                </tr>
                <tr>
                    <td>Rotavirus</td>
                    <td>2 months, 4 months, 6 months</td>
                    <td>2-3</td>
                    <td><span class="status-indicator status-pending"></span> Pending</td>
                </tr>
                <tr>
                    <td>DTaP</td>
                    <td>2 months, 4 months, 6 months, 15-18 months, 4-6 years</td>
                    <td>5</td>
                    <td><span class="status-indicator status-pending"></span> Pending</td>
                </tr>
                <tr>
                    <td>Hib</td>
                    <td>2 months, 4 months, 6 months, 12-15 months</td>
                    <td>3-4</td>
                    <td><span class="status-indicator status-pending"></span> Pending</td>
                </tr>
                <tr>
                    <td>Pneumococcal</td>
                    <td>2 months, 4 months, 6 months, 12-15 months</td>
                    <td>4</td>
                    <td><span class="status-indicator status-pending"></span> Pending</td>
                </tr>
                <tr>
                    <td>IPV</td>
                    <td>2 months, 4 months, 6-18 months, 4-6 years</td>
                    <td>4</td>
                    <td><span class="status-indicator status-pending"></span> Pending</td>
                </tr>
                <tr>
                    <td>Influenza</td>
                    <td>6 months, then yearly</td>
                    <td>1 yearly</td>
                    <td><span class="status-indicator status-pending"></span> Pending</td>
                </tr>
                <tr>
                    <td>MMR</td>
                    <td>12-15 months, 4-6 years</td>
                    <td>2</td>
                    <td><span class="status-indicator status-pending"></span> Pending</td>
                </tr>
                <tr>
                    <td>Varicella</td>
                    <td>12-15 months, 4-6 years</td>
                    <td>2</td>
                    <td><span class="status-indicator status-pending"></span> Pending</td>
                </tr>
                <tr>
                    <td>Hepatitis A</td>
                    <td>12-23 months, 6 months after first dose</td>
                    <td>2</td>
                    <td><span class="status-indicator status-pending"></span> Pending</td>
                </tr>
            </tbody>
        </table>

        <!-- Information Cards -->
        <div class="info-cards">
            <div class="info-card">
                <h3><i class="fas fa-info-circle"></i> Why Vaccinate?</h3>
                <p>Vaccines protect your baby from serious diseases that can cause complications or even death. They help develop immunity by imitating an infection, but don't cause illness.</p>
            </div>
            <div class="info-card">
                <h3><i class="fas fa-clock"></i> Vaccine Safety</h3>
                <p>Vaccines go through rigorous testing and continuous monitoring to ensure they are safe and effective for your child. Side effects are usually mild and temporary.</p>
            </div>
            <div class="info-card">
                <h3><i class="fas fa-calendar-alt"></i> Staying on Schedule</h3>
                <p>Following the recommended vaccination schedule ensures your child gets protection at the right time, before they're exposed to potentially life-threatening diseases.</p>
            </div>
        </div>
    </div>
</section>

<!-- Footer -->
<footer class="footer">
    <div class="footer-container">
        <div class="footer-column">
            <div class="footer-logo">
                <i class="fas fa-baby-carriage"></i>
                Maternal and Child Health Care
            </div>
            <p>
                Your trusted companion for pregnancy and baby care, providing expert
                guidance and support every step of the way.
            </p>
            <div class="social-icons">
                <a href="#"><i class="fab fa-facebook-f"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
        </div>

        <div class="footer-column">
            <h3>Quick Links</h3>
            <ul class="footer-links">
                <li><a href="/home.html">Home</a></li>
                <li><a href="/pages/Preg/pregcare.html">Pregnancy Care</a></li>
                <li><a href="/pages/baby/baby-care.html">Baby Care</a></li>
                <li><a href="/pages/doctor/dashboard.html">Consult Doctor</a></li>
            </ul>
        </div>

        <div class="footer-column">
            <h3>Baby Care</h3>
            <ul class="footer-links">
                <li><a href="/pages/baby/feeding.html">Feeding Guide</a></li>
                <li><a href="/pages/baby/development.html">Development Milestones</a></li>
                <li><a href="/pages/baby/vaccination.html">Vaccination Schedule</a></li>
            </ul>
        </div>

        <div class="footer-column">
            <h3>Contact Info</h3>
            <ul class="contact-info">
                <li><i class="fas fa-phone"></i> +****************</li>
                <li><i class="fas fa-envelope"></i> <EMAIL></li>
                <li><i class="fas fa-map-marker-alt"></i> 123 Health Street, Care City</li>
            </ul>
        </div>
    </div>

    <div class="footer-bottom">
        <p>&copy; 2024 Maternal and Child Health Care. All rights reserved.</p>
        <div class="footer-bottom-links">
            <a href="#">Privacy Policy</a>
            <a href="#">Terms of Service</a>
            <a href="#">Cookie Policy</a>
        </div>
    </div>
</footer>

<script>
    // Mobile menu functionality
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const navLinks = document.getElementById('navLinks');

    if (mobileMenuBtn && navLinks) {
        mobileMenuBtn.addEventListener('click', function() {
            navLinks.classList.toggle('active');
        });
    }

    // Get Started button functionality
    const getStartedBtn = document.querySelector('.book-now');
    if (getStartedBtn) {
        getStartedBtn.addEventListener('click', function() {
            window.location.href = '/signup';
        });
    }

    // Baby profile creation
    function createBabyProfile() {
        const babyName = document.getElementById('baby-name').value;
        const babyDOB = document.getElementById('baby-dob').value;
        
        if (!babyName || !babyDOB) {
            alert('Please enter both baby name and date of birth');
            return;
        }
        
        // Calculate age
        const dob = new Date(babyDOB);
        const today = new Date();
        let age = today.getFullYear() - dob.getFullYear();
        const monthDiff = today.getMonth() - dob.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
            age--;
        }
        
        let ageText = '';
        if (age === 0) {
            const monthAge = today.getMonth() - dob.getMonth();
            ageText = `${monthAge} month(s)`;
        } else {
            ageText = `${age} year(s)`;
        }
        
        // Update profile display
        document.getElementById('profile-name').textContent = babyName;
        document.getElementById('profile-dob').textContent = new Date(babyDOB).toLocaleDateString();
        document.getElementById('profile-age').textContent = ageText;
        
        // Show profile
        document.getElementById('baby-info').classList.add('active');
        
        // Save to localStorage
        localStorage.setItem('babyProfile', JSON.stringify({
            name: babyName,
            dob: babyDOB
        }));
    }

    // Mark vaccine as completed
    function markVaccineCompleted(button) {
        const statusElement = button.previousElementSibling;
        statusElement.innerHTML = '<span class="status-indicator status-completed"></span> Completed';
        button.textContent = 'Completed';
        button.classList.add('completed');
        button.onclick = null;
    }

    // Load baby profile if exists
    document.addEventListener('DOMContentLoaded', function() {
        const savedProfile = localStorage.getItem('babyProfile');
        if (savedProfile) {
            const profile = JSON.parse(savedProfile);
            document.getElementById('baby-name').value = profile.name;
            document.getElementById('baby-dob').value = profile.dob;
            createBabyProfile();
        }
    });
</script>
</body>
</html>