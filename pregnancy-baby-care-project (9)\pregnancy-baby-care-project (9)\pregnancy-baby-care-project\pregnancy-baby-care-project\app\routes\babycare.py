from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from datetime import datetime, date, timedelta
from app.data_manager import DataManager
import uuid
import json

babycare_bp = Blueprint('babycare', __name__, url_prefix='/babycare')

def login_required(f):
    """Simple login required decorator"""
    def decorated_function(*args, **kwargs):
        if not session.get('user_id'):
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def admin_required(f):
    """Decorator to require admin privileges"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('auth.login'))

        user = DataManager.get_user_by_id(session['user_id'])
        if not user or user['role'] != 'admin':
            return jsonify({'error': 'Admin privileges required'}), 403
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def validate_baby_access(baby_id, user_id):
    """Validate that user has access to baby"""
    baby = DataManager.get_baby_by_id(baby_id)
    if not baby:
        return None, "Baby not found"

    user = DataManager.get_user_by_id(user_id)
    if baby['parent_id'] != user_id and (not user or user['role'] != 'admin'):
        return None, "Access denied"

    return baby, None

# Page Routes

@babycare_bp.route('/')
@login_required
def index():
    """Baby care main page"""
    return render_template('babycare/babycare.html')

@babycare_bp.route('/vaccination')
@login_required
def vaccination():
    """Vaccination tracking page"""
    return render_template('babycare/vaccination.html')

@babycare_bp.route('/nutrition')
@login_required
def nutrition():
    """Nutrition tracking page"""
    return render_template('babycare/nutrition.html')



@babycare_bp.route('/growth-tracker')
@login_required
def growth_tracker():
    """Growth tracking page"""
    return render_template('babycare/growth_tracker.html')

@babycare_bp.route('/schemes')
@login_required
def schemes():
    """Government schemes page"""
    return render_template('babycare/schemes.html')

@babycare_bp.route('/unique_id')
@login_required
def unique_id():
    """Unique ID page"""
    return render_template('babycare/unique_id.html')

# Unique ID API Endpoints

@babycare_bp.route('/api/unique-id/validate/<unique_id>')
@login_required
def validate_unique_id(unique_id):
    """Validate a unique ID"""
    try:
        from app.data_manager import DataManager

        is_valid = DataManager.validate_unique_id(unique_id)
        baby_info = None

        if is_valid:
            baby_info = DataManager.get_baby_by_unique_id(unique_id)

        return jsonify({
            'success': True,
            'valid': is_valid,
            'baby_info': baby_info,
            'message': 'Valid unique ID' if is_valid else 'Invalid unique ID'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@babycare_bp.route('/api/unique-id/lookup/<unique_id>')
@login_required
def lookup_unique_id(unique_id):
    """Lookup baby information by unique ID"""
    try:
        from app.data_manager import DataManager

        baby_info = DataManager.get_baby_with_parent_info(unique_id)

        if not baby_info:
            return jsonify({
                'success': False,
                'error': 'Baby not found with this unique ID'
            }), 404

        # Check if user has permission to view this baby
        user_data = DataManager.get_user_by_id(session['user_id'])
        if baby_info['parent_email'] != user_data['email'] and user_data['role'] != 'admin':
            return jsonify({
                'success': False,
                'error': 'Access denied'
            }), 403

        return jsonify({
            'success': True,
            'baby': baby_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@babycare_bp.route('/api/unique-id/regenerate/<int:baby_id>', methods=['POST'])
@login_required
def regenerate_unique_id(baby_id):
    """Regenerate unique ID for a baby"""
    try:
        from app.data_manager import DataManager

        new_unique_id, error = DataManager.regenerate_baby_unique_id(baby_id, session['user_id'])

        if error:
            return jsonify({
                'success': False,
                'error': error
            }), 400 if 'Access denied' in error else 404

        return jsonify({
            'success': True,
            'new_unique_id': new_unique_id,
            'message': 'Unique ID regenerated successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@babycare_bp.route('/api/unique-id/my-babies')
@login_required
def get_my_babies_with_ids():
    """Get all babies for current user with their unique IDs"""
    try:
        from app.data_manager import DataManager

        user_data = DataManager.get_user_by_id(session['user_id'])
        babies = DataManager.get_babies_for_user(session['user_id'], user_data['role'] == 'admin')

        return jsonify({
            'success': True,
            'babies': babies,
            'count': len(babies)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@babycare_bp.route('/api/unique-id/qr-code/<unique_id>')
@login_required
def generate_qr_code(unique_id):
    """Generate QR code for unique ID"""
    try:
        from app.data_manager import DataManager
        import qrcode
        import io
        import base64

        # Validate the unique ID first
        baby_info = DataManager.get_baby_by_unique_id(unique_id)
        if not baby_info:
            return jsonify({
                'success': False,
                'error': 'Invalid unique ID'
            }), 404

        # Check permission
        user_data = DataManager.get_user_by_id(session['user_id'])
        if baby_info['parent_id'] != session['user_id'] and user_data['role'] != 'admin':
            return jsonify({
                'success': False,
                'error': 'Access denied'
            }), 403

        # Create QR code data
        qr_data = {
            'unique_id': unique_id,
            'baby_name': baby_info['name'],
            'verification_url': f"{request.host_url}babycare/api/unique-id/validate/{unique_id}"
        }

        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(str(qr_data))
        qr.make(fit=True)

        # Create QR code image
        qr_img = qr.make_image(fill_color="black", back_color="white")

        # Convert to base64
        img_buffer = io.BytesIO()
        qr_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        qr_base64 = base64.b64encode(img_buffer.getvalue()).decode()

        return jsonify({
            'success': True,
            'qr_code': f"data:image/png;base64,{qr_base64}",
            'qr_data': qr_data
        })

    except ImportError:
        # Fallback if qrcode library is not available
        return jsonify({
            'success': False,
            'error': 'QR code generation not available. Please install qrcode library.',
            'fallback_url': f"{request.host_url}babycare/api/unique-id/validate/{unique_id}"
        }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@babycare_bp.route('/verify-id')
def verify_id_page():
    """Public ID verification page"""
    return render_template('babycare/verify_id.html')

@babycare_bp.route('/api/verify-id', methods=['POST'])
def verify_id_api():
    """Public API to verify unique ID"""
    try:
        data = request.get_json()
        unique_id = data.get('unique_id', '').strip()

        if not unique_id:
            return jsonify({
                'success': False,
                'error': 'Unique ID is required'
            }), 400

        from app.data_manager import DataManager

        # Get baby information (limited for privacy)
        baby_info = DataManager.get_baby_by_unique_id(unique_id)

        if not baby_info:
            return jsonify({
                'success': False,
                'valid': False,
                'message': 'Invalid unique ID'
            })

        # Return limited information for verification
        verification_info = {
            'valid': True,
            'baby_name': baby_info['name'],
            'birth_date': baby_info['birth_date'],
            'gender': baby_info['gender'],
            'unique_id': baby_info['unique_id'],
            'created_at': baby_info['created_at']
        }

        return jsonify({
            'success': True,
            'valid': True,
            'baby': verification_info,
            'message': 'Valid unique ID verified successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@babycare_bp.route('/api/unique-id/history/<int:baby_id>')
@login_required
def get_unique_id_history(baby_id):
    """Get unique ID history for a baby"""
    try:
        from app.data_manager import DataManager

        # Check if user has permission to view this baby
        baby_info = DataManager.get_baby_by_id(baby_id)
        if not baby_info:
            return jsonify({
                'success': False,
                'error': 'Baby not found'
            }), 404

        user_data = DataManager.get_user_by_id(session['user_id'])
        if baby_info['parent_id'] != session['user_id'] and user_data['role'] != 'admin':
            return jsonify({
                'success': False,
                'error': 'Access denied'
            }), 403

        history = DataManager.get_unique_id_history(baby_id)

        return jsonify({
            'success': True,
            'history': history,
            'baby_name': baby_info['name']
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@babycare_bp.route('/api/admin/unique-ids')
@login_required
def admin_get_all_unique_ids():
    """Admin endpoint to get all unique IDs"""
    try:
        from app.data_manager import DataManager

        # Check if user is admin
        user_data = DataManager.get_user_by_id(session['user_id'])
        if user_data['role'] != 'admin':
            return jsonify({
                'success': False,
                'error': 'Admin access required'
            }), 403

        all_babies = DataManager.get_all_unique_ids_for_admin()

        return jsonify({
            'success': True,
            'babies': all_babies,
            'count': len(all_babies)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@babycare_bp.route('/chatbot')
@login_required
def chatbot():
    """Chatbot page"""
    return render_template('babycare/chatbot.html')

@babycare_bp.route('/appointments')
@login_required
def appointments():
    """Appointments page"""
    return render_template('babycare/appointments.html')

# API Routes

@babycare_bp.route('/api/babies', methods=['GET', 'POST'])
@login_required
def babies_api():
    """Get all babies for current user or create a new baby"""
    if request.method == 'GET':
        try:
            # Get babies based on user role
            if current_user.is_admin():
                babies = Baby.query.filter_by(is_active=True).all()
            else:
                babies = Baby.query.filter_by(parent_id=current_user.id, is_active=True).all()

            babies_data = []
            for baby in babies:
                baby_dict = baby.to_dict(include_relationships=True)
                babies_data.append(baby_dict)

            return jsonify({
                'success': True,
                'babies': babies_data,
                'count': len(babies_data)
            })

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    elif request.method == 'POST':
        try:
            data = request.get_json()

            # Validate required fields
            required_fields = ['name', 'birth_date', 'gender']
            for field in required_fields:
                if field not in data or not data[field]:
                    return jsonify({'error': f'Missing required field: {field}'}), 400

            # Validate name
            if len(data['name'].strip()) < 2:
                return jsonify({'error': 'Baby name must be at least 2 characters long'}), 400

            # Validate gender
            if data['gender'].lower() not in ['male', 'female', 'other']:
                return jsonify({'error': 'Gender must be male, female, or other'}), 400

            # Parse and validate birth date
            try:
                birth_date = datetime.strptime(data['birth_date'], '%Y-%m-%d').date()
                if birth_date > date.today():
                    return jsonify({'error': 'Birth date cannot be in the future'}), 400
                if birth_date < date.today() - timedelta(days=365*5):  # Max 5 years old
                    return jsonify({'error': 'Birth date cannot be more than 5 years ago'}), 400
            except ValueError:
                return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400

            # Validate weight and height if provided
            if data.get('weight_at_birth'):
                try:
                    weight = float(data['weight_at_birth'])
                    if weight <= 0 or weight > 10:  # Reasonable range for baby weight in kg
                        return jsonify({'error': 'Weight at birth must be between 0 and 10 kg'}), 400
                except ValueError:
                    return jsonify({'error': 'Invalid weight format'}), 400

            if data.get('height_at_birth'):
                try:
                    height = float(data['height_at_birth'])
                    if height <= 0 or height > 100:  # Reasonable range for baby height in cm
                        return jsonify({'error': 'Height at birth must be between 0 and 100 cm'}), 400
                except ValueError:
                    return jsonify({'error': 'Invalid height format'}), 400

            # Generate unique ID
            unique_id = Baby.generate_unique_id()

            # Create new baby
            baby = Baby(
                name=data['name'].strip(),
                birth_date=birth_date,
                gender=data['gender'].lower(),
                weight_at_birth=data.get('weight_at_birth'),
                height_at_birth=data.get('height_at_birth'),
                blood_type=data.get('blood_type'),
                parent_id=current_user.id,
                unique_id=unique_id,
                notes=data.get('notes', '').strip()
            )

            db.session.add(baby)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'Baby registered successfully',
                'baby': baby.to_dict(include_relationships=True)
            }), 201

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'Failed to register baby: {str(e)}'}), 500

@babycare_bp.route('/api/babies/<int:baby_id>', methods=['GET', 'PUT', 'DELETE'])
@login_required
def baby_detail_api(baby_id):
    """Get, update, or delete a specific baby"""
    try:
        baby, error = validate_baby_access(baby_id, current_user.id)
        if error:
            return jsonify({'error': error}), 404 if 'not found' in error else 403

        if request.method == 'GET':
            return jsonify({
                'success': True,
                'baby': baby.to_dict(include_relationships=True)
            })

        elif request.method == 'PUT':
            data = request.get_json()

            # Validate and update baby information
            if 'name' in data:
                if len(data['name'].strip()) < 2:
                    return jsonify({'error': 'Baby name must be at least 2 characters long'}), 400
                baby.name = data['name'].strip()

            if 'birth_date' in data:
                try:
                    birth_date = datetime.strptime(data['birth_date'], '%Y-%m-%d').date()
                    if birth_date > date.today():
                        return jsonify({'error': 'Birth date cannot be in the future'}), 400
                    baby.birth_date = birth_date
                except ValueError:
                    return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400

            if 'gender' in data:
                if data['gender'].lower() not in ['male', 'female', 'other']:
                    return jsonify({'error': 'Gender must be male, female, or other'}), 400
                baby.gender = data['gender'].lower()

            if 'weight_at_birth' in data:
                try:
                    weight = float(data['weight_at_birth'])
                    if weight <= 0 or weight > 10:
                        return jsonify({'error': 'Weight at birth must be between 0 and 10 kg'}), 400
                    baby.weight_at_birth = weight
                except ValueError:
                    return jsonify({'error': 'Invalid weight format'}), 400

            if 'height_at_birth' in data:
                try:
                    height = float(data['height_at_birth'])
                    if height <= 0 or height > 100:
                        return jsonify({'error': 'Height at birth must be between 0 and 100 cm'}), 400
                    baby.height_at_birth = height
                except ValueError:
                    return jsonify({'error': 'Invalid height format'}), 400

            if 'blood_type' in data:
                baby.blood_type = data['blood_type']

            if 'notes' in data:
                baby.notes = data['notes'].strip()

            baby.updated_at = datetime.utcnow()
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'Baby information updated successfully',
                'baby': baby.to_dict(include_relationships=True)
            })

        elif request.method == 'DELETE':
            # Soft delete - mark as inactive instead of actual deletion
            baby.is_active = False
            baby.updated_at = datetime.utcnow()
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'Baby record deactivated successfully'
            })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Operation failed: {str(e)}'}), 500

@babycare_bp.route('/api/babies/<int:baby_id>/dashboard')
@login_required
def baby_dashboard_api(baby_id):
    """Get comprehensive dashboard data for a specific baby"""
    try:
        baby, error = validate_baby_access(baby_id, current_user.id)
        if error:
            return jsonify({'error': error}), 404 if 'not found' in error else 403

        # Get recent records
        recent_vaccinations = Vaccination.query.filter_by(baby_id=baby_id).order_by(Vaccination.scheduled_date.desc()).limit(5).all()
        recent_growth = GrowthRecord.query.filter_by(baby_id=baby_id).order_by(GrowthRecord.record_date.desc()).limit(5).all()
        recent_nutrition = NutritionRecord.query.filter_by(baby_id=baby_id).order_by(NutritionRecord.record_date.desc()).limit(5).all()

        upcoming_appointments = Appointment.query.filter_by(baby_id=baby_id).filter(Appointment.appointment_date > datetime.utcnow()).order_by(Appointment.appointment_date).limit(5).all()

        # Calculate statistics
        vaccination_stats = baby.get_vaccination_progress()

        return jsonify({
            'success': True,
            'baby': baby.to_dict(include_relationships=True),
            'recent_data': {
                'vaccinations': [v.to_dict() for v in recent_vaccinations],
                'growth_records': [g.to_dict() for g in recent_growth],
                'nutrition_records': [n.to_dict() for n in recent_nutrition],

                'upcoming_appointments': [a.to_dict() for a in upcoming_appointments]
            },
            'statistics': {
                'vaccination_progress': vaccination_stats,
                'total_records': {
                    'vaccinations': len(baby.vaccinations),
                    'growth_records': len(baby.growth_records),
                    'nutrition_records': len(baby.nutrition_records),

                    'appointments': len(baby.appointments)
                }
            }
        })

    except Exception as e:
        return jsonify({'error': f'Failed to load dashboard: {str(e)}'}), 500

@babycare_bp.route('/api/babies/<int:baby_id>/vaccinations', methods=['GET', 'POST'])
@login_required
def vaccinations_api(baby_id):
    """Get or create vaccinations for a baby"""
    # Verify baby belongs to current user
    baby = Baby.query.filter_by(id=baby_id, parent_id=session['user_id']).first()
    if not baby:
        return jsonify({'error': 'Baby not found'}), 404

    if request.method == 'GET':
        vaccinations = Vaccination.query.filter_by(baby_id=baby_id).all()
        return jsonify({
            'success': True,
            'vaccinations': [vaccination.to_dict() for vaccination in vaccinations]
        })

    elif request.method == 'POST':
        data = request.get_json()

        # Validate required fields
        required_fields = ['vaccine_name', 'scheduled_date']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        try:
            # Parse dates
            scheduled_date = datetime.strptime(data['scheduled_date'], '%Y-%m-%d').date()
            administered_date = None
            if data.get('administered_date'):
                administered_date = datetime.strptime(data['administered_date'], '%Y-%m-%d').date()

            # Create new vaccination record
            vaccination = Vaccination(
                baby_id=baby_id,
                vaccine_name=data['vaccine_name'],
                scheduled_date=scheduled_date,
                administered_date=administered_date,
                status=data.get('status', 'scheduled'),
                doctor_name=data.get('doctor_name'),
                notes=data.get('notes')
            )

            db.session.add(vaccination)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'Vaccination record created successfully',
                'vaccination': vaccination.to_dict()
            }), 201

        except ValueError as e:
            return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

@babycare_bp.route('/api/babies/<int:baby_id>/nutrition', methods=['GET', 'POST'])
@login_required
def nutrition_api(baby_id):
    """Get or create nutrition records for a baby"""
    # Verify baby belongs to current user
    baby = Baby.query.filter_by(id=baby_id, parent_id=session['user_id']).first()
    if not baby:
        return jsonify({'error': 'Baby not found'}), 404

    if request.method == 'GET':
        nutrition_records = NutritionRecord.query.filter_by(baby_id=baby_id).all()
        return jsonify({
            'success': True,
            'nutrition_records': [record.to_dict() for record in nutrition_records]
        })

    elif request.method == 'POST':
        data = request.get_json()

        # Validate required fields
        required_fields = ['record_date', 'feeding_type']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        try:
            # Parse date
            record_date = datetime.strptime(data['record_date'], '%Y-%m-%d').date()

            # Create new nutrition record
            nutrition_record = NutritionRecord(
                baby_id=baby_id,
                record_date=record_date,
                feeding_type=data['feeding_type'],
                amount=data.get('amount'),
                frequency=data.get('frequency'),
                food_items=data.get('food_items'),
                notes=data.get('notes')
            )

            db.session.add(nutrition_record)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'Nutrition record created successfully',
                'nutrition_record': nutrition_record.to_dict()
            }), 201

        except ValueError as e:
            return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500



@babycare_bp.route('/api/babies/<int:baby_id>/growth', methods=['GET', 'POST'])
@login_required
def growth_api(baby_id):
    """Get or create growth records for a baby"""
    # Verify baby belongs to current user
    baby = Baby.query.filter_by(id=baby_id, parent_id=session['user_id']).first()
    if not baby:
        return jsonify({'error': 'Baby not found'}), 404

    if request.method == 'GET':
        growth_records = GrowthRecord.query.filter_by(baby_id=baby_id).all()
        return jsonify({
            'success': True,
            'growth_records': [record.to_dict() for record in growth_records]
        })

    elif request.method == 'POST':
        data = request.get_json()

        # Validate required fields
        required_fields = ['record_date']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        try:
            # Parse date
            record_date = datetime.strptime(data['record_date'], '%Y-%m-%d').date()

            # Create new growth record
            growth_record = GrowthRecord(
                baby_id=baby_id,
                record_date=record_date,
                weight=data.get('weight'),
                height=data.get('height'),
                head_circumference=data.get('head_circumference'),
                notes=data.get('notes')
            )

            db.session.add(growth_record)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'Growth record created successfully',
                'growth_record': growth_record.to_dict()
            }), 201

        except ValueError as e:
            return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

# Admin Routes

@babycare_bp.route('/api/admin/baby-care-stats')
@admin_required
def admin_baby_care_stats():
    """Get baby care statistics for admin dashboard"""
    try:
        total_babies = Baby.query.count()
        total_vaccinations = Vaccination.query.count()
        total_nutrition_records = NutritionRecord.query.count()

        total_growth_records = GrowthRecord.query.count()

        # Get recent activity
        recent_babies = Baby.query.order_by(Baby.created_at.desc()).limit(5).all()
        recent_vaccinations = Vaccination.query.order_by(Vaccination.created_at.desc()).limit(5).all()

        return jsonify({
            'success': True,
            'stats': {
                'total_babies': total_babies,
                'total_vaccinations': total_vaccinations,
                'total_nutrition_records': total_nutrition_records,

                'total_growth_records': total_growth_records,
                'baby_records': total_babies,
                'total_users': User.query.count(),
                'active_sessions': 1,  # This would be calculated based on active sessions
                'last_updated': datetime.utcnow().isoformat()
            },
            'recent_activity': {
                'recent_babies': [baby.to_dict() for baby in recent_babies],
                'recent_vaccinations': [vaccination.to_dict() for vaccination in recent_vaccinations]
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@babycare_bp.route('/api/admin/update-content', methods=['POST'])
@admin_required
def admin_update_content():
    """Update baby care page content"""
    try:
        data = request.get_json()

        # In a real application, you would save this to a content management table
        # For now, we'll just return success

        return jsonify({
            'success': True,
            'message': 'Content updated successfully'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@babycare_bp.route('/api/admin/all-babies')
@admin_required
def admin_all_babies():
    """Get all babies for admin management"""
    try:
        babies = Baby.query.all()
        babies_data = []

        for baby in babies:
            baby_dict = baby.to_dict()
            # Add parent information
            parent = User.query.get(baby.parent_id)
            baby_dict['parent_name'] = parent.full_name if parent else 'Unknown'
            baby_dict['parent_email'] = parent.email if parent else 'Unknown'
            babies_data.append(baby_dict)

        return jsonify({
            'success': True,
            'babies': babies_data
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@babycare_bp.route('/api/admin/vaccination-schedule')
@admin_required
def admin_vaccination_schedule():
    """Get vaccination schedule overview for admin"""
    try:
        # Get upcoming vaccinations
        upcoming_vaccinations = Vaccination.query.filter(
            Vaccination.status == 'scheduled',
            Vaccination.scheduled_date >= date.today()
        ).order_by(Vaccination.scheduled_date).all()

        # Get overdue vaccinations
        overdue_vaccinations = Vaccination.query.filter(
            Vaccination.status == 'scheduled',
            Vaccination.scheduled_date < date.today()
        ).order_by(Vaccination.scheduled_date).all()

        vaccination_data = []
        for vaccination in upcoming_vaccinations + overdue_vaccinations:
            vaccination_dict = vaccination.to_dict()
            baby = Baby.query.get(vaccination.baby_id)
            vaccination_dict['baby_name'] = baby.name if baby else 'Unknown'
            vaccination_dict['is_overdue'] = vaccination.scheduled_date < date.today()
            vaccination_data.append(vaccination_dict)

        return jsonify({
            'success': True,
            'vaccinations': vaccination_data,
            'upcoming_count': len(upcoming_vaccinations),
            'overdue_count': len(overdue_vaccinations)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@babycare_bp.route('/api/admin/export-data')
@admin_required
def admin_export_data():
    """Export all baby care data for admin"""
    try:
        # In a real application, this would generate a comprehensive export
        # For now, we'll return a summary

        export_data = {
            'babies': [baby.to_dict() for baby in Baby.query.all()],
            'vaccinations': [vaccination.to_dict() for vaccination in Vaccination.query.all()],
            'nutrition_records': [record.to_dict() for record in NutritionRecord.query.all()],

            'growth_records': [record.to_dict() for record in GrowthRecord.query.all()],
            'export_date': datetime.utcnow().isoformat()
        }

        return jsonify({
            'success': True,
            'message': 'Data export completed',
            'data': export_data
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@babycare_bp.route('/api/appointments', methods=['GET', 'POST'])
@login_required
def appointments_api():
    """Get baby care appointments or create new appointment"""
    if request.method == 'GET':
        try:
            from app.data_manager import DataManager

            user_id = session['user_id']

            # Get all baby care appointments for the current user
            conn = DataManager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT a.id, a.user_id, a.baby_id, a.appointment_type, a.appointment_date,
                       a.doctor_name, a.clinic_name, a.purpose, a.status, a.notes,
                       a.created_at, b.name as baby_name, u.full_name as doctor_full_name,
                       u.email as doctor_email
                FROM appointments a
                LEFT JOIN babies b ON a.baby_id = b.id
                LEFT JOIN users u ON a.doctor_id = u.id
                WHERE a.user_id = ? AND a.appointment_type LIKE '%Baby Care%'
                ORDER BY a.appointment_date DESC
            ''', (user_id,))

            appointments = cursor.fetchall()
            conn.close()

            appointments_data = []
            for appointment in appointments:
                appointments_data.append({
                    'id': appointment[0],
                    'user_id': appointment[1],
                    'baby_id': appointment[2],
                    'appointment_type': appointment[3],
                    'appointment_date': appointment[4],
                    'doctor_name': appointment[5],
                    'clinic_name': appointment[6],
                    'purpose': appointment[7],
                    'status': appointment[8],
                    'notes': appointment[9],
                    'created_at': appointment[10],
                    'baby_name': appointment[11],
                    'doctor_full_name': appointment[12],
                    'doctor_email': appointment[13]
                })

            return jsonify({
                'success': True,
                'appointments': appointments_data,
                'count': len(appointments_data)
            })

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    elif request.method == 'POST':
        try:
            from app.data_manager import DataManager
            from app.services.email_service import email_service
            from datetime import datetime

            data = request.get_json()
            user_id = session['user_id']

            # Validate required fields
            required_fields = ['doctor_name', 'appointment_date']
            for field in required_fields:
                if not data.get(field):
                    return jsonify({
                        'success': False,
                        'error': f'Missing required field: {field}'
                    }), 400

            # Parse appointment date
            try:
                appointment_date = datetime.fromisoformat(data['appointment_date'].replace('Z', '+00:00'))
            except ValueError:
                return jsonify({
                    'success': False,
                    'error': 'Invalid appointment date format'
                }), 400

            # Get user information
            user_data = DataManager.get_user_by_id(user_id)
            if not user_data:
                return jsonify({
                    'success': False,
                    'error': 'User not found'
                }), 404

            # Find doctor by name
            doctor_id = None
            doctor_email = None
            if data.get('doctor_name'):
                users = DataManager.get_all_users()
                doctor = next((u for u in users if u.get('full_name') == data['doctor_name'] and u.get('role') == 'doctor'), None)
                if doctor:
                    doctor_id = doctor['id']
                    doctor_email = doctor['email']

            # Create new baby care appointment
            conn = DataManager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO appointments (user_id, baby_id, doctor_id, appointment_type, appointment_date,
                                        doctor_name, clinic_name, purpose, status, patient_name, patient_email,
                                        child_name, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id,
                data.get('baby_id'),
                doctor_id,
                data.get('appointment_type', 'Baby Care Checkup'),
                appointment_date.isoformat(),
                data['doctor_name'],
                data.get('clinic_name', 'Baby Care Clinic'),
                data.get('purpose', ''),
                'pending',
                data.get('patient_name', user_data['full_name']),
                user_data['email'],
                data.get('child_name', 'Child'),
                datetime.now().isoformat(),
                datetime.now().isoformat()
            ))

            appointment_id = cursor.lastrowid
            conn.commit()
            conn.close()

            # Send immediate booking notification to patient and doctor
            email_results = {
                'patient_email_sent': False,
                'doctor_email_sent': False
            }

            try:
                appointment_details = {
                    'appointment_id': appointment_id,
                    'patient_name': data.get('patient_name', user_data['full_name']),
                    'child_name': data.get('child_name', 'Child'),
                    'doctor_name': data['doctor_name'],
                    'appointment_date': appointment_date.strftime('%Y-%m-%d'),
                    'appointment_time': appointment_date.strftime('%H:%M'),
                    'appointment_type': data.get('appointment_type', 'Baby Care Checkup'),
                    'purpose': data.get('purpose', 'Baby Care Checkup'),
                    'clinic_name': data.get('clinic_name', 'Baby Care Clinic'),
                    'patient_email': user_data['email']
                }

                # Send immediate notification to patient
                patient_email_sent = email_service.send_appointment_booking_notification(
                    user_data['email'],
                    appointment_details
                )
                email_results['patient_email_sent'] = patient_email_sent

                # Send notification to doctor if email is available
                if doctor_email:
                    doctor_email_sent = email_service.send_doctor_new_appointment_notification(
                        doctor_email,
                        appointment_details
                    )
                    email_results['doctor_email_sent'] = doctor_email_sent

            except Exception as email_error:
                print(f"Email sending failed: {email_error}")

            return jsonify({
                'success': True,
                'message': 'Baby care appointment created successfully. Confirmation emails sent.',
                'appointment_id': appointment_id,
                'email_results': email_results
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500