<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pregnancy Reports & Analytics - Maternal and Child Health Monitoring</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #f472b6;
            --primary-dark: #ec4899;
            --secondary: #c084fc;
            --secondary-dark: #a855f7;
            --purple: #a855f7;
            --accent: #fde047;
            --accent-dark: #facc15;
            --info: #7dd3fc;
            --light: #fdf2f8;
            --dark: #581c87;
            --gray: #64748b;
            --light-gray: #e2e8f0;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --shadow: 0 4px 20px rgba(0,0,0,0.08);
            --shadow-hover: 0 8px 30px rgba(0,0,0,0.12);
            --border-radius: 20px;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #ffffff;
            min-height: 100vh;
            margin: 0;
            overflow-x: hidden;
            padding-top: 80px;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.07);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 0.5rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 70px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark);
            text-decoration: none;
        }

        .logo-icon {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1.5rem;
            align-items: center;
        }

        .nav-link {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            padding: 0.8rem 1.2rem;
            border-radius: 25px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
            transition: var(--transition);
        }

        .nav-link:hover,
        .nav-link.active {
            color: white;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(244, 114, 182, 0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .page-header {
            background: linear-gradient(135deg, var(--light), #ffffff);
            padding: 4rem 0 2rem;
            text-align: center;
        }

        .page-header h1 {
            font-size: 3rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 1rem;
        }

        .page-header p {
            font-size: 1.2rem;
            color: var(--gray);
            max-width: 600px;
            margin: 0 auto;
        }

        .reports-section {
            padding: 4rem 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
            text-align: center;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
        }

        .stat-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 1rem;
        }

        .stat-icon.weight {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
        }

        .stat-icon.appointments {
            background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
        }

        .stat-icon.nutrition {
            background: linear-gradient(135deg, var(--accent), var(--accent-dark));
        }

        .stat-icon.exercise {
            background: linear-gradient(135deg, var(--info), #0ea5e9);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--gray);
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .page-header h1 {
                font-size: 2rem;
            }

            .nav-menu {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="/pregnancy" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <span>Reports</span>
            </a>
            <nav class="nav-menu">
                <a href="/" class="nav-link">
                    <i class="fas fa-home"></i> Home
                </a>
                <a href="/pregnancy" class="nav-link">
                    <i class="fas fa-heart"></i> Pregnancy Care
                </a>
                <a href="/pregnancy/reports" class="nav-link active">
                    <i class="fas fa-chart-line"></i> Reports
                </a>
                <a href="/pregnancy/nutrition" class="nav-link">
                    <i class="fas fa-utensils"></i> Nutrition
                </a>
                <a href="/pregnancy/exercise" class="nav-link">
                    <i class="fas fa-dumbbell"></i> Exercise
                </a>
                <a href="/pregnancy/weight-tracker" class="nav-link">
                    <i class="fas fa-weight"></i> Weight Tracker
                </a>
                <a href="/pregnancy/appointments" class="nav-link">
                    <i class="fas fa-calendar-alt"></i> Appointments
                </a>
                <a href="/babycare" class="nav-link">
                    <i class="fas fa-baby"></i> Baby Care
                </a>
                <a href="/auth/logout" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </nav>
        </div>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Pregnancy Reports & Analytics</h1>
            <p>Track your pregnancy progress with detailed reports and visual analytics</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="reports-section">
        <div class="container">
            <!-- Statistics Overview -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon weight">
                        <i class="fas fa-weight"></i>
                    </div>
                    <div class="stat-value">12.5</div>
                    <div class="stat-label">Weight Gained (kg)</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon appointments">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-value">8</div>
                    <div class="stat-label">Appointments Completed</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon nutrition">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <div class="stat-value">85%</div>
                    <div class="stat-label">Nutrition Goals Met</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon exercise">
                        <i class="fas fa-dumbbell"></i>
                    </div>
                    <div class="stat-value">24</div>
                    <div class="stat-label">Exercise Sessions</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        console.log('Reports page loaded successfully');
    </script>
</body>
</html>