<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointments - <PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .doctor-nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateY(-2px);
        }

        .doctor-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .page-header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .calendar-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .calendar-nav {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .calendar-nav button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .calendar-nav button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .current-date {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .appointments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .appointment-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #667eea;
        }

        .appointment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .appointment-card.urgent {
            border-left-color: #f44336;
        }

        .appointment-card.completed {
            border-left-color: #4caf50;
        }

        .appointment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .appointment-time {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .appointment-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-scheduled {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-completed {
            background: #e8f5e8;
            color: #4caf50;
        }

        .status-urgent {
            background: #ffebee;
            color: #f44336;
        }

        .patient-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .appointment-type {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .baby-name {
            color: #667eea;
            font-size: 0.85rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .appointment-purpose {
            color: #555;
            font-size: 0.85rem;
            margin-bottom: 0.5rem;
            font-style: italic;
        }

        .appointment-notes {
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 5px;
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 1rem;
            border-left: 3px solid #667eea;
        }

        .appointment-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.8rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-success {
            background: #4caf50;
            color: white;
        }

        .btn-warning {
            background: #ff9800;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #666;
            font-size: 1.1rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            color: #ccc;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .doctor-container {
                padding: 1rem;
            }

            .appointments-grid {
                grid-template-columns: 1fr;
            }

            .calendar-header {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="doctor-nav">
        <div class="nav-content">
            <div class="nav-logo">
                <i class="fas fa-user-md"></i>
                <span>Doctor Portal</span>
            </div>
            <div class="nav-links">
                <a href="/doctor"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/doctor/patients"><i class="fas fa-users"></i> Patients</a>
                <a href="/doctor/appointments" class="active"><i class="fas fa-calendar-check"></i> Appointments</a>
                <a href="/doctor/generate_id"><i class="fas fa-id-card"></i> Generate ID</a>
                <a href="/doctor/consultations"><i class="fas fa-stethoscope"></i> Consultations</a>
                <a href="/doctor/reports"><i class="fas fa-file-medical"></i> Reports</a>
                <a href="/auth/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="doctor-container">
        <!-- Header -->
        <div class="page-header">
            <h1>
                <i class="fas fa-calendar-check"></i>
                Appointment Management
            </h1>
            <p>Schedule and manage patient appointments</p>
        </div>

        <!-- Calendar Container -->
        <div class="calendar-container">
            <div class="calendar-header">
                <div class="current-date" id="current-date">Today's Appointments</div>
                <div class="calendar-nav">
                    <button onclick="showPreviousDay()">
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <button onclick="showToday()">
                        <i class="fas fa-calendar-day"></i> Today
                    </button>
                    <button onclick="showNextDay()">
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Appointments Grid -->
        <div class="appointments-grid" id="appointments-grid">
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                Loading appointments...
            </div>
        </div>
    </div>

    <script>
        let currentDate = new Date();

        document.addEventListener('DOMContentLoaded', function() {
            updateDateDisplay();
            loadAppointments();
        });

        function updateDateDisplay() {
            const options = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            };
            document.getElementById('current-date').textContent = 
                currentDate.toLocaleDateString('en-US', options) + ' Appointments';
        }

        function showPreviousDay() {
            currentDate.setDate(currentDate.getDate() - 1);
            updateDateDisplay();
            loadAppointments();
        }

        function showToday() {
            currentDate = new Date();
            updateDateDisplay();
            loadAppointments();
        }

        function showNextDay() {
            currentDate.setDate(currentDate.getDate() + 1);
            updateDateDisplay();
            loadAppointments();
        }

        async function loadAppointments() {
            try {
                // Show loading state
                const container = document.getElementById('appointments-grid');
                container.innerHTML = `
                    <div class="loading-state">
                        <i class="fas fa-spinner fa-spin"></i>
                        <h3>Loading Appointments...</h3>
                        <p>Please wait while we fetch your appointments.</p>
                    </div>
                `;

                // Fetch appointments from API
                const response = await fetch('/doctor/api/appointments');
                const result = await response.json();

                if (result.success) {
                    displayAppointments(result.appointments);
                } else {
                    container.innerHTML = `
                        <div class="error-state">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h3>Error Loading Appointments</h3>
                            <p>${result.error || 'Failed to load appointments'}</p>
                            <button onclick="loadAppointments()" class="btn btn-primary">
                                <i class="fas fa-refresh"></i> Try Again
                            </button>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading appointments:', error);
                const container = document.getElementById('appointments-grid');
                container.innerHTML = `
                    <div class="error-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>Connection Error</h3>
                        <p>Unable to connect to the server. Please check your connection and try again.</p>
                        <button onclick="loadAppointments()" class="btn btn-primary">
                            <i class="fas fa-refresh"></i> Try Again
                        </button>
                    </div>
                `;
            }
        }

        function displayAppointments(appointments) {
            const container = document.getElementById('appointments-grid');

            // Store appointments globally for access in other functions
            currentAppointments = appointments;

            if (appointments.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-calendar-times"></i>
                        <h3>No Appointments</h3>
                        <p>No appointments scheduled for this date.</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = appointments.map(appointment => {
                const statusClass = `status-${appointment.status}`;
                const cardClass = appointment.status === 'urgent' ? 'urgent' :
                                 appointment.status === 'completed' ? 'completed' : '';

                // Format date and time
                const timeString = appointment.appointment_time;

                return `
                    <div class="appointment-card ${cardClass}">
                        <div class="appointment-header">
                            <div class="appointment-time">${timeString}</div>
                            <div class="appointment-status ${statusClass}">
                                ${appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                            </div>
                        </div>

                        <div class="patient-name">${appointment.patient_name}</div>
                        <div class="appointment-type">${appointment.appointment_type}</div>
                        ${appointment.baby_name ? `<div class="baby-name"><i class="fas fa-baby"></i> ${appointment.baby_name}</div>` : ''}
                        ${appointment.purpose ? `<div class="appointment-purpose">${appointment.purpose}</div>` : ''}
                        ${appointment.notes ? `<div class="appointment-notes">${appointment.notes}</div>` : ''}

                        <div class="appointment-actions">
                            ${appointment.status === 'pending' ? `
                                <button class="btn btn-primary" onclick="confirmAppointment(${appointment.id})">
                                    <i class="fas fa-check-circle"></i> Confirm
                                </button>
                                <button class="btn btn-danger" onclick="cancelAppointment(${appointment.id})">
                                    <i class="fas fa-times"></i> Decline
                                </button>
                            ` : ''}
                            ${appointment.status === 'confirmed' ? `
                                <button class="btn btn-success" onclick="markCompleted(${appointment.id})">
                                    <i class="fas fa-check"></i> Complete
                                </button>
                                <button class="btn btn-danger" onclick="cancelAppointment(${appointment.id})">
                                    <i class="fas fa-times"></i> Cancel
                                </button>
                            ` : ''}
                            ${appointment.status === 'scheduled' ? `
                                <button class="btn btn-success" onclick="markCompleted(${appointment.id})">
                                    <i class="fas fa-check"></i> Complete
                                </button>
                                <button class="btn btn-warning" onclick="rescheduleAppointment(${appointment.id})">
                                    <i class="fas fa-clock"></i> Reschedule
                                </button>
                                <button class="btn btn-danger" onclick="cancelAppointment(${appointment.id})">
                                    <i class="fas fa-times"></i> Cancel
                                </button>
                            ` : ''}
                            <button class="btn btn-primary" onclick="viewAppointmentDetails(${appointment.id})">
                                <i class="fas fa-eye"></i> Details
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        async function confirmAppointment(appointmentId) {
            if (!confirm('Are you sure you want to confirm this appointment? This will send confirmation emails to the patient.')) {
                return;
            }

            try {
                const response = await fetch(`/doctor/api/appointments/${appointmentId}/confirm`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('Appointment confirmed! Confirmation emails sent.', 'success');
                    loadAppointments(); // Refresh the list
                } else {
                    showNotification(result.error || 'Failed to confirm appointment', 'error');
                }
            } catch (error) {
                console.error('Error confirming appointment:', error);
                showNotification('Error confirming appointment', 'error');
            }
        }

        async function markCompleted(appointmentId) {
            const notes = prompt('Enter consultation notes (optional):');
            if (notes === null) return; // User cancelled

            try {
                const response = await fetch(`/doctor/api/appointment/${appointmentId}/complete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ notes: notes })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('Appointment marked as completed!', 'success');
                    loadAppointments(); // Refresh the list
                } else {
                    showNotification(result.error || 'Failed to complete appointment', 'error');
                }
            } catch (error) {
                console.error('Error completing appointment:', error);
                showNotification('Error completing appointment', 'error');
            }
        }

        async function rescheduleAppointment(appointmentId) {
            const newDate = prompt('Enter new date (YYYY-MM-DD):');
            if (!newDate) return;

            const newTime = prompt('Enter new time (HH:MM in 24-hour format):');
            if (!newTime) return;

            try {
                const response = await fetch(`/doctor/api/appointment/${appointmentId}/reschedule`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        new_date: newDate,
                        new_time: newTime
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('Appointment rescheduled successfully!', 'success');
                    loadAppointments(); // Refresh the list
                } else {
                    showNotification(result.error || 'Failed to reschedule appointment', 'error');
                }
            } catch (error) {
                console.error('Error rescheduling appointment:', error);
                showNotification('Error rescheduling appointment', 'error');
            }
        }

        async function viewAppointmentDetails(appointmentId) {
            try {
                // Find appointment in current list
                const appointment = currentAppointments.find(apt => apt.id === appointmentId);
                if (!appointment) {
                    showNotification('Appointment not found', 'error');
                    return;
                }

                const details = `
APPOINTMENT DETAILS
==================
Patient: ${appointment.patient_name}
Email: ${appointment.patient_contact?.email || 'Not provided'}
Phone: ${appointment.patient_contact?.phone || 'Not provided'}
Date: ${appointment.formatted_date}
Time: ${appointment.appointment_time}
Type: ${appointment.appointment_type}
Status: ${appointment.status.toUpperCase()}
${appointment.baby_name ? `Baby: ${appointment.baby_name}` : ''}
${appointment.purpose ? `Purpose: ${appointment.purpose}` : ''}
${appointment.notes ? `Notes: ${appointment.notes}` : ''}
                `.trim();

                alert(details);
            } catch (error) {
                console.error('Error viewing appointment details:', error);
                showNotification('Error loading appointment details', 'error');
            }
        }

        async function cancelAppointment(appointmentId) {
            const reason = prompt('Enter cancellation reason:');
            if (reason === null) return; // User cancelled

            try {
                const response = await fetch(`/doctor/api/appointments/${appointmentId}/cancel`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ reason: reason })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('Appointment cancelled successfully!', 'success');
                    loadAppointments(); // Refresh the list
                } else {
                    showNotification(result.error || 'Failed to cancel appointment', 'error');
                }
            } catch (error) {
                console.error('Error cancelling appointment:', error);
                showNotification('Error cancelling appointment', 'error');
            }
        }

        // Global variable to store current appointments
        let currentAppointments = [];

        // Notification function
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 10px;
                color: white;
                font-weight: 500;
                z-index: 9999;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                background: ${type === 'success' ? '#4caf50' : '#f44336'};
                opacity: 0;
                transform: translateX(20px);
                transition: all 0.4s ease;
            `;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Hide notification after 4 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(20px)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 400);
            }, 4000);
        }
    </script>
</body>
</html>
