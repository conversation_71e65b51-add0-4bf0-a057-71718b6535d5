# 🍼 Pregnancy Baby Care System

A comprehensive **Maternal and Child Health Monitoring System** built with Flask for tracking pregnancy and baby care with role-based access control.

## ✅ Project Status: COMPLETE & FUNCTIONAL

This application is **fully working** and ready to use with all core features implemented and tested.

### 🆕 Latest Update: Web3Forms Email Integration
- **Doctor Appointment System**: Complete workflow with email confirmations
- **Web3Forms API**: Integrated for reliable email delivery to patients and doctors
- **Professional Email Templates**: Automated confirmation emails for appointments
- **Real-time Status Updates**: Pending → Confirmed → Completed appointment flow

## 🚀 Quick Start

### Option 1: Windows Users (Easiest)
```bash
# Double-click or run:
start.bat
```

### Option 2: Manual Start
```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python run.py
```

### Option 3: Check HOW_TO_RUN.md
See detailed instructions in `HOW_TO_RUN.md`

## 🌐 Access the Application

Once started, the application will be available at:
**http://127.0.0.1:5000**

The browser will open automatically, or you can manually navigate to the URL.

## 👥 Demo User Accounts

| Role | Email | Password | Access Level |
|------|-------|----------|--------------|
| **👑 Admin** | <EMAIL> | admin123 | Full system access, user management |
| **👨‍⚕️ Doctor** | <EMAIL> | doctor123 | Medical records, patient management |
| **👤 User** | <EMAIL> | user123 | Personal baby care tracking |

## 🎯 Key Features

### ✅ Authentication & Security
- Secure user registration and login
- Role-based access control (Admin/Doctor/User)
- Password hashing with Werkzeug
- Session-based authentication

### ✅ Baby Care Management
- Baby registration and profile management
- Vaccination tracking and scheduling
- Growth monitoring with charts
- Nutrition and feeding records
- Sleep pattern tracking
- Medical appointment management

### ✅ Admin Dashboard
- User management and analytics
- System statistics and insights
- Content management tools
- Comprehensive reporting

### ✅ User Interface
- Clean, modern responsive design
- Mobile-friendly interface
- Intuitive navigation
- Real-time form validation

## 🛠️ Technology Stack

- **Backend**: Flask 3.0.0 (Python)
- **Database**: SQLite database with proper relational structure
- **Frontend**: HTML5, CSS3, JavaScript
- **Security**: Werkzeug password hashing
- **Icons**: Font Awesome 6.4.0

## 📁 Project Structure

```
pregnancy-baby-care-project/
├── app/                    # Main application
│   ├── __init__.py        # App factory
│   ├── data_manager.py    # Data management
│   ├── routes/            # Route blueprints
│   └── templates/         # HTML templates
├── instance/              # Data storage
│   └── data.json         # Database file
├── run.py                # Main entry point
├── start.bat             # Windows startup script
├── HOW_TO_RUN.md        # Detailed instructions
└── requirements.txt      # Dependencies
```

## 🔧 Configuration

### Environment Variables (Optional)
```bash
set FLASK_ENV=development    # or production
set SECRET_KEY=your-key     # for production
set HOST=127.0.0.1         # server host
set PORT=5000              # server port
```

### Data Storage
- SQLite database stored in `instance/pregnancy_care.db`
- Automatically created on first run with proper schema
- Includes demo users and sample data
- Relational database with proper foreign key constraints

## 📱 How to Use

### For Parents/Users:
1. **Login** with user credentials or register
2. **Add Baby** - Register your baby's information
3. **Track Health** - Record vaccinations, growth, nutrition
4. **Monitor Progress** - View development charts and reports
5. **Manage Appointments** - Schedule and track medical visits

### For Healthcare Providers:
1. **Login** with doctor credentials
2. **Access Records** - View patient information
3. **Update Medical Data** - Add notes and recommendations
4. **Manage Patients** - Track multiple patients

### For Administrators:
1. **Login** with admin credentials
2. **User Management** - Add, edit, or remove users
3. **System Analytics** - View usage statistics
4. **Content Management** - Manage system content

## 🔒 Security Features

- ✅ Secure password hashing
- ✅ Session-based authentication
- ✅ Input validation and sanitization
- ✅ Role-based access control
- ✅ CSRF protection ready
- ✅ Secure data storage

## 🆘 Troubleshooting

### Common Issues:
1. **Port in use**: Change PORT environment variable
2. **Dependencies missing**: Run `pip install -r requirements.txt`
3. **Python not found**: Install Python 3.9+ from python.org
4. **Permission errors**: Run as administrator (Windows)

### Getting Help:
- Check `HOW_TO_RUN.md` for detailed instructions
- Verify Python version: `python --version`
- Check dependencies: `pip list`

## 🎉 Success Indicators

When working correctly, you should see:
- ✅ Server starts without errors
- ✅ Browser opens to home page automatically
- ✅ Login works with demo credentials
- ✅ Different dashboards for different roles
- ✅ All features accessible and responsive

## 📊 API Endpoints

### Authentication
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `GET /auth/logout` - User logout
- `GET /auth/api/check-auth` - Check auth status

### Main Routes
- `GET /` - Home page
- `GET /baby-care` - Baby care dashboard
- `GET /admin` - Admin dashboard

## 🚀 Deployment Ready

The application is ready for:
- ✅ Local development
- ✅ Demo presentations
- ✅ Production deployment
- ✅ Further customization

---

## 🎊 **READY TO USE!**

**The Pregnancy Baby Care System is complete, tested, and ready for immediate use!**

Simply run `python run.py` or `start.bat` and start managing maternal and child health data with a professional, secure, and user-friendly interface.