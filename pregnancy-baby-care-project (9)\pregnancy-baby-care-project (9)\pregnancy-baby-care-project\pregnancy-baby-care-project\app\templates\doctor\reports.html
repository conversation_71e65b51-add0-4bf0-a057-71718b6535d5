<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Reports - Doctor Portal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .doctor-nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateY(-2px);
        }

        .doctor-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .page-header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .reports-filters {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-group label {
            font-weight: 600;
            color: #667eea;
            font-size: 0.9rem;
        }

        .filter-group select,
        .filter-group input {
            padding: 0.5rem;
            border: 2px solid #e0e6ff;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 1.5rem;
        }

        .report-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .report-type {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.8rem;
        }

        .report-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-pending {
            background: #fff3e0;
            color: #f57c00;
        }

        .status-reviewed {
            background: #e8f5e8;
            color: #4caf50;
        }

        .status-urgent {
            background: #ffebee;
            color: #f44336;
        }

        .patient-info {
            margin-bottom: 1rem;
        }

        .patient-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .report-date {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .report-summary {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }

        .summary-title {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .summary-content {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .report-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.8rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-success {
            background: #4caf50;
            color: white;
        }

        .btn-warning {
            background: #ff9800;
            color: white;
        }

        .btn-info {
            background: #2196f3;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #666;
            font-size: 1.1rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            color: #ccc;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .doctor-container {
                padding: 1rem;
            }

            .reports-grid {
                grid-template-columns: 1fr;
            }

            .reports-filters {
                flex-direction: column;
                align-items: stretch;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="doctor-nav">
        <div class="nav-content">
            <div class="nav-logo">
                <i class="fas fa-user-md"></i>
                <span>Doctor Portal</span>
            </div>
            <div class="nav-links">
                <a href="/doctor"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/doctor/patients"><i class="fas fa-users"></i> Patients</a>
                <a href="/doctor/appointments"><i class="fas fa-calendar-check"></i> Appointments</a>
                <a href="/doctor/consultations"><i class="fas fa-stethoscope"></i> Consultations</a>
                <a href="/doctor/reports" class="active"><i class="fas fa-file-medical"></i> Reports</a>
                <a href="/auth/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="doctor-container">
        <!-- Header -->
        <div class="page-header">
            <h1>
                <i class="fas fa-file-medical"></i>
                Medical Reports
            </h1>
            <p>Review and manage patient medical reports and lab results</p>
        </div>

        <!-- Filters -->
        <div class="reports-filters">
            <div class="filter-group">
                <label for="status-filter">Status</label>
                <select id="status-filter" onchange="filterReports()">
                    <option value="all">All Reports</option>
                    <option value="pending">Pending Review</option>
                    <option value="reviewed">Reviewed</option>
                    <option value="urgent">Urgent</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="type-filter">Report Type</label>
                <select id="type-filter" onchange="filterReports()">
                    <option value="all">All Types</option>
                    <option value="lab">Lab Results</option>
                    <option value="imaging">Imaging</option>
                    <option value="prenatal">Prenatal</option>
                    <option value="vaccination">Vaccination</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label for="date-filter">Date Range</label>
                <input type="date" id="date-filter" onchange="filterReports()">
            </div>
        </div>

        <!-- Reports Grid -->
        <div class="reports-grid" id="reports-grid">
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                Loading reports...
            </div>
        </div>
    </div>

    <script>
        let allReports = [];

        document.addEventListener('DOMContentLoaded', function() {
            loadReports();
        });

        function loadReports() {
            // Mock reports data
            allReports = [
                {
                    id: 1,
                    patient: 'Priya Sharma',
                    type: 'lab',
                    title: 'Blood Test Results',
                    date: '2024-01-15',
                    status: 'pending',
                    summary: 'Complete blood count and metabolic panel. Hemoglobin levels slightly below normal range.'
                },
                {
                    id: 2,
                    patient: 'Anita Patel',
                    type: 'prenatal',
                    title: 'Ultrasound Report',
                    date: '2024-01-14',
                    status: 'reviewed',
                    summary: '20-week anatomy scan completed. Fetal development appears normal. All major organs visible.'
                },
                {
                    id: 3,
                    patient: 'Meera Singh',
                    type: 'lab',
                    title: 'Glucose Tolerance Test',
                    date: '2024-01-13',
                    status: 'urgent',
                    summary: 'Glucose levels elevated. Possible gestational diabetes. Immediate dietary consultation recommended.'
                },
                {
                    id: 4,
                    patient: 'Kavya Reddy',
                    type: 'imaging',
                    title: 'Chest X-Ray',
                    date: '2024-01-12',
                    status: 'reviewed',
                    summary: 'Chest X-ray shows clear lungs. No signs of infection or abnormalities detected.'
                },
                {
                    id: 5,
                    patient: 'Sunita Gupta',
                    type: 'vaccination',
                    title: 'Immunization Record',
                    date: '2024-01-11',
                    status: 'pending',
                    summary: 'Tdap vaccination administered. Patient tolerated well. Next vaccination due in 6 months.'
                }
            ];

            displayReports(allReports);
        }

        function filterReports() {
            const statusFilter = document.getElementById('status-filter').value;
            const typeFilter = document.getElementById('type-filter').value;
            const dateFilter = document.getElementById('date-filter').value;

            let filteredReports = allReports;

            if (statusFilter !== 'all') {
                filteredReports = filteredReports.filter(report => report.status === statusFilter);
            }

            if (typeFilter !== 'all') {
                filteredReports = filteredReports.filter(report => report.type === typeFilter);
            }

            if (dateFilter) {
                filteredReports = filteredReports.filter(report => report.date >= dateFilter);
            }

            displayReports(filteredReports);
        }

        function displayReports(reports) {
            const container = document.getElementById('reports-grid');
            
            if (reports.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-file-medical"></i>
                        <h3>No Reports Found</h3>
                        <p>No reports match the selected filters.</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = reports.map(report => {
                const statusClass = `status-${report.status}`;
                const typeIcon = getTypeIcon(report.type);
                const formattedDate = new Date(report.date).toLocaleDateString();
                
                return `
                    <div class="report-card">
                        <div class="report-header">
                            <div class="report-type">
                                <i class="${typeIcon}"></i> ${report.title}
                            </div>
                            <div class="report-status ${statusClass}">
                                ${report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                            </div>
                        </div>
                        
                        <div class="patient-info">
                            <div class="patient-name">${report.patient}</div>
                            <div class="report-date">
                                <i class="fas fa-calendar"></i> ${formattedDate}
                            </div>
                        </div>
                        
                        <div class="report-summary">
                            <div class="summary-title">Summary:</div>
                            <div class="summary-content">${report.summary}</div>
                        </div>
                        
                        <div class="report-actions">
                            ${report.status === 'pending' ? `
                                <button class="btn btn-success" onclick="reviewReport(${report.id})">
                                    <i class="fas fa-check"></i> Mark Reviewed
                                </button>
                                <button class="btn btn-warning" onclick="flagUrgent(${report.id})">
                                    <i class="fas fa-exclamation-triangle"></i> Flag Urgent
                                </button>
                            ` : ''}
                            <button class="btn btn-primary" onclick="viewFullReport(${report.id})">
                                <i class="fas fa-eye"></i> View Full Report
                            </button>
                            <button class="btn btn-info" onclick="downloadReport(${report.id})">
                                <i class="fas fa-download"></i> Download
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getTypeIcon(type) {
            const icons = {
                'lab': 'fas fa-vial',
                'imaging': 'fas fa-x-ray',
                'prenatal': 'fas fa-baby',
                'vaccination': 'fas fa-syringe'
            };
            return icons[type] || 'fas fa-file-medical';
        }

        function reviewReport(reportId) {
            const report = allReports.find(r => r.id === reportId);
            if (report) {
                report.status = 'reviewed';
                alert(`Report for ${report.patient} has been marked as reviewed.\n\nThis would update the patient's medical record and notify relevant staff.`);
                displayReports(allReports);
            }
        }

        function flagUrgent(reportId) {
            const report = allReports.find(r => r.id === reportId);
            if (report) {
                report.status = 'urgent';
                alert(`Report for ${report.patient} has been flagged as urgent.\n\nThis would send immediate notifications and prioritize the case.`);
                displayReports(allReports);
            }
        }

        function viewFullReport(reportId) {
            const report = allReports.find(r => r.id === reportId);
            if (report) {
                alert(`Viewing full report for ${report.patient}.\n\nThis would open a detailed view with complete lab results, images, and analysis.`);
            }
        }

        function downloadReport(reportId) {
            const report = allReports.find(r => r.id === reportId);
            if (report) {
                alert(`Downloading report for ${report.patient}.\n\nThis would generate and download a PDF version of the complete medical report.`);
            }
        }
    </script>
</body>
</html>
