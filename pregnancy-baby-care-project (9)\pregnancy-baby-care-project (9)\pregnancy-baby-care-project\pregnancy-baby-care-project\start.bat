@echo off
title Pregnancy Baby Care System
color 0A

echo.
echo ===============================================
echo    PREGNANCY BABY CARE SYSTEM - STARTUP
echo ===============================================
echo.

echo [1/4] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.9 or higher from https://python.org
    pause
    exit /b 1
)
python --version

echo.
echo [2/4] Checking dependencies...
pip show flask >nul 2>&1
if errorlevel 1 (
    echo Installing Flask and dependencies...
    pip install -r requirements.txt
) else (
    echo Dependencies already installed
)

echo.
echo [3/4] Starting the application...
echo.
echo ===============================================
echo    SERVER STARTING...
echo ===============================================
echo.
echo Demo Credentials:
echo   Admin: <EMAIL> / admin123
echo   Doctor: <EMAIL> / doctor123  
echo   User: <EMAIL> / user123
echo.
echo Browser will open automatically...
echo Press Ctrl+C to stop the server
echo ===============================================
echo.

echo [4/4] Launching server...
python run.py

echo.
echo ===============================================
echo    SERVER STOPPED
echo ===============================================
pause
