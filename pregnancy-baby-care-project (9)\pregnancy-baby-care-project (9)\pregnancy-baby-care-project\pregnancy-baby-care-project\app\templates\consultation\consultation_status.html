<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Consultation Status - Maternal and Child Health Care</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        :root {
            --primary: #d63384;
            --secondary: #6f42c1;
            --success: #198754;
            --info: #0dcaf0;
            --warning: #ffc107;
            --danger: #dc3545;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --light-gray: #e9ecef;
            --gradient-primary: linear-gradient(135deg, #d63384 0%, #6f42c1 100%);
        }

        body {
            background: linear-gradient(135deg, #f9f0ff 0%, #f0f9ff 100%);
            color: var(--dark);
            min-height: 100vh;
            padding-top: 80px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 0.5rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--primary);
            font-weight: 700;
            font-size: 1.5rem;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 0.5rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-link:hover {
            color: var(--primary);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .page-header {
            text-align: center;
            padding: 3rem 0;
            background: var(--gradient-primary);
            color: white;
            margin-bottom: 3rem;
        }

        .page-header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .page-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .consultation-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border-left: 5px solid var(--primary);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .consultation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .consultation-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .consultation-id {
            font-family: 'Courier New', monospace;
            background: var(--light);
            padding: 0.5rem 1rem;
            border-radius: 10px;
            font-size: 0.9rem;
            color: var(--gray);
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-approved {
            background: #d1edff;
            color: #0c5460;
            border: 1px solid #b8daff;
        }

        .status-confirmed {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-completed {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }

        .consultation-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .detail-group {
            background: var(--light);
            padding: 1rem;
            border-radius: 10px;
        }

        .detail-label {
            font-weight: 600;
            color: var(--gray);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .detail-value {
            color: var(--dark);
            font-size: 1rem;
        }

        .blockchain-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #f0f9ff 100%);
            border: 1px solid var(--info);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 1.5rem;
        }

        .blockchain-info h4 {
            color: var(--info);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .blockchain-hash {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 0.8rem;
            border-radius: 8px;
            border: 1px solid var(--light-gray);
            word-break: break-all;
            font-size: 0.9rem;
            color: var(--gray);
        }

        .symptoms-section {
            margin-top: 1.5rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 4px solid var(--primary);
        }

        .symptoms-section h4 {
            color: var(--primary);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--gray);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: var(--dark);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(214, 51, 132, 0.3);
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: var(--gray);
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .urgency-high {
            border-left-color: var(--danger) !important;
        }

        .urgency-medium {
            border-left-color: var(--warning) !important;
        }

        .urgency-low {
            border-left-color: var(--success) !important;
        }

        @media (max-width: 768px) {
            .nav-container {
                padding: 0 1rem;
            }
            
            .container {
                padding: 0 1rem;
            }
            
            .page-header h1 {
                font-size: 2.5rem;
            }
            
            .consultation-details {
                grid-template-columns: 1fr;
            }
            
            .consultation-header {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="/" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <span>Consultation Status</span>
            </a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="/" class="nav-link"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="/consultation" class="nav-link"><i class="fas fa-plus"></i> New Consultation</a></li>
                    <li><a href="/pregnancy" class="nav-link"><i class="fas fa-heart"></i> Pregnancy</a></li>
                    <li><a href="/babycare" class="nav-link"><i class="fas fa-baby"></i> Baby Care</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <div class="page-header">
            <div class="container">
                <h1><i class="fas fa-clipboard-list"></i> My Consultation Requests</h1>
                <p>Track your Web3 blockchain-verified consultation requests and appointments</p>
            </div>
        </div>

        <div class="container">
            <div id="consultations-container">
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>Loading your consultations...</p>
                </div>
            </div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadConsultations();
        });

        async function loadConsultations() {
            try {
                const response = await fetch('/consultation/api/consultations', {
                    credentials: 'include'
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        displayConsultations(result.consultations);
                    } else {
                        showError('Failed to load consultations');
                    }
                } else if (response.status === 401) {
                    // User not logged in, redirect to login
                    window.location.href = '/auth/login';
                } else {
                    showError('Failed to load consultations');
                }
            } catch (error) {
                console.error('Error loading consultations:', error);
                showError('Error loading consultations');
            }
        }

        function displayConsultations(consultations) {
            const container = document.getElementById('consultations-container');
            
            if (consultations.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-stethoscope"></i>
                        <h3>No Consultation Requests Yet</h3>
                        <p>You haven't submitted any Web3 consultation requests yet.</p>
                        <a href="/consultation" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Submit New Consultation
                        </a>
                    </div>
                `;
                return;
            }

            const consultationsHtml = consultations.map(consultation => {
                const urgencyClass = consultation.urgency_level === 'emergency' ? 'urgency-high' : 
                                   consultation.urgency_level === 'urgent' ? 'urgency-medium' : 'urgency-low';
                
                const statusClass = `status-${consultation.status}`;
                
                const createdDate = new Date(consultation.created_at).toLocaleDateString();
                const createdTime = new Date(consultation.created_at).toLocaleTimeString();

                return `
                    <div class="consultation-card ${urgencyClass}">
                        <div class="consultation-header">
                            <div class="consultation-id">
                                ID: ${consultation.id.substring(0, 8)}...
                            </div>
                            <div class="status-badge ${statusClass}">
                                ${consultation.status}
                            </div>
                        </div>

                        <div class="consultation-details">
                            <div class="detail-group">
                                <div class="detail-label">
                                    <i class="fas fa-user-md"></i> Doctor
                                </div>
                                <div class="detail-value">${consultation.doctor_name}</div>
                            </div>

                            <div class="detail-group">
                                <div class="detail-label">
                                    <i class="fas fa-calendar"></i> Preferred Date
                                </div>
                                <div class="detail-value">${consultation.preferred_date}</div>
                            </div>

                            <div class="detail-group">
                                <div class="detail-label">
                                    <i class="fas fa-clock"></i> Preferred Time
                                </div>
                                <div class="detail-value">${consultation.preferred_time}</div>
                            </div>

                            <div class="detail-group">
                                <div class="detail-label">
                                    <i class="fas fa-exclamation-triangle"></i> Urgency
                                </div>
                                <div class="detail-value" style="text-transform: capitalize; color: ${consultation.urgency_level === 'emergency' ? 'var(--danger)' : consultation.urgency_level === 'urgent' ? 'var(--warning)' : 'var(--success)'};">
                                    ${consultation.urgency_level}
                                </div>
                            </div>

                            <div class="detail-group">
                                <div class="detail-label">
                                    <i class="fas fa-stethoscope"></i> Type
                                </div>
                                <div class="detail-value" style="text-transform: capitalize;">
                                    ${consultation.consultation_type.replace('_', ' ')}
                                </div>
                            </div>

                            <div class="detail-group">
                                <div class="detail-label">
                                    <i class="fas fa-calendar-plus"></i> Submitted
                                </div>
                                <div class="detail-value">${createdDate} at ${createdTime}</div>
                            </div>
                        </div>

                        ${consultation.symptoms ? `
                            <div class="symptoms-section">
                                <h4><i class="fas fa-notes-medical"></i> Symptoms / Reason</h4>
                                <p>${consultation.symptoms}</p>
                            </div>
                        ` : ''}

                        ${consultation.medical_history ? `
                            <div class="symptoms-section">
                                <h4><i class="fas fa-history"></i> Medical History</h4>
                                <p>${consultation.medical_history}</p>
                            </div>
                        ` : ''}

                        <div class="blockchain-info">
                            <h4><i class="fas fa-cube"></i> Blockchain Verification</h4>
                            <p><strong>Transaction Hash:</strong></p>
                            <div class="blockchain-hash">${consultation.blockchain_hash}</div>
                            <p style="margin-top: 0.5rem;"><strong>Wallet Address:</strong> ${consultation.wallet_address}</p>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = consultationsHtml;
        }

        function showError(message) {
            const container = document.getElementById('consultations-container');
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle" style="color: var(--danger);"></i>
                    <h3>Error Loading Consultations</h3>
                    <p>${message}</p>
                    <button onclick="loadConsultations()" class="btn btn-primary">
                        <i class="fas fa-refresh"></i> Try Again
                    </button>
                </div>
            `;
        }
    </script>
</body>
</html>
