#!/usr/bin/env python3
"""
Test script for admin content management functionality
"""

import requests
import json

def test_admin_content_management():
    """Test the admin content management functionality"""
    
    print("🧪 TESTING ADMIN CONTENT MANAGEMENT")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # Test 1: Login as admin
        print("1. Testing admin login...")
        login_data = {
            'email': '<EMAIL>',
            'password': 'admin123'
        }
        
        login_response = session.post(
            'http://127.0.0.1:5000/auth/login',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(login_data)
        )
        
        if login_response.status_code == 200:
            print("   ✅ Admin login successful")
        else:
            print(f"   ❌ Admin login failed: {login_response.status_code}")
            return
        
        # Test 2: Access content management page
        print("2. Testing content management page access...")
        content_page_response = session.get('http://127.0.0.1:5000/admin/manage-content')
        
        if content_page_response.status_code == 200:
            print("   ✅ Content management page accessible")
        else:
            print(f"   ❌ Content management page failed: {content_page_response.status_code}")
            return
        
        # Test 3: Test nutrition content API
        print("3. Testing nutrition content API...")
        nutrition_response = session.get('http://127.0.0.1:5000/admin/api/content/nutrition')
        
        if nutrition_response.status_code == 200:
            print("   ✅ Nutrition API working")
            data = nutrition_response.json()
            if 'data' in data:
                print(f"   📊 Current nutrition items: {len(data['data'])}")
        else:
            print(f"   ❌ Nutrition API failed: {nutrition_response.status_code}")
            print(f"   Response: {nutrition_response.text}")
        
        # Test 4: Add new nutrition content
        print("4. Testing add nutrition content...")
        new_nutrition_data = {
            'title': 'First Trimester Nutrition',
            'description': 'Essential nutrition guidelines for the first trimester of pregnancy',
            'category': 'pregnancy',
            'trimester': 'first',
            'foods': 'Folic acid rich foods, lean proteins, whole grains',
            'tips': 'Take prenatal vitamins, avoid raw fish, limit caffeine'
        }
        
        add_response = session.post(
            'http://127.0.0.1:5000/admin/api/content/nutrition',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(new_nutrition_data)
        )
        
        print(f"   📋 Add nutrition response status: {add_response.status_code}")
        print(f"   📋 Add nutrition response: {add_response.text}")
        
        if add_response.status_code == 200:
            result = add_response.json()
            if result.get('success'):
                print("   ✅ Nutrition content added successfully!")
                content_id = result.get('id')
            else:
                print(f"   ❌ Nutrition content add failed: {result.get('error')}")
        else:
            print(f"   ❌ Nutrition content add API failed with status {add_response.status_code}")
        
        # Test 5: Test FAQ content API
        print("5. Testing FAQ content API...")
        faq_response = session.get('http://127.0.0.1:5000/admin/api/content/faq')
        
        if faq_response.status_code == 200:
            print("   ✅ FAQ API working")
            data = faq_response.json()
            if 'data' in data:
                print(f"   📊 Current FAQ items: {len(data['data'])}")
        else:
            print(f"   ❌ FAQ API failed: {faq_response.status_code}")
        
        # Test 6: Add new FAQ content
        print("6. Testing add FAQ content...")
        new_faq_data = {
            'question': 'What foods should I avoid during pregnancy?',
            'answer': 'Avoid raw or undercooked meats, fish high in mercury, unpasteurized dairy products, raw eggs, and excessive caffeine.',
            'category': 'nutrition'
        }
        
        add_faq_response = session.post(
            'http://127.0.0.1:5000/admin/api/content/faq',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(new_faq_data)
        )
        
        print(f"   📋 Add FAQ response status: {add_faq_response.status_code}")
        print(f"   📋 Add FAQ response: {add_faq_response.text}")
        
        # Test 7: Test government schemes API
        print("7. Testing government schemes API...")
        schemes_response = session.get('http://127.0.0.1:5000/admin/api/content/schemes')
        
        if schemes_response.status_code == 200:
            print("   ✅ Government schemes API working")
            data = schemes_response.json()
            if 'data' in data:
                print(f"   📊 Current schemes: {len(data['data'])}")
        else:
            print(f"   ❌ Government schemes API failed: {schemes_response.status_code}")
        
        # Test 8: Add new government scheme
        print("8. Testing add government scheme...")
        new_scheme_data = {
            'name': 'Pradhan Mantri Matru Vandana Yojana',
            'description': 'A maternity benefit program for pregnant women and lactating mothers',
            'eligibility': 'Pregnant women and lactating mothers for first living child',
            'benefits': 'Cash incentive of Rs. 5000 in three installments',
            'how_to_apply': 'Apply through Anganwadi Centers or health facilities'
        }
        
        add_scheme_response = session.post(
            'http://127.0.0.1:5000/admin/api/content/schemes',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(new_scheme_data)
        )
        
        print(f"   📋 Add scheme response status: {add_scheme_response.status_code}")
        print(f"   📋 Add scheme response: {add_scheme_response.text}")
        
        print("\n🎉 ADMIN CONTENT MANAGEMENT TESTING COMPLETE!")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_admin_content_management()
