<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doctor Appointment Booking - Baby Care System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary: #f472b6; /* Main Pink */
            --primary-dark: #ec4899;
            --secondary: #c084fc; /* Soft Purple */
            --secondary-dark: #a855f7;
            --accent: #fde047; /* Sunny Yellow */
            --info: #7dd3fc; /* Soft Blue */
            --light: #fdf2f8; /* Very light pink tint */
            --dark: #581c87; /* Dark Purple */
            --gray: #64748b;
            --light-gray: #e2e8f0;
            --success: #34d399;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --shadow: 0 4px 20px rgba(0,0,0,0.08);
            --shadow-hover: 0 8px 30px rgba(0,0,0,0.12);
            --border-radius: 20px;
        }
        
        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: var(--light);
            min-height: 100vh;
            margin: 0;
            overflow-x: hidden;
            padding-top: 80px;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.07);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 0.5rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 70px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark);
            text-decoration: none;
        }

        .logo-icon {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            align-items: center;
        }

        .nav-link {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            transition: var(--transition);
            position: relative;
            padding: 0.8rem 1.2rem;
            border-radius: 25px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
        }

        .nav-link:hover {
             color: var(--primary);
        }
        
        .nav-link.active {
            color: white;
            background: var(--primary);
            box-shadow: 0 4px 15px rgba(244, 114, 182, 0.4);
        }
        
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--dark);
            cursor: pointer;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .page-header {
            text-align: center;
            padding: 3rem 0;
        }

        .page-header h1 {
            font-size: 2.8rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }

        .page-header p {
            font-size: 1.2rem;
            color: var(--gray);
            max-width: 700px;
            margin: 0 auto;
        }
        
        .card {
            background: white;
            padding: 2.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }
        
        .card-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .card-title i {
            color: var(--primary);
        }

        /* Doctor Selection */
        .doctors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }
        
        .doctor-card {
            background: var(--light);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            transition: var(--transition);
        }

        .doctor-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-hover);
        }
        
        .doctor-img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 5px solid white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }

        .doctor-card h4 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark);
        }

        .doctor-card span {
            color: var(--primary);
            font-weight: 500;
            display: block;
            margin-bottom: 1rem;
        }

        .btn-select-doctor {
            padding: 0.75rem 1.5rem;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }
        .btn-select-doctor:hover {
            background: var(--primary-dark);
            box-shadow: var(--shadow);
        }

        /* Appointments Display */
        .appointment-item {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: var(--shadow);
            border-left: 4px solid var(--primary);
            transition: var(--transition);
        }

        .appointment-item:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-2px);
        }

        .appointment-item.confirmed {
            border-left-color: var(--success);
        }

        .appointment-item.cancelled {
            border-left-color: #ef4444;
            opacity: 0.7;
        }

        .appointment-item.completed {
            border-left-color: #10b981;
        }

        .appointment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .appointment-status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .appointment-status.pending {
            background: #fef3c7;
            color: #d97706;
        }

        .appointment-status.confirmed {
            background: #d1fae5;
            color: #059669;
        }

        .appointment-status.cancelled {
            background: #fee2e2;
            color: #dc2626;
        }

        .appointment-status.completed {
            background: #d1fae5;
            color: #059669;
        }

        .appointment-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .appointment-detail {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--gray);
        }

        .appointment-detail i {
            color: var(--primary);
            width: 16px;
        }

        .loading-message, .no-appointments {
            text-align: center;
            padding: 2rem;
            color: var(--gray);
        }

        .no-appointments {
            background: white;
            border-radius: 15px;
            box-shadow: var(--shadow);
        }

        /* Appointment Form */
        .appointment-form-layout {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.25rem;
        }
        
        .form-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--gray);
        }
        
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            font-size: 1rem;
            transition: var(--transition);
            font-family: 'Inter', sans-serif;
        }
        
        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(244, 114, 182, 0.2);
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .time-slots {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 0.75rem;
        }

        .time-slot {
            padding: 0.75rem;
            background: var(--light);
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
        }

        .time-slot:hover {
            border-color: var(--secondary);
            color: var(--secondary-dark);
        }
        
        .time-slot.selected {
            background: var(--secondary);
            color: white;
            border-color: var(--secondary-dark);
            font-weight: 600;
        }

        .btn-submit {
            grid-column: 1 / -1;
            padding: 1rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-top: 1rem;
        }

        .btn-submit:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-hover);
        }

        #booking-form-section {
            display: none;
        }
        
        @media (max-width: 992px) {
           .appointment-form-layout {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            body {
                padding-top: 70px;
            }
            .nav-menu {
                display: none;
                position: absolute;
                top: 70px;
                left: 0;
                width: 100%;
                background: white;
                flex-direction: column;
                padding: 1rem;
                box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            }
            .nav-menu.active {
                display: flex;
            }
            .mobile-menu-btn {
                display: block;
            }
            .page-header h1 {
                font-size: 2.2rem;
            }
            .nav-container, .container {
                padding: 0 1rem;
            }
            .card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="/" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <span>Appointments</span>
            </a>
            <nav>
                <ul class="nav-menu" id="navMenu">
                    <li><a href="/" class="nav-link"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="/pregnancy" class="nav-link"><i class="fas fa-heart"></i> Pregnancy</a></li>
                    <li><a href="/babycare" class="nav-link active"><i class="fas fa-baby"></i> Baby Care</a></li>
                </ul>
            </nav>
            <button class="mobile-menu-btn" id="mobileMenuBtn" aria-label="Toggle navigation menu">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="page-header">
            <div class="container">
                <h1>Book a Doctor's Appointment</h1>
                <p>Schedule a visit with one of our expert pediatricians at your convenience. Choose a doctor below to get started.</p>
            </div>
        </div>

        <div class="container">
            <!-- My Appointments Section -->
            <div class="card" id="my-appointments-section">
                <h2 class="card-title"><i class="fas fa-calendar-check"></i> My Appointments</h2>
                <div id="appointments-list">
                    <div class="loading-message">
                        <i class="fas fa-spinner fa-spin"></i> Loading your appointments...
                    </div>
                </div>
            </div>

            <!-- Doctor Selection -->
            <div class="card">
                <h2 class="card-title"><i class="fas fa-user-md"></i> Choose Your Pediatrician</h2>
                <div class="doctors-grid">
                    <div class="doctor-card">
                        <img src="https://placehold.co/120x120/f472b6/ffffff?text=Dr.+A" alt="Dr. Anya Sharma" class="doctor-img">
                        <h4>Dr. Anya Sharma</h4>
                        <span>General Pediatrics</span>
                        <button class="btn-select-doctor" data-doctor="Dr. Anya Sharma" data-doctor-id="3">Book Now</button>
                    </div>
                    <div class="doctor-card">
                        <img src="https://placehold.co/120x120/c084fc/ffffff?text=Dr.+R" alt="Dr. Rohan Verma" class="doctor-img">
                        <h4>Dr. Rohan Verma</h4>
                        <span>Neonatology</span>
                        <button class="btn-select-doctor" data-doctor="Dr. Rohan Verma" data-doctor-id="4">Book Now</button>
                    </div>
                    <div class="doctor-card">
                        <img src="https://placehold.co/120x120/7dd3fc/ffffff?text=Dr.+S" alt="Dr. Sunita Desai" class="doctor-img">
                        <h4>Dr. Sunita Desai</h4>
                        <span>Developmental Pediatrics</span>
                        <button class="btn-select-doctor" data-doctor="Dr. Sunita Desai" data-doctor-id="5">Book Now</button>
                    </div>
                </div>
            </div>

            <!-- Appointment Booking Form -->
            <div class="card" id="booking-form-section">
                <h2 class="card-title"><i class="fas fa-calendar-alt"></i> Schedule Your Visit</h2>
                <form id="appointment-form">
                    <div class="appointment-form-layout">
                        <div class="form-group">
                            <label for="doctor-name">Doctor</label>
                            <input type="text" id="doctor-name" name="doctor-name" readonly>
                            <input type="hidden" id="doctor-id" name="doctor-id">
                        </div>
                        <div class="form-group">
                            <label for="parent-name">Parent's Name</label>
                            <input type="text" id="parent-name" name="parent-name" required placeholder="Enter your full name">
                        </div>
                         <div class="form-group">
                            <label for="child-name">Child's Name</label>
                            <input type="text" id="child-name" name="child-name" required placeholder="Enter your child's name">
                        </div>
                        <div class="form-group">
                            <label for="appointment-date">Date</label>
                            <input type="date" id="appointment-date" name="appointment-date" required>
                        </div>
                        <div class="form-group" style="grid-column: 1 / -1;">
                             <label>Available Time Slots</label>
                             <div class="time-slots" id="time-slots">
                                <!-- Time slots will be populated by JS -->
                             </div>
                        </div>
                         <div class="form-group" style="grid-column: 1 / -1;">
                            <label for="reason">Reason for Visit</label>
                            <textarea id="reason" name="reason" placeholder="e.g., Regular checkup, vaccination, fever..."></textarea>
                        </div>
                        <input type="hidden" id="selected-time" name="selected-time">
                        <button type="submit" class="btn-submit">Confirm Appointment</button>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const doctorButtons = document.querySelectorAll('.btn-select-doctor');
            const bookingFormSection = document.getElementById('booking-form-section');
            const doctorNameInput = document.getElementById('doctor-name');
            const doctorIdInput = document.getElementById('doctor-id');
            const appointmentForm = document.getElementById('appointment-form');
            const timeSlotsContainer = document.getElementById('time-slots');
            const selectedTimeInput = document.getElementById('selected-time');
            const appointmentDateInput = document.getElementById('appointment-date');
            
            const availableSlots = [
                '09:00 AM', '09:30 AM', '10:00 AM', '10:30 AM', 
                '11:00 AM', '11:30 AM', '02:00 PM', '02:30 PM', 
                '03:00 PM', '03:30 PM', '04:00 PM', '04:30 PM'
            ];

            function generateTimeSlots() {
                timeSlotsContainer.innerHTML = '';
                availableSlots.forEach(slot => {
                    const slotEl = document.createElement('div');
                    slotEl.classList.add('time-slot');
                    slotEl.textContent = slot;
                    slotEl.dataset.time = slot;
                    timeSlotsContainer.appendChild(slotEl);
                });
            }

            doctorButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const doctorName = button.dataset.doctor;
                    const doctorId = button.dataset.doctorId;
                    doctorNameInput.value = doctorName;
                    doctorIdInput.value = doctorId;
                    bookingFormSection.style.display = 'block';
                    bookingFormSection.scrollIntoView({ behavior: 'smooth' });
                    generateTimeSlots();
                });
            });

            timeSlotsContainer.addEventListener('click', (e) => {
                if (e.target.classList.contains('time-slot')) {
                    document.querySelectorAll('.time-slot').forEach(slot => slot.classList.remove('selected'));
                    e.target.classList.add('selected');
                    selectedTimeInput.value = e.target.dataset.time;
                }
            });
            
            // Set min date to today
            const today = new Date().toISOString().split('T')[0];
            appointmentDateInput.setAttribute('min', today);
            appointmentDateInput.value = today;

            appointmentForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                if (!selectedTimeInput.value) {
                    showNotification('Please select a time slot.', 'error');
                    return;
                }

                const submitBtn = this.querySelector('.btn-submit');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = 'Booking...';
                submitBtn.disabled = true;

                try {
                    const appointmentDateTime = new Date(`${appointmentDateInput.value} ${selectedTimeInput.value}`);

                    const appointmentData = {
                        doctor_id: doctorIdInput.value,
                        doctor_name: doctorNameInput.value,
                        appointment_type: 'Baby Care Checkup',
                        appointment_date: appointmentDateTime.toISOString(),
                        purpose: document.getElementById('reason').value,
                        clinic_name: 'Baby Care Clinic',
                        patient_name: document.getElementById('parent-name').value,
                        child_name: document.getElementById('child-name').value
                    };

                    const response = await fetch('/api/babycare/appointments', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(appointmentData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        showNotification(`Appointment request sent to ${appointmentData.doctor_name}! You'll receive confirmation once the doctor approves.`, 'success');
                        appointmentForm.reset();
                        bookingFormSection.style.display = 'none';
                        document.querySelectorAll('.time-slot').forEach(slot => slot.classList.remove('selected'));
                        // Refresh appointments list
                        loadAppointments();
                    } else {
                        showNotification(result.error || 'Failed to book appointment', 'error');
                    }
                } catch (error) {
                    console.error('Error booking appointment:', error);
                    showNotification('Error booking appointment. Please try again.', 'error');
                } finally {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            });

            function showNotification(message, type) {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 90px;
                    right: 20px;
                    padding: 15px 25px;
                    border-radius: 10px;
                    color: white;
                    font-weight: 500;
                    z-index: 9999;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                    background: ${type === 'success' ? 'var(--success)' : 'var(--primary)'};
                    opacity: 0;
                    transform: translateX(20px);
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                `;
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => { 
                    notification.style.opacity = '1';
                    notification.style.transform = 'translateX(0)';
                }, 10);
                
                setTimeout(() => {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateX(20px)';
                    setTimeout(() => { if(notification.parentNode) {document.body.removeChild(notification);} }, 400);
                }, 4000);
            }

            // Load appointments on page load
            async function loadAppointments() {
                try {
                    const response = await fetch('/api/babycare/appointments');
                    const data = await response.json();

                    const appointmentsList = document.getElementById('appointments-list');

                    if (data.success && data.appointments && data.appointments.length > 0) {
                        appointmentsList.innerHTML = data.appointments.map(appointment => {
                            const statusClass = appointment.status || 'pending';
                            const appointmentDate = new Date(appointment.appointment_date);
                            const formattedDate = appointmentDate.toLocaleDateString();
                            const formattedTime = appointmentDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

                            return `
                                <div class="appointment-item ${statusClass}">
                                    <div class="appointment-header">
                                        <h3>${appointment.doctor_name}</h3>
                                        <span class="appointment-status ${statusClass}">${statusClass}</span>
                                    </div>
                                    <div class="appointment-details">
                                        <div class="appointment-detail">
                                            <i class="fas fa-user"></i>
                                            <span>Patient: ${appointment.patient_name || 'N/A'}</span>
                                        </div>
                                        <div class="appointment-detail">
                                            <i class="fas fa-baby"></i>
                                            <span>Child: ${appointment.child_name || 'N/A'}</span>
                                        </div>
                                        <div class="appointment-detail">
                                            <i class="fas fa-calendar"></i>
                                            <span>${formattedDate}</span>
                                        </div>
                                        <div class="appointment-detail">
                                            <i class="fas fa-clock"></i>
                                            <span>${formattedTime}</span>
                                        </div>
                                        <div class="appointment-detail">
                                            <i class="fas fa-stethoscope"></i>
                                            <span>${appointment.appointment_type || 'Baby Care Checkup'}</span>
                                        </div>
                                        <div class="appointment-detail">
                                            <i class="fas fa-hospital"></i>
                                            <span>${appointment.clinic_name || 'Baby Care Clinic'}</span>
                                        </div>
                                    </div>
                                    ${appointment.purpose ? `
                                        <div class="appointment-detail">
                                            <i class="fas fa-notes-medical"></i>
                                            <span>Purpose: ${appointment.purpose}</span>
                                        </div>
                                    ` : ''}
                                    ${appointment.notes ? `
                                        <div class="appointment-detail">
                                            <i class="fas fa-comment"></i>
                                            <span>Notes: ${appointment.notes}</span>
                                        </div>
                                    ` : ''}
                                </div>
                            `;
                        }).join('');
                    } else {
                        appointmentsList.innerHTML = `
                            <div class="no-appointments">
                                <i class="fas fa-calendar-times" style="font-size: 3rem; color: var(--gray); margin-bottom: 1rem;"></i>
                                <h3>No appointments found</h3>
                                <p>You haven't booked any appointments yet. Use the form below to schedule your first appointment.</p>
                            </div>
                        `;
                    }
                } catch (error) {
                    console.error('Error loading appointments:', error);
                    document.getElementById('appointments-list').innerHTML = `
                        <div class="no-appointments">
                            <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #ef4444; margin-bottom: 1rem;"></i>
                            <h3>Error loading appointments</h3>
                            <p>There was an error loading your appointments. Please refresh the page and try again.</p>
                        </div>
                    `;
                }
            }

            // Load appointments when page loads
            document.addEventListener('DOMContentLoaded', loadAppointments);

            // Mobile menu toggle
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const navMenu = document.getElementById('navMenu');

            if (mobileMenuBtn && navMenu) {
                mobileMenuBtn.addEventListener('click', function() {
                    navMenu.classList.toggle('active');
                });
            }
        });
    </script>
</body>
</html>
