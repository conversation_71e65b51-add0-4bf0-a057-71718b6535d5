<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doctor Dashboard - Maternal and Child Health Monitoring</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #4caf50;
            --primary-dark: #388e3c;
            --secondary: #2196f3;
            --accent: #ff9800;
            --success: #4caf50;
            --warning: #ff9800;
            --danger: #f44336;
            --light: #f8fafc;
            --dark: #2d3748;
            --gray: #718096;
            --light-gray: #e2e8f0;
            --transition: all 0.3s ease;
            --shadow: 0 4px 20px rgba(0,0,0,0.1);
            --shadow-hover: 0 8px 30px rgba(0,0,0,0.15);
            --border-radius: 16px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        /* Navigation */
        .doctor-nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary);
        }

        .nav-logo i {
            font-size: 2rem;
            color: #764ba2;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateY(-2px);
        }

        /* Main Container */
        .doctor-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Header */
        .doctor-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .doctor-header h1 {
            color: var(--primary);
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .doctor-header p {
            color: #666;
            font-size: 1.1rem;
        }

        /* Statistics Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            animation: slideInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card h3 {
            color: #667eea;
            font-size: 1rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .action-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .action-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .action-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .action-card h3 {
            color: #333;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .action-card p {
            color: #666;
            font-size: 0.9rem;
        }

        /* Recent Activity */
        .recent-activity {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .recent-activity h2 {
            color: #667eea;
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }

        .activity-icon.appointment {
            background: #4caf50;
        }

        .activity-icon.consultation {
            background: #2196f3;
        }

        .activity-icon.report {
            background: #ff9800;
        }

        .activity-icon.urgent {
            background: #f44336;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .activity-description {
            color: #666;
            font-size: 0.9rem;
        }

        .activity-time {
            color: #999;
            font-size: 0.8rem;
        }

        .activity-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            transition: var(--transition);
            cursor: pointer;
        }

        .status-badge, .risk-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-right: 5px;
            color: white !important;
        }

        .btn-refresh {
            background: var(--secondary);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }

        .btn-refresh:hover {
            background: var(--primary);
            transform: translateY(-2px);
        }

        /* Loading and Error States */
        .loading, .error {
            text-align: center;
            padding: 2rem;
            color: var(--gray);
            font-style: italic;
        }

        .error {
            color: var(--danger);
        }

        .loading::before {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--light-gray);
            border-top: 2px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        /* Animations */
        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-content {
                padding: 0 1rem;
            }

            .doctor-container {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .quick-actions {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="doctor-nav">
        <div class="nav-content">
            <div class="nav-logo">
                <i class="fas fa-user-md"></i>
                <span>Doctor Portal</span>
            </div>
            <div class="nav-links">
                <a href="/doctor" class="active"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/doctor/patients"><i class="fas fa-users"></i> Patients</a>
                <a href="/doctor/appointments"><i class="fas fa-calendar-check"></i> Appointments</a>
                <a href="/doctor/generate_id"><i class="fas fa-id-card"></i> Generate ID</a>
                <a href="/doctor/consultations"><i class="fas fa-stethoscope"></i> Consultations</a>
                <a href="/doctor/reports"><i class="fas fa-file-medical"></i> Reports</a>
                <a href="/auth/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="doctor-container">
        <!-- Header -->
        <div class="doctor-header">
            <div>
                <h1>
                    <i class="fas fa-user-md"></i>
                    Doctor Dashboard
                </h1>
                <p>Comprehensive maternal and child health monitoring system</p>
            </div>
            <button onclick="refreshDashboard()" class="btn btn-primary" style="padding: 0.75rem 1.5rem; border: none; border-radius: 12px; background: var(--primary); color: white; cursor: pointer; font-weight: 600; transition: var(--transition);">
                <i class="fas fa-sync-alt"></i> Refresh Data
            </button>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card" style="animation-delay: 0.1s;">
                <h3><i class="fas fa-users"></i> Total Patients</h3>
                <div class="stat-number" id="total-patients">-</div>
                <div class="stat-label">Active patients under care</div>
            </div>

            <div class="stat-card" style="animation-delay: 0.2s;">
                <h3><i class="fas fa-calendar-day"></i> Today's Appointments</h3>
                <div class="stat-number" id="today-appointments">-</div>
                <div class="stat-label">Scheduled for today</div>
            </div>

            <div class="stat-card" style="animation-delay: 0.3s;">
                <h3><i class="fas fa-stethoscope"></i> Consultations</h3>
                <div class="stat-number" id="today-consultations">-</div>
                <div class="stat-label">Completed today</div>
            </div>

            <div class="stat-card" style="animation-delay: 0.4s;">
                <h3><i class="fas fa-file-medical"></i> Pending Reports</h3>
                <div class="stat-number" id="pending-reports">-</div>
                <div class="stat-label">Awaiting review</div>
            </div>

            <div class="stat-card" style="animation-delay: 0.5s;">
                <h3><i class="fas fa-user-plus"></i> New Patients</h3>
                <div class="stat-number" id="new-patients">-</div>
                <div class="stat-label">This month</div>
            </div>

            <div class="stat-card" style="animation-delay: 0.6s;">
                <h3><i class="fas fa-chart-line"></i> This Week</h3>
                <div class="stat-number" id="week-appointments">-</div>
                <div class="stat-label">Total appointments</div>
            </div>

            <div class="stat-card" style="animation-delay: 0.7s;">
                <h3><i class="fas fa-baby"></i> Pregnant Patients</h3>
                <div class="stat-number" id="pregnant-patients">-</div>
                <div class="stat-label">Currently expecting</div>
            </div>

            <div class="stat-card" style="animation-delay: 0.8s;">
                <h3><i class="fas fa-exclamation-triangle"></i> High Risk</h3>
                <div class="stat-number" id="high-risk-patients">-</div>
                <div class="stat-label">High-risk pregnancies</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/doctor/patients" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3>Manage Patients</h3>
                <p>View patient records, medical history, and treatment plans</p>
            </a>

            <a href="/doctor/appointments" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-calendar-plus"></i>
                </div>
                <h3>Schedule Appointments</h3>
                <p>Book new appointments and manage existing schedules</p>
            </a>

            <a href="/doctor/consultations" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-stethoscope"></i>
                </div>
                <h3>Consultations</h3>
                <p>Conduct virtual consultations and follow-ups</p>
            </a>

            <a href="/doctor/reports" class="action-card">
                <div class="action-icon">
                    <i class="fas fa-file-medical-alt"></i>
                </div>
                <h3>Medical Reports</h3>
                <p>Review lab results, generate reports, and track progress</p>
            </a>
        </div>

        <!-- Patient Management -->
        <div class="recent-activity">
            <h2><i class="fas fa-users"></i> Recent Patients</h2>
            <div id="patient-list">
                <div class="loading">Loading patients...</div>
            </div>
            <div style="text-align: center; margin-top: 1rem;">
                <button onclick="loadAllPatients()" class="btn-refresh">
                    <i class="fas fa-eye"></i> View All Patients
                </button>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="recent-activity">
            <h2><i class="fas fa-clock"></i> Recent Activity</h2>
            <div id="recent-activities">
                <div class="loading">Loading recent activities...</div>
            </div>
        </div>
    </div>

    <script>
        // Load dashboard data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardStats();
            loadRecentActivity();
            loadRecentPatients();
        });

        async function loadDashboardStats() {
            try {
                const response = await fetch('/doctor/api/dashboard-stats');
                const data = await response.json();

                if (data.success) {
                    const stats = data.stats;

                    // Update all statistics
                    document.getElementById('total-patients').textContent = stats.patients.total;
                    document.getElementById('today-appointments').textContent = stats.appointments.today;
                    document.getElementById('today-consultations').textContent = stats.consultations.today;
                    document.getElementById('pending-reports').textContent = stats.reports.pending;
                    document.getElementById('new-patients').textContent = stats.patients.new_this_month;
                    document.getElementById('week-appointments').textContent = stats.appointments.this_week;
                    document.getElementById('pregnant-patients').textContent = stats.patients.pregnant || 0;
                    document.getElementById('high-risk-patients').textContent = stats.health_metrics.high_risk_pregnancies || 0;

                    // Update recent activity if available
                    if (data.recent_activity) {
                        displayRecentActivity(data.recent_activity);
                    }
                } else {
                    console.error('Failed to load dashboard stats:', data.error);
                    showError('Failed to load dashboard statistics');
                }
            } catch (error) {
                console.error('Error loading dashboard stats:', error);
                showError('Error loading dashboard statistics');
            }
        }

        async function loadRecentActivity() {
            try {
                const response = await fetch('/doctor/api/recent-activity');
                const data = await response.json();

                if (data.success) {
                    displayRecentActivity(data.activities);
                } else {
                    console.error('Failed to load recent activity:', data.error);
                    showError('Failed to load recent activity');
                }
            } catch (error) {
                console.error('Error loading recent activity:', error);
                showError('Error loading recent activity');
            }
        }

        function displayRecentActivity(activityData) {
            const container = document.getElementById('recent-activities');
            if (!container) return;

            let activities = [];

            // Handle different data formats
            if (Array.isArray(activityData)) {
                activities = activityData;
            } else if (activityData && activityData.recent_patients) {
                // Convert patient data to activity format
                activities = activityData.recent_patients.map(patient => ({
                    type: 'patient',
                    patient: patient.full_name,
                    description: 'New patient registration',
                    time: patient.created_at || new Date().toISOString(),
                    status: 'completed'
                }));

                // Add summary as an activity if available
                if (activityData.summary) {
                    activities.push({
                        type: 'summary',
                        patient: 'System',
                        description: activityData.summary,
                        time: new Date().toISOString(),
                        status: 'info'
                    });
                }
            }

            if (activities.length === 0) {
                container.innerHTML = '<div class="loading">No recent activities</div>';
                return;
            }

            container.innerHTML = activities.map(activity => {
                const timeAgo = getTimeAgo(new Date(activity.time));
                const iconClass = getActivityIcon(activity.type);

                return `
                    <div class="activity-item">
                        <div class="activity-icon ${activity.type}">
                            <i class="${iconClass}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">${activity.patient}</div>
                            <div class="activity-description">${activity.description}</div>
                            <div class="activity-time">${timeAgo}</div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getActivityIcon(type) {
            const icons = {
                'appointment': 'fas fa-calendar-check',
                'consultation': 'fas fa-stethoscope',
                'report': 'fas fa-file-medical',
                'urgent': 'fas fa-exclamation-triangle',
                'patient': 'fas fa-user-plus',
                'summary': 'fas fa-info-circle'
            };
            return icons[type] || 'fas fa-circle';
        }

        function getTimeAgo(date) {
            const now = new Date();
            const diffInMinutes = Math.floor((now - date) / (1000 * 60));

            if (diffInMinutes < 1) return 'Just now';
            if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;

            const diffInHours = Math.floor(diffInMinutes / 60);
            if (diffInHours < 24) return `${diffInHours} hours ago`;

            const diffInDays = Math.floor(diffInHours / 24);
            return `${diffInDays} days ago`;
        }

        function showError(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10001;
                padding: 15px 20px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                background: #f44336;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                max-width: 300px;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // Load recent patients
        async function loadRecentPatients() {
            try {
                const response = await fetch('/doctor/api/patients');
                const data = await response.json();

                if (data.success) {
                    displayRecentPatients(data.patients.slice(0, 5)); // Show only recent 5
                } else {
                    console.error('Failed to load patients:', data.error);
                    showError('Failed to load patients');
                }
            } catch (error) {
                console.error('Error loading patients:', error);
                showError('Error loading patients');
            }
        }

        function displayRecentPatients(patients) {
            const container = document.getElementById('patient-list');
            if (!container) return;

            if (patients.length === 0) {
                container.innerHTML = '<div class="loading">No patients found</div>';
                return;
            }

            container.innerHTML = patients.map(patient => {
                const statusClass = patient.status === 'Active' ? 'success' : 'warning';
                const riskClass = patient.risk_level === 'High' ? 'danger' :
                                 patient.risk_level === 'Medium' ? 'warning' : 'success';

                return `
                    <div class="activity-item" onclick="viewPatientDetails(${patient.id})" style="cursor: pointer;">
                        <div class="activity-icon patient">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">${patient.full_name}</div>
                            <div class="activity-description">
                                ${patient.email} • ${patient.phone}
                                <br><small>
                                    <span class="status-badge ${statusClass}" style="background: var(--${statusClass}); color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.7rem; margin-right: 5px;">${patient.status}</span>
                                    <span class="risk-badge ${riskClass}" style="background: var(--${riskClass}); color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.7rem;">${patient.risk_level} Risk</span>
                                </small>
                            </div>
                            <div class="activity-time">Registered: ${new Date(patient.created_at).toLocaleDateString()}</div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function viewPatientDetails(patientId) {
            // In a real app, this would open a patient details modal or page
            alert(`Opening patient details for ID: ${patientId}`);
        }

        function loadAllPatients() {
            // In a real app, this would navigate to the patients page
            window.location.href = '/doctor/patients';
        }

        // Refresh dashboard function
        function refreshDashboard() {
            // Show loading state
            const statsCards = document.querySelectorAll('.stat-number');
            statsCards.forEach(card => {
                card.textContent = '...';
            });

            // Reload data
            loadDashboardStats();
            loadRecentActivity();
            loadRecentPatients();
        }



        // Show error message
        function showError(message) {
            console.error(message);
            // You can add a toast notification here
        }

        // Auto-refresh dashboard every 5 minutes
        setInterval(() => {
            loadDashboardStats();
            loadRecentActivity();
            loadRecentPatients();
        }, 5 * 60 * 1000);
    </script>
</body>
</html>