<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Doctor Portal</title>

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

    <!-- Chart.js for statistics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        /* --- General <PERSON> Styling --- */
        body {
            margin: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8fafc;
            color: #333;
            line-height: 1.6;
        }

        /* --- Header and Navigation Bar --- */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            height: 70px;
        }

        .logo {
            font-size: 1.3rem;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
        }

        .logo i {
            color: #ffd700;
            margin-right: 10px;
            font-size: 1.5rem;
        }

        .nav-links {
            list-style: none;
            display: flex;
            align-items: center;
            height: 100%;
            margin: 0;
            padding: 0;
        }

        .nav-links a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1.5rem;
            height: 100%;
            text-decoration: none;
            color: rgba(255,255,255,0.9);
            font-weight: 500;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .nav-links a:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }

        .nav-links a.active {
            color: white;
            font-weight: 600;
            border-bottom: 3px solid #ffd700;
            background-color: rgba(255,255,255,0.1);
        }

        .user-info {
            color: rgba(255,255,255,0.9);
            font-size: 0.9rem;
        }

        .user-info strong {
            color: white;
            font-weight: 600;
        }

        /* --- Main Content Area --- */
        .main-container {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* --- Welcome Banner --- */
        .welcome-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .welcome-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .welcome-text h1 {
            margin: 0 0 0.5rem 0;
            font-size: 2.2rem;
            font-weight: 700;
        }

        .welcome-text p {
            margin: 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .welcome-stats {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 2rem;
            font-weight: 700;
            color: #ffd700;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* --- Dashboard Grid --- */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        /* --- Dashboard Cards --- */
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-decoration: none;
            color: inherit;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            border: 1px solid #f0f0f0;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .dashboard-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .dashboard-card .icon {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            border-radius: 50%;
            background: rgba(102, 126, 234, 0.1);
        }

        .dashboard-card h3 {
            margin: 0 0 1rem 0;
            font-size: 1.5rem;
            font-weight: 600;
            color: #2d3748;
        }

        .dashboard-card p {
            margin: 0 0 1.5rem 0;
            color: #718096;
            flex-grow: 1;
            line-height: 1.6;
        }

        .dashboard-card .go-to-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.8rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .dashboard-card .go-to-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        /* Card-specific icon colors */
        .card-appointments .icon { color: #667eea; }
        .card-patients .icon { color: #48bb78; }
        .card-reports .icon { color: #ed8936; }
        .card-generate-id .icon { color: #9f7aea; }

        /* Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #667eea;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin: 0;
        }

        .stat-label {
            color: #718096;
            font-size: 0.9rem;
            margin: 0.5rem 0 0 0;
        }

        .stat-change {
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }

        .stat-change.positive {
            color: #48bb78;
        }

        .stat-change.negative {
            color: #f56565;
        }

        /* Recent Activity Section */
        .recent-activity {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 1.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .activity-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f7fafc;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.2rem;
        }

        .activity-icon.appointment {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .activity-icon.patient {
            background: rgba(72, 187, 120, 0.1);
            color: #48bb78;
        }

        .activity-icon.report {
            background: rgba(237, 137, 54, 0.1);
            color: #ed8936;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 0.25rem 0;
        }

        .activity-description {
            color: #718096;
            font-size: 0.9rem;
            margin: 0;
        }

        .activity-time {
            color: #a0aec0;
            font-size: 0.8rem;
        }

        /* Loading and Message States */
        .loading {
            text-align: center;
            padding: 2rem;
            color: #718096;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #f0fff4;
            border: 1px solid #9ae6b4;
            color: #276749;
        }

        .alert-error {
            background-color: #fed7d7;
            border: 1px solid #feb2b2;
            color: #c53030;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header {
                padding: 0 1rem;
                height: auto;
                flex-direction: column;
                padding: 1rem;
            }

            .nav-links {
                justify-content: center;
                width: 100%;
                flex-wrap: wrap;
                margin-top: 1rem;
            }

            .nav-links a {
                padding: 0.5rem 1rem;
                height: auto;
                border-radius: 20px;
                margin: 0.25rem;
            }

            .main-container {
                padding: 1rem;
            }

            .welcome-content {
                flex-direction: column;
                text-align: center;
            }

            .welcome-stats {
                justify-content: center;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .welcome-text h1 {
                font-size: 1.8rem;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .welcome-text h1 {
                font-size: 1.5rem;
            }

            .dashboard-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header class="header">
        <div class="logo">
            <i class="fa-solid fa-user-doctor"></i>
            Maternal Care - Doctor Portal
        </div>
        <nav>
            <ul class="nav-links">
                <li><a href="{{ url_for('doctor.dashboard') }}" class="active">
                    <i class="fa-solid fa-house-chimney"></i>Dashboard
                </a></li>
                <li><a href="{{ url_for('doctor.appointments') }}">
                    <i class="fa-solid fa-calendar-check"></i>Appointments
                </a></li>
                <li><a href="{{ url_for('doctor.patients') }}">
                    <i class="fa-solid fa-users"></i>Patients
                </a></li>
                <li><a href="{{ url_for('doctor.generate_id') }}">
                    <i class="fa-solid fa-id-card"></i>Generate ID
                </a></li>
                <li><a href="{{ url_for('doctor.reports') }}">
                    <i class="fa-solid fa-file-lines"></i>Reports
                </a></li>
            </ul>
        </nav>
        <div class="user-info">
            Welcome, <strong>{{ user.full_name if user else 'Doctor' }}</strong>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-container">
        <!-- Welcome Banner -->
        <div class="welcome-banner">
            <div class="welcome-content">
                <div class="welcome-text">
                    <h1>Welcome back, Dr. {{ user.full_name.split()[-1] if user else 'Doctor' }}!</h1>
                    <p>Here's what's happening with your patients today</p>
                </div>
                <div class="welcome-stats">
                    <div class="stat-item">
                        <span class="stat-number" id="today-appointments">-</span>
                        <span class="stat-label">Today's Appointments</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="total-patients">-</span>
                        <span class="stat-label">Total Patients</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="pending-reports">-</span>
                        <span class="stat-label">Pending Reports</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Cards -->
        <div class="dashboard-grid">
            
            <!-- Appointments Card -->
            <a href="#" class="dashboard-card card-appointments">
                <div class="icon">
                    <i class="fa-solid fa-calendar-check"></i>
                </div>
                <h3>Appointments</h3>
                <p>View, schedule, and manage patient appointments.</p>
                <div class="go-to-btn">Go to Appointments</div>
            </a>

            <!-- Generate ID Card -->
            <a href="#" class="dashboard-card card-generate-id">
                <div class="icon">
                    <i class="fa-solid fa-id-card"></i>
                </div>
                <h3>Generate ID</h3>
                <p>Create and print new patient identification cards.</p>
                <div class="go-to-btn">Generate ID</div>
            </a>

            <!-- Reports Card -->
            <a href="#" class="dashboard-card card-reports">
                <div class="icon">
                    <i class="fa-solid fa-file-lines"></i>
                </div>
                <h3>Reports</h3>
                <p>Generate and view patient or administrative reports.</p>
                <div class="go-to-btn">View Reports</div>
            </a>

            <!-- Live Count Card (New Firestore Feature) -->
            <div class="dashboard-card card-live-count">
                <div class="icon">
                    <i class="fa-solid fa-bell-concierge"></i>
                </div>
                <h3>Live Patients In Queue</h3>
                <p>The current number of patients waiting today (real-time data).</p>
                <span id="live-count" class="live-count-value">--</span>
                <button id="update-count-btn">
                    <div id="loading-spinner" class="spinner"></div>
                    <i class="fa-solid fa-plus"></i> Update Queue
                </button>
            </div>

        </div>

    </main>
    
    <!-- Custom Message Box -->
    <div id="message-box" class="message-box"></div>


    <!-- Firebase/Firestore Script -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { 
            getAuth, 
            signInAnonymously, 
            signInWithCustomToken, 
            onAuthStateChanged 
        } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { 
            getFirestore, 
            doc, 
            setDoc, 
            getDoc, 
            onSnapshot, 
            updateDoc,
            increment
        } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";
        import { setLogLevel } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";
        
        // Setting Firebase log level to Debug for visibility in the console
        setLogLevel('Debug');

        // --- Global Variables (Mandatory for Canvas Environment) ---
        const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-doctor-portal-app-id';
        const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : null;
        const initialAuthToken = typeof __initial_auth_token !== 'undefined' ? __initial_auth_token : null;

        let db = null;
        let auth = null;
        let currentUserId = null;
        let isAuthReady = false;

        // --- Utility Functions ---

        /** Shows a temporary custom message box instead of using alert() */
        function showMessage(message) {
            const box = document.getElementById('message-box');
            box.textContent = message;
            box.classList.add('show');
            setTimeout(() => {
                box.classList.remove('show');
            }, 3000);
        }

        /** Handles setting up Firebase authentication and stores user ID */
        async function setupAuth() {
            try {
                if (!firebaseConfig) {
                    showMessage("Error: Firebase config is missing.");
                    console.error("Firebase config not found.");
                    return;
                }

                const app = initializeApp(firebaseConfig);
                auth = getAuth(app);
                db = getFirestore(app);

                // Sign in using the custom token or anonymously if the token is not available
                if (initialAuthToken) {
                    await signInWithCustomToken(auth, initialAuthToken);
                } else {
                    await signInAnonymously(auth);
                }

                onAuthStateChanged(auth, (user) => {
                    if (user) {
                        currentUserId = user.uid;
                        document.getElementById('user-id-display').textContent = currentUserId;
                        isAuthReady = true;
                        console.log("Authentication successful. User ID:", currentUserId);
                        // Start real-time listener only after successful authentication
                        setupRealtimeListener(); 
                    } else {
                        currentUserId = 'anonymous-' + crypto.randomUUID().substring(0, 8); // Fallback or anonymous ID
                        document.getElementById('user-id-display').textContent = "Not logged in (Anon ID: " + currentUserId + ")";
                        isAuthReady = true;
                        console.warn("User is signed out or anonymous.");
                        // Even if anonymous, we still want to try loading the public data
                        setupRealtimeListener();
                    }
                });

            } catch (error) {
                console.error("Firebase Initialization or Authentication Error:", error);
                showMessage("Database initialization failed. Check console for details.");
                document.getElementById('user-id-display').textContent = "ERROR";
            }
        }

        /** Defines the path to the public, shared document for the live count */
        function getCountDocRef() {
            // Using a PUBLIC path so all users share the same live count data
            const path = `/artifacts/${appId}/public/data/global_dashboard_stats/live_queue_count`;
            return doc(db, path);
        }

        /** Sets up the real-time listener for the patient count */
        function setupRealtimeListener() {
            if (!db || !isAuthReady) {
                console.warn("Database or Auth not ready. Skipping onSnapshot setup.");
                return;
            }

            const countDocRef = getCountDocRef();
            const liveCountElement = document.getElementById('live-count');

            // Listen for real-time changes
            onSnapshot(countDocRef, (docSnapshot) => {
                if (docSnapshot.exists()) {
                    const data = docSnapshot.data();
                    const count = data.count || 0;
                    liveCountElement.textContent = count;
                    console.log("Real-time update received. Count:", count);
                } else {
                    // Document doesn't exist, initialize it to 0
                    console.log("Count document not found. Initializing...");
                    setDoc(countDocRef, { count: 0, last_updated_by: currentUserId });
                    liveCountElement.textContent = 0;
                }
            }, (error) => {
                console.error("Firestore onSnapshot error:", error);
                showMessage("Failed to load live data.");
                liveCountElement.textContent = 'ERR';
            });
        }

        /** Increments the patient count in Firestore */
        async function updatePatientCount() {
            if (!db || !currentUserId) {
                showMessage("Please wait for authentication to complete.");
                return;
            }

            const button = document.getElementById('update-count-btn');
            const spinner = document.getElementById('loading-spinner');

            button.disabled = true;
            spinner.classList.add('spinner-active');

            try {
                const countDocRef = getCountDocRef();
                
                // Use updateDoc with increment for atomic counting
                await updateDoc(countDocRef, {
                    count: increment(1),
                    last_updated_by: currentUserId,
                    timestamp: new Date().toISOString()
                });
                
                // The UI will update automatically via the onSnapshot listener, 
                // so no need to update the DOM here.
                showMessage("Patient added to queue successfully!");

            } catch (error) {
                // If the document doesn't exist yet, it will throw an error. 
                // We'll check the error code and try to create it first.
                if (error.code === 'not-found') {
                    console.warn("Document not found during update. Attempting initial set.");
                    try {
                         await setDoc(countDocRef, {
                            count: 1,
                            last_updated_by: currentUserId,
                            timestamp: new Date().toISOString()
                        });
                        showMessage("Queue initialized and first patient added!");
                    } catch(setError) {
                        console.error("Initial setDoc failed:", setError);
                        showMessage("Failed to initialize queue data.");
                    }
                } else {
                    console.error("Error updating patient count:", error);
                    showMessage("Failed to update queue count: " + error.message);
                }

            } finally {
                button.disabled = false;
                spinner.classList.remove('spinner-active');
            }
        }

        // --- Event Listeners and Initialization ---
        
        document.addEventListener('DOMContentLoaded', () => {
            // Setup click listener for the update button
            document.getElementById('update-count-btn').addEventListener('click', updatePatientCount);
            
            // Start the Firebase setup process
            setupAuth();
        });

    </script>
</body>
</html>
