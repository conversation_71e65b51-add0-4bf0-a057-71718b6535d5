<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <PERSON></title>
    
    <!-- This line imports the icons used on the page (like the calendar, ID card, etc.) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

    <style>
        /* --- General Page Styling --- */
        body {
            margin: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #eef7ff; /* A very light blue background */
            color: #333;
        }

        /* --- Header and Navigation Bar --- */
        .header {
            background-color: #ffffff;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #e0e0e0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            height: 60px; /* Fixed height for the header */
        }

        .logo {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50; /* Dark blue-grey text */
            display: flex;
            align-items: center;
        }

        .logo i {
            color: #3498db; /* Blue icon */
            margin-right: 8px;
        }

        .nav-links {
            list-style: none;
            display: flex;
            align-items: center;
            height: 100%;
            margin: 0;
            padding: 0;
        }

        .nav-links a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1.2rem;
            height: 100%;
            text-decoration: none;
            color: #555;
            font-weight: 500;
            transition: background-color 0.2s ease-in-out, color 0.2s;
            border-bottom: 3px solid transparent;
        }

        .nav-links a:hover {
            background-color: #f5f5f5;
        }
        
        /* The ".active" class highlights the current page in the navigation */
        .nav-links a.active {
            color: #3498db;
            font-weight: 600;
            border-bottom: 3px solid #3498db;
        }

        /* --- Main Content Area --- */
        .main-container {
            padding: 1.5rem 2rem;
        }
        
        /* --- "Dashboard" Banner --- */
        .title-banner {
            background-color: #ffffff;
            border-left: 5px solid #3498db; /* Blue left border for emphasis */
            border-radius: 8px;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between; /* Added space between title and user info */
            gap: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        }

        .title-group {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .title-banner .icon {
            font-size: 2rem;
            color: #3498db;
        }

        .title-banner h1 {
            margin: 0;
            font-size: 1.8rem;
            color: #2c3e50;
        }

        .title-banner p {
            margin: 0.2rem 0 0 0;
            color: #555;
        }
        
        /* User ID Display */
        .user-info {
            text-align: right;
            font-size: 0.85rem;
            color: #555;
        }
        .user-info strong {
            display: block;
            color: #2c3e50;
            font-size: 0.9rem;
            word-break: break-all; /* Ensures long IDs wrap */
        }

        /* --- Dashboard Grid --- */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Adjusted min-width for mobile */
            gap: 1.5rem;
        }

        /* --- Dashboard Cards --- */
        .dashboard-card {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 1.5rem 2rem;
            text-decoration: none;
            color: inherit;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 12px rgba(0,0,0,0.15);
        }

        .dashboard-card .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .dashboard-card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.4rem;
        }

        .dashboard-card p {
            margin: 0;
            color: #666;
            flex-grow: 1; /* Pushes button/count to bottom */
            line-height: 1.4;
        }
        
        .dashboard-card .go-to-btn, #update-count-btn {
            margin-top: 1.5rem;
            background-color: #3498db;
            color: white;
            padding: 0.7rem 1.5rem;
            border-radius: 5px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: background-color 0.2s, transform 0.1s;
        }
        
        .dashboard-card .go-to-btn:hover, #update-count-btn:hover {
            background-color: #2980b9;
            transform: scale(1.02);
        }

        /* Card-specific icon colors */
        .card-appointments .icon { color: #3498db; } /* Blue */
        .card-generate-id .icon { color: #2ecc71; } /* Green */
        .card-reports .icon { color: #e74c3c; }    /* Red */
        .card-live-count .icon { color: #f39c12; } /* Orange */

        /* Live Count Specifics */
        .live-count-value {
            font-size: 3rem;
            font-weight: 700;
            color: #3498db;
            margin: 0.75rem 0;
            display: block;
            line-height: 1;
        }

        /* --- Custom Message Box (for alerts) --- */
        .message-box {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #333;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
            z-index: 1000;
            font-size: 0.9rem;
        }

        .message-box.show {
            opacity: 1;
            visibility: visible;
        }

        /* --- Loading Spinner (for API calls) --- */
        .spinner {
            border: 4px solid #f3f3f3; /* Light grey */
            border-top: 4px solid #3498db; /* Blue */
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: none; /* Hidden by default */
            margin: 0 10px 0 0;
        }

        .spinner-active {
            display: inline-block;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Adjustments */
        @media (max-width: 600px) {
            .header {
                padding: 0 1rem;
                height: auto;
                flex-direction: column;
                padding-bottom: 10px;
            }
            .nav-links {
                justify-content: center;
                width: 100%;
                flex-wrap: wrap;
                margin-top: 10px;
            }
            .nav-links a {
                padding: 0.5rem 0.8rem;
                height: auto;
            }
            .main-container {
                padding: 1rem;
            }
            .title-banner {
                flex-direction: column;
                align-items: flex-start;
                padding: 1rem;
                gap: 0.5rem;
            }
            .user-info {
                text-align: left;
                width: 100%;
                margin-top: 10px;
                padding-top: 5px;
                border-top: 1px solid #eee;
            }
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>

    <!-- Header Section -->
    <header class="header">
        <div class="logo">
            <i class="fa-solid fa-user-doctor"></i> Doctor Portal
        </div>
        <nav>
            <ul class="nav-links">
                <li><a href="#" class="active"><i class="fa-solid fa-house-chimney"></i>Dashboard</a></li>
                <li><a href="#"><i class="fa-solid fa-calendar-check"></i>Appointments</a></li>
                <li><a href="#"><i class="fa-solid fa-id-card"></i>Generate ID</a></li>
                <li><a href="#"><i class="fa-solid fa-file-lines"></i>Reports</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content of the Page -->
    <main class="main-container">

        <!-- Top Banner, Dashboard Title and User Info -->
        <div class="title-banner">
            <div class="title-group">
                <div class="icon">
                    <i class="fa-solid fa-house-chimney"></i>
                </div>
                <div>
                    <h1>Dashboard</h1>
                    <p>Quick access to all portal features</p>
                </div>
            </div>
            <!-- Authentication Status (MANDATORY) -->
            <div class="user-info">
                Logged in as:
                <strong id="user-id-display">Authenticating...</strong>
            </div>
        </div>

        <!-- Dashboard Cards -->
        <div class="dashboard-grid">
            
            <!-- Appointments Card -->
            <a href="#" class="dashboard-card card-appointments">
                <div class="icon">
                    <i class="fa-solid fa-calendar-check"></i>
                </div>
                <h3>Appointments</h3>
                <p>View, schedule, and manage patient appointments.</p>
                <div class="go-to-btn">Go to Appointments</div>
            </a>

            <!-- Generate ID Card -->
            <a href="#" class="dashboard-card card-generate-id">
                <div class="icon">
                    <i class="fa-solid fa-id-card"></i>
                </div>
                <h3>Generate ID</h3>
                <p>Create and print new patient identification cards.</p>
                <div class="go-to-btn">Generate ID</div>
            </a>

            <!-- Reports Card -->
            <a href="#" class="dashboard-card card-reports">
                <div class="icon">
                    <i class="fa-solid fa-file-lines"></i>
                </div>
                <h3>Reports</h3>
                <p>Generate and view patient or administrative reports.</p>
                <div class="go-to-btn">View Reports</div>
            </a>

            <!-- Live Count Card (New Firestore Feature) -->
            <div class="dashboard-card card-live-count">
                <div class="icon">
                    <i class="fa-solid fa-bell-concierge"></i>
                </div>
                <h3>Live Patients In Queue</h3>
                <p>The current number of patients waiting today (real-time data).</p>
                <span id="live-count" class="live-count-value">--</span>
                <button id="update-count-btn">
                    <div id="loading-spinner" class="spinner"></div>
                    <i class="fa-solid fa-plus"></i> Update Queue
                </button>
            </div>

        </div>

    </main>
    
    <!-- Custom Message Box -->
    <div id="message-box" class="message-box"></div>


    <!-- Firebase/Firestore Script -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { 
            getAuth, 
            signInAnonymously, 
            signInWithCustomToken, 
            onAuthStateChanged 
        } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { 
            getFirestore, 
            doc, 
            setDoc, 
            getDoc, 
            onSnapshot, 
            updateDoc,
            increment
        } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";
        import { setLogLevel } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";
        
        // Setting Firebase log level to Debug for visibility in the console
        setLogLevel('Debug');

        // --- Global Variables (Mandatory for Canvas Environment) ---
        const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-doctor-portal-app-id';
        const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : null;
        const initialAuthToken = typeof __initial_auth_token !== 'undefined' ? __initial_auth_token : null;

        let db = null;
        let auth = null;
        let currentUserId = null;
        let isAuthReady = false;

        // --- Utility Functions ---

        /** Shows a temporary custom message box instead of using alert() */
        function showMessage(message) {
            const box = document.getElementById('message-box');
            box.textContent = message;
            box.classList.add('show');
            setTimeout(() => {
                box.classList.remove('show');
            }, 3000);
        }

        /** Handles setting up Firebase authentication and stores user ID */
        async function setupAuth() {
            try {
                if (!firebaseConfig) {
                    showMessage("Error: Firebase config is missing.");
                    console.error("Firebase config not found.");
                    return;
                }

                const app = initializeApp(firebaseConfig);
                auth = getAuth(app);
                db = getFirestore(app);

                // Sign in using the custom token or anonymously if the token is not available
                if (initialAuthToken) {
                    await signInWithCustomToken(auth, initialAuthToken);
                } else {
                    await signInAnonymously(auth);
                }

                onAuthStateChanged(auth, (user) => {
                    if (user) {
                        currentUserId = user.uid;
                        document.getElementById('user-id-display').textContent = currentUserId;
                        isAuthReady = true;
                        console.log("Authentication successful. User ID:", currentUserId);
                        // Start real-time listener only after successful authentication
                        setupRealtimeListener(); 
                    } else {
                        currentUserId = 'anonymous-' + crypto.randomUUID().substring(0, 8); // Fallback or anonymous ID
                        document.getElementById('user-id-display').textContent = "Not logged in (Anon ID: " + currentUserId + ")";
                        isAuthReady = true;
                        console.warn("User is signed out or anonymous.");
                        // Even if anonymous, we still want to try loading the public data
                        setupRealtimeListener();
                    }
                });

            } catch (error) {
                console.error("Firebase Initialization or Authentication Error:", error);
                showMessage("Database initialization failed. Check console for details.");
                document.getElementById('user-id-display').textContent = "ERROR";
            }
        }

        /** Defines the path to the public, shared document for the live count */
        function getCountDocRef() {
            // Using a PUBLIC path so all users share the same live count data
            const path = `/artifacts/${appId}/public/data/global_dashboard_stats/live_queue_count`;
            return doc(db, path);
        }

        /** Sets up the real-time listener for the patient count */
        function setupRealtimeListener() {
            if (!db || !isAuthReady) {
                console.warn("Database or Auth not ready. Skipping onSnapshot setup.");
                return;
            }

            const countDocRef = getCountDocRef();
            const liveCountElement = document.getElementById('live-count');

            // Listen for real-time changes
            onSnapshot(countDocRef, (docSnapshot) => {
                if (docSnapshot.exists()) {
                    const data = docSnapshot.data();
                    const count = data.count || 0;
                    liveCountElement.textContent = count;
                    console.log("Real-time update received. Count:", count);
                } else {
                    // Document doesn't exist, initialize it to 0
                    console.log("Count document not found. Initializing...");
                    setDoc(countDocRef, { count: 0, last_updated_by: currentUserId });
                    liveCountElement.textContent = 0;
                }
            }, (error) => {
                console.error("Firestore onSnapshot error:", error);
                showMessage("Failed to load live data.");
                liveCountElement.textContent = 'ERR';
            });
        }

        /** Increments the patient count in Firestore */
        async function updatePatientCount() {
            if (!db || !currentUserId) {
                showMessage("Please wait for authentication to complete.");
                return;
            }

            const button = document.getElementById('update-count-btn');
            const spinner = document.getElementById('loading-spinner');

            button.disabled = true;
            spinner.classList.add('spinner-active');

            try {
                const countDocRef = getCountDocRef();
                
                // Use updateDoc with increment for atomic counting
                await updateDoc(countDocRef, {
                    count: increment(1),
                    last_updated_by: currentUserId,
                    timestamp: new Date().toISOString()
                });
                
                // The UI will update automatically via the onSnapshot listener, 
                // so no need to update the DOM here.
                showMessage("Patient added to queue successfully!");

            } catch (error) {
                // If the document doesn't exist yet, it will throw an error. 
                // We'll check the error code and try to create it first.
                if (error.code === 'not-found') {
                    console.warn("Document not found during update. Attempting initial set.");
                    try {
                         await setDoc(countDocRef, {
                            count: 1,
                            last_updated_by: currentUserId,
                            timestamp: new Date().toISOString()
                        });
                        showMessage("Queue initialized and first patient added!");
                    } catch(setError) {
                        console.error("Initial setDoc failed:", setError);
                        showMessage("Failed to initialize queue data.");
                    }
                } else {
                    console.error("Error updating patient count:", error);
                    showMessage("Failed to update queue count: " + error.message);
                }

            } finally {
                button.disabled = false;
                spinner.classList.remove('spinner-active');
            }
        }

        // --- Event Listeners and Initialization ---
        
        document.addEventListener('DOMContentLoaded', () => {
            // Setup click listener for the update button
            document.getElementById('update-count-btn').addEventListener('click', updatePatientCount);
            
            // Start the Firebase setup process
            setupAuth();
        });

    </script>
</body>
</html>
