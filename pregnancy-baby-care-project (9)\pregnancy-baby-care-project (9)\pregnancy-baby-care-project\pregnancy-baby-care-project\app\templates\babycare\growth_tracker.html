<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baby Growth Tracker - Baby Care System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary: #f472b6; /* Main Pink */
            --primary-dark: #ec4899;
            --secondary: #c084fc; /* Soft Purple */
            --secondary-dark: #a855f7;
            --accent: #fde047; /* <PERSON> Yellow */
            --info: #7dd3fc; /* Soft Blue */
            --light: #fdf2f8; /* Very light pink tint */
            --dark: #581c87; /* Dark Purple */
            --gray: #64748b;
            --light-gray: #e2e8f0;
            --success: #34d399;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --shadow: 0 4px 20px rgba(0,0,0,0.08);
            --shadow-hover: 0 8px 30px rgba(0,0,0,0.12);
            --border-radius: 20px;
        }
        
        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: var(--light);
            min-height: 100vh;
            margin: 0;
            overflow-x: hidden;
            padding-top: 80px;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.07);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 0.5rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 70px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark);
            text-decoration: none;
        }

        .logo-icon {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1rem;
            align-items: center;
        }

        .nav-link {
            text-decoration: none;
            color: var(--dark);
            font-weight: 500;
            transition: var(--transition);
            position: relative;
            padding: 0.8rem 1.2rem;
            border-radius: 25px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
        }

        .nav-link:hover {
             color: var(--primary);
        }
        
        .nav-link.active {
            color: white;
            background: var(--primary);
            box-shadow: 0 4px 15px rgba(244, 114, 182, 0.4);
        }
        
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--dark);
            cursor: pointer;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .page-header {
            text-align: center;
            padding: 3rem 0;
        }

        .page-header h1 {
            font-size: 2.8rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }

        .page-header p {
            font-size: 1.2rem;
            color: var(--gray);
            max-width: 600px;
            margin: 0 auto;
        }

        .tracker-layout {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
            padding-bottom: 4rem;
        }
        
        .card {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        
        .card-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .card-title i {
            color: var(--primary);
        }

        .form-group {
            margin-bottom: 1.25rem;
        }
        
        .form-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--gray);
        }
        
        .form-group input {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-group input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(244, 114, 182, 0.2);
        }
        
        .btn-submit {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-submit:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-hover);
        }
        
        #growthChart {
            max-height: 400px;
        }
        
        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .history-table th, .history-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .history-table th {
            font-weight: 600;
            color: var(--gray);
        }

        .history-table tbody tr:last-child td {
            border-bottom: none;
        }
        
        .history-table tbody tr:hover {
            background-color: var(--light);
        }

        .btn-submit:disabled {
            background: var(--light-gray);
            color: var(--gray);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-submit:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        #baby-select {
            transition: var(--transition);
        }

        #baby-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(244, 114, 182, 0.2);
        }

        .loading-spinner {
            display: inline-block;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 992px) {
            .tracker-layout {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            body {
                padding-top: 70px;
            }
            .nav-menu {
                display: none;
                position: absolute;
                top: 70px;
                left: 0;
                width: 100%;
                background: white;
                flex-direction: column;
                padding: 1rem;
                box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            }
            .nav-menu.active {
                display: flex;
            }
            .mobile-menu-btn {
                display: block;
            }
            .page-header h1 {
                font-size: 2.2rem;
            }
            .nav-container, .container {
                padding: 0 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="/" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <span>Growth Tracker</span>
            </a>
            <nav>
                <ul class="nav-menu" id="navMenu">
                    <li><a href="/" class="nav-link"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="/pregnancy" class="nav-link"><i class="fas fa-heart"></i> Pregnancy</a></li>
                    <li><a href="/babycare" class="nav-link active"><i class="fas fa-baby"></i> Baby Care</a></li>
                </ul>
            </nav>
            <button class="mobile-menu-btn" id="mobileMenuBtn" aria-label="Toggle navigation menu">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="page-header">
            <div class="container">
                <h1>Track Your Baby's Growth</h1>
                <p>Monitor your little one's development by recording their weight, height, and head circumference over time.</p>
            </div>
        </div>

        <div class="container">
            <!-- Baby Selection -->
            <div class="card" id="baby-selection" style="margin-bottom: 2rem;">
                <h2 class="card-title"><i class="fas fa-baby"></i> Select Baby</h2>
                <div class="form-group">
                    <select id="baby-select" style="width: 100%; padding: 0.8rem 1rem; border: 2px solid var(--light-gray); border-radius: 10px; font-size: 1rem;">
                        <option value="">Loading babies...</option>
                    </select>
                </div>
            </div>

            <div class="tracker-layout">
                <div class="tracker-sidebar">
                    <div class="card">
                        <h2 class="card-title"><i class="fas fa-plus-circle"></i> Add New Record</h2>
                        <form id="growth-form">
                            <div class="form-group">
                                <label for="date">Date</label>
                                <input type="date" id="date" name="date" required>
                            </div>
                            <div class="form-group">
                                <label for="weight">Weight (kg)</label>
                                <input type="number" id="weight" name="weight" step="0.01" placeholder="e.g., 5.5">
                            </div>
                            <div class="form-group">
                                <label for="height">Height (cm)</label>
                                <input type="number" id="height" name="height" step="0.1" placeholder="e.g., 58.5">
                            </div>
                            <div class="form-group">
                                <label for="head">Head Circumference (cm)</label>
                                <input type="number" id="head" name="head" step="0.1" placeholder="e.g., 38.2">
                            </div>
                            <div class="form-group">
                                <label for="notes">Notes (optional)</label>
                                <textarea id="notes" name="notes" rows="3" style="width: 100%; padding: 0.8rem 1rem; border: 2px solid var(--light-gray); border-radius: 10px; font-size: 1rem; resize: vertical;" placeholder="Any additional notes..."></textarea>
                            </div>
                            <button type="submit" class="btn-submit" disabled id="submit-btn">
                                <i class="fas fa-save"></i> Save Record
                            </button>
                        </form>
                    </div>
                </div>
                <div class="tracker-main">
                     <div class="card">
                         <h2 class="card-title"><i class="fas fa-chart-bar"></i> Growth Chart</h2>
                         <canvas id="growthChart"></canvas>
                     </div>
                </div>
            </div>
            
            <div class="card">
                <h2 class="card-title"><i class="fas fa-history"></i> Record History</h2>
                <div style="overflow-x: auto;">
                    <table class="history-table" id="history-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Weight</th>
                                <th>Height</th>
                                <th>Head Circumference</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be populated by JavaScript -->
                        </tbody>
                    </table>
                    <div id="empty-state" style="display: none; text-align: center; padding: 3rem; color: var(--gray);">
                        <i class="fas fa-chart-line" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <h3>No Growth Records Yet</h3>
                        <p>Start tracking your baby's growth by adding their first measurement above.</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const growthForm = document.getElementById('growth-form');
            const historyTableBody = document.querySelector('#history-table tbody');
            const ctx = document.getElementById('growthChart').getContext('2d');
            const babySelect = document.getElementById('baby-select');
            const submitBtn = document.getElementById('submit-btn');

            // Set default date to today
            document.getElementById('date').valueAsDate = new Date();

            let growthData = [];
            let babies = [];
            let selectedBabyId = null;

            // Load babies on page load
            loadBabies();

            // API Functions
            async function loadBabies() {
                try {
                    const response = await fetch('/babycare/api/babies');
                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            babies = result.babies;
                            populateBabySelect();
                        } else {
                            showNotification('Failed to load babies', 'error');
                        }
                    } else if (response.status === 401) {
                        window.location.href = '/auth/login';
                    } else {
                        showNotification('Failed to load babies', 'error');
                    }
                } catch (error) {
                    console.error('Error loading babies:', error);
                    showNotification('Error loading babies', 'error');
                }
            }

            function populateBabySelect() {
                babySelect.innerHTML = '';
                if (babies.length === 0) {
                    babySelect.innerHTML = '<option value="">No babies registered. Please register a baby first.</option>';
                    submitBtn.disabled = true;
                } else {
                    babySelect.innerHTML = '<option value="">Select a baby...</option>';
                    babies.forEach(baby => {
                        const option = document.createElement('option');
                        option.value = baby.id;
                        option.textContent = `${baby.name} (Born: ${new Date(baby.birth_date).toLocaleDateString()})`;
                        babySelect.appendChild(option);
                    });
                }
            }

            async function loadGrowthData(babyId) {
                try {
                    const response = await fetch(`/babycare/api/babies/${babyId}/growth`);
                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            growthData = result.growth_records.map(record => ({
                                date: record.record_date,
                                weight: record.weight,
                                height: record.height,
                                head: record.head_circumference,
                                notes: record.notes,
                                id: record.id
                            }));
                            renderData();
                        }
                    } else {
                        showNotification('Failed to load growth data', 'error');
                    }
                } catch (error) {
                    console.error('Error loading growth data:', error);
                    showNotification('Error loading growth data', 'error');
                }
            }

            async function saveGrowthRecord(recordData) {
                try {
                    const response = await fetch(`/babycare/api/babies/${selectedBabyId}/growth`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            record_date: recordData.date,
                            weight: recordData.weight || null,
                            height: recordData.height || null,
                            head_circumference: recordData.head || null,
                            notes: recordData.notes || ''
                        })
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            showNotification('Growth record saved successfully!', 'success');
                            loadGrowthData(selectedBabyId); // Reload data
                            return true;
                        } else {
                            showNotification(result.error || 'Failed to save record', 'error');
                            return false;
                        }
                    } else {
                        showNotification('Failed to save record', 'error');
                        return false;
                    }
                } catch (error) {
                    console.error('Error saving growth record:', error);
                    showNotification('Error saving record', 'error');
                    return false;
                }
            }

            // Baby selection handler
            babySelect.addEventListener('change', function() {
                selectedBabyId = this.value;
                if (selectedBabyId) {
                    submitBtn.disabled = false;
                    loadGrowthData(selectedBabyId);
                } else {
                    submitBtn.disabled = true;
                    growthData = [];
                    renderData();
                }
            });

            const growthChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: 'Weight (kg)',
                            data: [],
                            borderColor: 'var(--primary)',
                            backgroundColor: 'rgba(244, 114, 182, 0.1)',
                            fill: true,
                            tension: 0.4,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Height (cm)',
                            data: [],
                            borderColor: 'var(--secondary)',
                            backgroundColor: 'rgba(192, 132, 252, 0.1)',
                            fill: true,
                            tension: 0.4,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: { display: true, text: 'Date' }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: 'Weight (kg)' },
                            beginAtZero: true
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: 'Height (cm)' },
                            grid: {
                                drawOnChartArea: false, 
                            },
                             beginAtZero: true
                        }
                    },
                    plugins: {
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    }
                }
            });

            function renderData() {
                // Sort data by date
                growthData.sort((a, b) => new Date(a.date) - new Date(b.date));

                // Clear existing data
                historyTableBody.innerHTML = '';
                growthChart.data.labels = [];
                growthChart.data.datasets[0].data = [];
                growthChart.data.datasets[1].data = [];

                const emptyState = document.getElementById('empty-state');
                const historyTable = document.getElementById('history-table');

                if (growthData.length === 0) {
                    // Show empty state
                    historyTable.style.display = 'none';
                    emptyState.style.display = 'block';
                } else {
                    // Show table and populate data
                    historyTable.style.display = 'table';
                    emptyState.style.display = 'none';

                    // Populate table and chart
                    growthData.forEach(record => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${new Date(record.date).toLocaleDateString('en-IN', {day: 'numeric', month: 'short', year: 'numeric'})}</td>
                            <td>${record.weight ? record.weight + ' kg' : 'N/A'}</td>
                            <td>${record.height ? record.height + ' cm' : 'N/A'}</td>
                            <td>${record.head ? record.head + ' cm' : 'N/A'}</td>
                            <td>${record.notes ? `<span title="${record.notes}" style="cursor: help;"><i class="fas fa-sticky-note" style="color: var(--primary);"></i></span>` : 'N/A'}</td>
                        `;
                        historyTableBody.appendChild(row);

                        const dateLabel = new Date(record.date).toLocaleDateString('en-IN', {day: 'numeric', month: 'short'});
                        growthChart.data.labels.push(dateLabel);
                        growthChart.data.datasets[0].data.push(record.weight);
                        growthChart.data.datasets[1].data.push(record.height);
                    });
                }

                growthChart.update();
            }

            growthForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                if (!selectedBabyId) {
                    showNotification('Please select a baby first', 'error');
                    return;
                }

                const formData = new FormData(e.target);
                const newRecord = {
                    date: formData.get('date'),
                    weight: formData.get('weight') ? parseFloat(formData.get('weight')) : null,
                    height: formData.get('height') ? parseFloat(formData.get('height')) : null,
                    head: formData.get('head') ? parseFloat(formData.get('head')) : null,
                    notes: formData.get('notes') || ''
                };

                // Validate that at least one measurement is provided
                if (!newRecord.weight && !newRecord.height && !newRecord.head) {
                    showNotification('Please provide at least one measurement', 'error');
                    return;
                }

                // Disable submit button during save
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

                const success = await saveGrowthRecord(newRecord);

                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> Save Record';

                if (success) {
                    growthForm.reset();
                    document.getElementById('date').valueAsDate = new Date();
                }
            });
            
            function showNotification(message, type) {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 90px;
                    right: 20px;
                    padding: 15px 25px;
                    border-radius: 10px;
                    color: white;
                    font-weight: 500;
                    z-index: 9999;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                    background: ${type === 'success' ? 'var(--success)' : 'var(--primary)'};
                    opacity: 0;
                    transform: translateX(20px);
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                `;
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => { 
                    notification.style.opacity = '1';
                    notification.style.transform = 'translateX(0)';
                }, 10);
                
                setTimeout(() => {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateX(20px)';
                    setTimeout(() => { document.body.removeChild(notification); }, 400);
                }, 3000);
            }

            // Initial render
            renderData();

            // Mobile menu toggle
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const navMenu = document.getElementById('navMenu');

            if (mobileMenuBtn && navMenu) {
                mobileMenuBtn.addEventListener('click', function() {
                    navMenu.classList.toggle('active');
                });
            }
        });
    </script>
</body>
</html>
