
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Smart Pregnancy Weight Tracker</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
      background: linear-gradient(135deg, #f9f0ff 0%, #f0f9ff 100%);
      color: #333;
      min-height: 100vh;
      padding-top: 80px;
    }

    /* Header Styles */
    .header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        padding: 0.5rem 0;
    }

    .nav-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 70px;
    }

    .logo {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 1.5rem;
        font-weight: 700;
        color: #7e3af2;
        text-decoration: none;
    }

    .logo-icon {
        background: linear-gradient(135deg, #a78bfa, #818cf8);
        width: 45px;
        height: 45px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.8rem;
    }

    .nav-menu {
        display: flex;
        list-style: none;
        gap: 1.5rem;
        align-items: center;
    }

    .nav-link {
        color: #333;
        text-decoration: none;
        font-weight: 500;
        padding: 0.8rem 1.2rem;
        border-radius: 25px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }

    .nav-link:hover,
    .nav-link.active {
        color: white;
        background: linear-gradient(135deg, #a78bfa, #818cf8);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(167, 139, 250, 0.3);
    }

    .main-content {
        padding: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 80px);
    }

    .container {
      max-width: 1200px;
      width: 100%;
      background: white;
      border-radius: 20px;
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    header {
      background: linear-gradient(to right, #a78bfa, #818cf8);
      color: white;
      padding: 30px;
      text-align: center;
      position: relative;
    }

    h1 {
      font-size: 2.8rem;
      margin-bottom: 10px;
      font-weight: 700;
      letter-spacing: -0.5px;
    }

    .subtitle {
      font-size: 1.2rem;
      opacity: 0.9;
      max-width: 700px;
      margin: 0 auto;
    }

    .dashboard {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 25px;
      padding: 30px;
    }

    @media (max-width: 768px) {
      .dashboard {
        grid-template-columns: 1fr;
      }
    }

    .card {
      background: white;
      border-radius: 15px;
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
      padding: 25px;
      transition: transform 0.3s ease;
    }

    .card:hover {
      transform: translateY(-5px);
    }

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      color: #7e3af2;
    }

    .card-header i {
      font-size: 1.8rem;
      margin-right: 15px;
      background: #f0f4ff;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .card-header h2 {
      font-size: 1.6rem;
    }

    .input-group {
      margin-bottom: 18px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #4b5563;
    }

    input {
      width: 100%;
      padding: 14px;
      border: 2px solid #e5e7eb;
      border-radius: 12px;
      font-size: 1rem;
      transition: border-color 0.3s;
    }

    input:focus {
      border-color: #818cf8;
      outline: none;
      box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.2);
    }

    button {
      background: linear-gradient(to right, #8b5cf6, #6366f1);
      color: white;
      border: none;
      padding: 14px 25px;
      border-radius: 12px;
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      width: 100%;
      transition: all 0.3s ease;
      margin-top: 10px;
    }

    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 7px 15px rgba(129, 140, 248, 0.4);
    }

    .recommendation {
      background: #f0f9ff;
      border-left: 4px solid #60a5fa;
      padding: 20px;
      border-radius: 8px;
      margin-top: 20px;
      display: flex;
    }

    .recommendation i {
      font-size: 2rem;
      color: #3b82f6;
      margin-right: 15px;
    }

    .recommendation p {
      font-size: 1.1rem;
      line-height: 1.6;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    th {
      background: #818cf8;
      color: white;
      text-align: left;
      padding: 16px;
      font-weight: 600;
    }

    td {
      padding: 14px 16px;
      border-bottom: 1px solid #e5e7eb;
    }

    tr:nth-child(even) {
      background-color: #f9fafb;
    }

    tr:hover {
      background-color: #f0f4ff;
    }

    .status {
      display: inline-block;
      padding: 5px 12px;
      border-radius: 20px;
      font-size: 0.85rem;
      font-weight: 600;
    }

    .status.good {
      background: #dcfce7;
      color: #166534;
    }

    .status.low {
      background: #fef3c7;
      color: #92400e;
    }

    .status.high {
      background: #fee2e2;
      color: #b91c1c;
    }

    .chart-container {
      height: 300px;
      margin-top: 25px;
      position: relative;
    }

    .info-panel {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 20px;
      padding: 30px;
      background: #f8fafc;
    }

    .info-box {
      text-align: center;
      padding: 25px;
      border-radius: 15px;
      background: white;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .info-box h3 {
      color: #7e3af2;
      margin-bottom: 15px;
      font-size: 1.4rem;
    }

    .info-box ul {
      text-align: left;
      padding-left: 20px;
    }

    .info-box li {
      margin-bottom: 10px;
      line-height: 1.5;
    }

    footer {
      text-align: center;
      padding: 25px;
      background: #f8fafc;
      color: #64748b;
      border-top: 1px solid #e2e8f0;
    }

    .highlight {
      background: linear-gradient(120deg, #f0abfc 0%, #a5b4fc 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: 700;
    }
  </style>
</head>
<body>
  <!-- Navigation Header -->
  <header class="header">
      <div class="nav-container">
          <a href="/pregnancy" class="logo">
              <div class="logo-icon">
                  <i class="fas fa-weight"></i>
              </div>
              <span>Weight Tracker</span>
          </a>
          <nav class="nav-menu">
              <a href="/" class="nav-link">
                  <i class="fas fa-home"></i> Home
              </a>
              <a href="/pregnancy" class="nav-link">
                  <i class="fas fa-heart"></i> Pregnancy Care
              </a>
              <a href="/pregnancy/weight-tracker" class="nav-link active">
                  <i class="fas fa-weight"></i> Weight Tracker
              </a>
              <a href="/pregnancy/nutrition" class="nav-link">
                  <i class="fas fa-utensils"></i> Nutrition
              </a>
              <a href="/pregnancy/exercise" class="nav-link">
                  <i class="fas fa-dumbbell"></i> Exercise
              </a>
              <a href="/pregnancy/appointments" class="nav-link">
                  <i class="fas fa-calendar-alt"></i> Appointments
              </a>
              <a href="/pregnancy/reports" class="nav-link">
                  <i class="fas fa-chart-line"></i> Reports
              </a>
              <a href="/babycare" class="nav-link">
                  <i class="fas fa-baby"></i> Baby Care
              </a>
              <a href="/auth/logout" class="nav-link">
                  <i class="fas fa-sign-out-alt"></i> Logout
              </a>
          </nav>
      </div>
  </header>

  <div class="main-content">
    <div class="container">
      <header>
        <h1>Pregnancy Weight Tracker</h1>
        <p class="subtitle">Track your weight gain and get personalized recommendations throughout your pregnancy journey</p>
      </header>

    <div class="dashboard">
      <div class="card">
        <div class="card-header">
          <i class="fas fa-user-circle"></i>
          <h2>Your Information</h2>
        </div>

        <div class="input-group">
          <label for="preWeight">Pre-pregnancy Weight (kg)</label>
          <input type="number" id="preWeight" placeholder="Enter your weight before pregnancy" step="0.1">
        </div>

        <div class="input-group">
          <label for="height">Height (cm)</label>
          <input type="number" id="height" placeholder="Enter your height" step="0.1">
        </div>

        <div class="input-group">
          <label for="currentWeek">Current Pregnancy Week</label>
          <input type="number" id="currentWeek" placeholder="Enter current week (4-40)" min="4" max="40">
        </div>

        <div class="input-group">
          <label for="currentWeight">Current Weight (kg)</label>
          <input type="number" id="currentWeight" placeholder="Enter your current weight" step="0.1">
        </div>

        <button id="calculateBtn">Calculate Recommendations</button>

        <div class="recommendation">
          <i class="fas fa-lightbulb"></i>
          <p>Enter your information above to get personalized weight gain recommendations based on medical guidelines.</p>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <i class="fas fa-chart-line"></i>
          <h2>Weight Analysis</h2>
        </div>

        <div class="chart-container">
          <canvas id="weightChart"></canvas>
        </div>

        <table>
          <thead>
            <tr>
              <th>Week</th>
              <th>Status</th>
              <th>Recommendation</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>12</td>
              <td><span class="status good">On Track</span></td>
              <td>Gain 0.5-2 kg total by week 14</td>
            </tr>
            <tr>
              <td>16</td>
              <td><span class="status low">Below Target</span></td>
              <td>Aim for 0.25-0.35 kg/week gain</td>
            </tr>
            <tr>
              <td>20</td>
              <td><span class="status good">On Track</span></td>
              <td>Continue with current nutrition plan</td>
            </tr>
            <tr>
              <td>24</td>
              <td><span class="status high">Above Target</span></td>
              <td>Focus on nutrient-dense foods</td>
            </tr>
            <tr>
              <td>28</td>
              <td><span class="status good">On Track</span></td>
              <td>Maintain balanced diet with proteins</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="info-panel">
      <div class="info-box">
        <h3>Weight Gain Guidelines</h3>
        <ul>
          <li><span class="highlight">Underweight</span> (BMI &lt;18.5): 12.7-18.1 kg</li>
          <li><span class="highlight">Normal weight</span> (BMI 18.5-24.9): 11.3-15.9 kg</li>
          <li><span class="highlight">Overweight</span> (BMI 25-29.9): 6.8-11.3 kg</li>
          <li><span class="highlight">Obese</span> (BMI ≥30): 5.0-9.1 kg</li>
        </ul>
      </div>

      <div class="info-box">
        <h3>Healthy Weight Gain Tips</h3>
        <ul>
          <li>Eat frequent, small meals throughout the day</li>
          <li>Focus on nutrient-dense foods</li>
          <li>Include lean proteins in every meal</li>
          <li>Stay hydrated with water and milk</li>
          <li>Listen to your hunger cues</li>
        </ul>
      </div>

      <div class="info-box">
        <h3>When to Seek Help</h3>
        <ul>
          <li>Gaining less than 1kg/month after 1st trimester</li>
          <li>Gaining more than 3kg/month consistently</li>
          <li>Sudden weight changes (+2kg/week)</li>
          <li>Concerns about nutrition or appetite</li>
          <li>Any rapid swelling or bloating</li>
        </ul>
      </div>
    </div>

    <footer>
      </footer>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const calculateBtn = document.getElementById('calculateBtn');

      // Initialize chart
      const ctx = document.getElementById('weightChart').getContext('2d');
      const chart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: ['8', '12', '16', '20', '24', '28', '32', '36', '40'],
          datasets: [{
            label: 'Your Weight',
            data: [58, 59, 60.5, 62.5, 64.5, 67, 69.5, 72, 74],
            borderColor: '#8b5cf6',
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            borderWidth: 3,
            tension: 0.2,
            fill: true
          }, {
            label: 'Recommended Range',
            data: [57.5, 58.5, 60, 61.5, 63, 64.5, 66.5, 68, 69.5],
            borderColor: '#10b981',
            borderWidth: 2,
            borderDash: [5, 5],
            pointRadius: 0,
            fill: false
          }, {
            label: '',
            data: [58.5, 60, 61.5, 63.5, 65.5, 67.5, 69.5, 71.5, 73.5],
            borderColor: '#10b981',
            borderWidth: 2,
            borderDash: [5, 5],
            pointRadius: 0,
            fill: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          scales: {
            y: {
              beginAtZero: false,
              title: {
                display: true,
                text: 'Weight (kg)'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Pregnancy Week'
              }
            }
          }
        }
      });

      // Button click handler
      calculateBtn.addEventListener('click', function() {
        const preWeight = parseFloat(document.getElementById('preWeight').value);
        const height = parseFloat(document.getElementById('height').value);
        const currentWeek = parseInt(document.getElementById('currentWeek').value);
        const currentWeight = parseFloat(document.getElementById('currentWeight').value);

        if (!preWeight || !height || !currentWeek || !currentWeight) {
          alert('Please fill in all fields');
          return;
        }

        // Calculate BMI
        const heightMeters = height / 100;
        const bmi = preWeight / (heightMeters * heightMeters);

        // Determine BMI category
        let category, totalGainRange, weeklyGain;
        if (bmi < 18.5) {
          category = 'underweight';
          totalGainRange = '12.7-18.1 kg';
          weeklyGain = '0.44-0.58 kg/week';
        } else if (bmi < 25) {
          category = 'normal weight';
          totalGainRange = '11.3-15.9 kg';
          weeklyGain = '0.35-0.50 kg/week';
        } else if (bmi < 30) {
          category = 'overweight';
          totalGainRange = '6.8-11.3 kg';
          weeklyGain = '0.23-0.33 kg/week';
        } else {
          category = 'obese';
          totalGainRange = '5.0-9.1 kg';
          weeklyGain = '0.17-0.27 kg/week';
        }

        // Calculate current gain
        const weightGain = currentWeight - preWeight;
        const weeksSinceFirstTrimester = Math.max(0, currentWeek - 13);
        const expectedMin = 0.5 + (weeksSinceFirstTrimester * 0.3);
        const expectedMax = 2.0 + (weeksSinceFirstTrimester * 0.45);

        // Generate recommendation
        let recommendation = '';
        let status = 'good';

        if (currentWeek < 14) {
          if (weightGain < 0.5) {
            recommendation = `Based on your ${category} BMI, consider increasing your weight gain. Aim for 0.5-2 kg total gain by week 14.`;
            status = 'low';
          } else if (weightGain > 2.0) {
            recommendation = `Based on your ${category} BMI, your weight gain is above recommendations. Focus on nutrient-dense foods.`;
            status = 'high';
          } else {
            recommendation = `Great! Your weight gain is within the recommended range for the first trimester.`;
          }
        } else {
          if (weightGain < expectedMin) {
            recommendation = `As someone with ${category} BMI, you should increase your weight gain. Aim for ${weeklyGain} during 2nd/3rd trimesters.`;
            status = 'low';
          } else if (weightGain > expectedMax) {
            recommendation = `As someone with ${category} BMI, your weight gain is above recommendations. Focus on lean proteins and vegetables.`;
            status = 'high';
          } else {
            recommendation = `Excellent! Your weight gain is on track for your ${category} BMI category.`;
          }
        }

        // Add BMI info
        recommendation += ` Your BMI before pregnancy was ${bmi.toFixed(1)} (${category}). Total recommended gain: ${totalGainRange}.`;

        // Update UI
        const recommendationEl = document.querySelector('.recommendation p');
        recommendationEl.innerHTML = recommendation;

        // Add icon based on status
        const icon = document.querySelector('.recommendation i');
        if (status === 'low') {
          icon.className = 'fas fa-arrow-up';
          icon.style.color = '#f59e0b';
          document.querySelector('.recommendation').style.borderLeftColor = '#f59e0b';
          document.querySelector('.recommendation').style.background = '#fffbeb';
        } else if (status === 'high') {
          icon.className = 'fas fa-arrow-down';
          icon.style.color = '#ef4444';
          document.querySelector('.recommendation').style.borderLeftColor = '#ef4444';
          document.querySelector('.recommendation').style.background = '#fef2f2';
        } else {
          icon.className = 'fas fa-check-circle';
          icon.style.color = '#10b981';
          document.querySelector('.recommendation').style.borderLeftColor = '#10b981';
          document.querySelector('.recommendation').style.background = '#ecfdf5';
        }
      });
    });
  </script>
</body>
</html>