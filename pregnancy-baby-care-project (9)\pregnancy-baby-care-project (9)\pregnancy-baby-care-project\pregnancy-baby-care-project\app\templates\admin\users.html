<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Admin Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #667eea;
            --primary-dark: #5a67d8;
            --secondary: #764ba2;
            --accent: #f093fb;
            --success: #48bb78;
            --warning: #ed8936;
            --danger: #f56565;
            --light: #f7fafc;
            --dark: #2d3748;
            --gray: #718096;
            --light-gray: #e2e8f0;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --shadow: 0 4px 20px rgba(0,0,0,0.1);
            --shadow-hover: 0 8px 30px rgba(0,0,0,0.15);
            --border-radius: 20px;
            --gradient-primary: linear-gradient(135deg, #667eea, #764ba2);
            --gradient-secondary: linear-gradient(135deg, #f093fb, #f5576c);
            --gradient-accent: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        /* Navigation Header */
        .admin-nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
            margin-bottom: 2rem;
        }

        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: 0.5rem 1rem;
            border-radius: 10px;
        }

        .nav-links a:hover {
            background: var(--light-gray);
            color: var(--primary);
        }

        .nav-links a.active {
            background: var(--primary);
            color: white;
        }

        /* Main Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem 2rem;
        }

        /* Page Header */
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            text-align: center;
        }

        .page-title {
            font-size: 3rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .page-subtitle {
            font-size: 1.2rem;
            color: var(--gray);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Controls */
        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .search-box {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex: 1;
            max-width: 400px;
        }

        .search-input {
            flex: 1;
            padding: 0.75rem 1rem;
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            font-size: 1rem;
            transition: var(--transition);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .btn-secondary {
            background: var(--light-gray);
            color: var(--dark);
        }

        .btn-secondary:hover {
            background: var(--gray);
            color: white;
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-warning {
            background: var(--warning);
            color: white;
        }

        .btn-danger {
            background: var(--danger);
            color: white;
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        /* Users Table */
        .users-table {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .table-header {
            background: var(--gradient-primary);
            color: white;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .table-content {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--light-gray);
        }

        th {
            background: var(--light);
            font-weight: 600;
            color: var(--dark);
        }

        tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--gradient-secondary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 1rem;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-details h4 {
            margin-bottom: 0.25rem;
            color: var(--dark);
        }

        .user-details p {
            color: var(--gray);
            font-size: 0.9rem;
        }

        .role-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .role-admin {
            background: var(--danger);
            color: white;
        }

        .role-doctor {
            background: var(--warning);
            color: white;
        }

        .role-user {
            background: var(--success);
            color: white;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .actions {
            display: flex;
            gap: 0.5rem;
        }

        /* Loading and Empty States */
        .loading {
            text-align: center;
            padding: 3rem;
            color: var(--gray);
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: var(--gray);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Alerts */
        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 0 1rem 1rem;
            }

            .page-title {
                font-size: 2rem;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .nav-links {
                display: none;
            }

            .nav-content {
                padding: 0 1rem;
            }

            .table-content {
                font-size: 0.9rem;
            }

            th, td {
                padding: 0.75rem 0.5rem;
            }
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 10001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-hover);
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            animation: slideInUp 0.3s ease;
        }

        .modal-header {
            padding: 2rem 2rem 1rem;
            border-bottom: 1px solid var(--light-gray);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            color: var(--primary);
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--gray);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: var(--transition);
        }

        .close-btn:hover {
            background: var(--light-gray);
            color: var(--danger);
        }

        .modal-form {
            padding: 2rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--light-gray);
            border-radius: 10px;
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--light-gray);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInUp {
            from {
                transform: translateY(30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                margin: 1rem;
            }

            .modal-header,
            .modal-form {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Header -->
    <nav class="admin-nav">
        <div class="nav-content">
            <div class="nav-logo">
                <i class="fas fa-crown"></i>
                <span>Admin Dashboard</span>
            </div>
            <div class="nav-links">
                <a href="/admin"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/admin/manage-vaccination"><i class="fas fa-syringe"></i> Vaccinations</a>
                <a href="/admin/users" class="active"><i class="fas fa-users"></i> Users</a>
                <a href="/admin/babies"><i class="fas fa-baby"></i> Babies</a>
                <a href="/admin/reports"><i class="fas fa-chart-bar"></i> Reports</a>
                <a href="/admin/settings"><i class="fas fa-cog"></i> Settings</a>
                <a href="/auth/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-users"></i>
                User Management
            </h1>
            <p class="page-subtitle">
                Manage user accounts, roles, and permissions across the platform
            </p>
        </div>

        <!-- Alert Messages -->
        <div id="alert-container"></div>

        <!-- Controls -->
        <div class="controls">
            <div class="search-box">
                <input type="text" id="search-input" class="search-input" placeholder="Search users by name or email...">
                <button class="btn btn-secondary" onclick="searchUsers()">
                    <i class="fas fa-search"></i>
                    Search
                </button>
            </div>
            <div style="display: flex; gap: 1rem;">
                <select id="role-filter" class="search-input" onchange="filterUsers()">
                    <option value="">All Roles</option>
                    <option value="admin">Admin</option>
                    <option value="doctor">Doctor</option>
                    <option value="user">User</option>
                </select>
                <select id="status-filter" class="search-input" onchange="filterUsers()">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
                <button class="btn btn-primary" onclick="showAddUserModal()">
                    <i class="fas fa-plus"></i>
                    Add User
                </button>
            </div>
        </div>

        <!-- Users Table -->
        <div class="users-table">
            <div class="table-header">
                <i class="fas fa-users"></i>
                <h3>System Users</h3>
            </div>
            <div class="table-content">
                <table>
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <tr>
                            <td colspan="6">
                                <div class="loading">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    Loading users...
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div id="add-user-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-user-plus"></i> Add New User</h2>
                <button class="close-btn" onclick="closeAddUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="add-user-form" class="modal-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="user-full-name">Full Name *</label>
                        <input type="text" id="user-full-name" name="full_name" required>
                    </div>
                    <div class="form-group">
                        <label for="user-email">Email *</label>
                        <input type="email" id="user-email" name="email" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="user-password">Password *</label>
                        <input type="password" id="user-password" name="password" required minlength="6">
                    </div>
                    <div class="form-group">
                        <label for="user-role">Role *</label>
                        <select id="user-role" name="role" required>
                            <option value="">Select Role</option>
                            <option value="user">Regular User</option>
                            <option value="doctor">Doctor</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="user-phone">Phone</label>
                        <input type="tel" id="user-phone" name="phone">
                    </div>
                    <div class="form-group">
                        <label for="user-dob">Date of Birth</label>
                        <input type="date" id="user-dob" name="date_of_birth">
                    </div>
                </div>

                <div class="form-group">
                    <label for="user-address">Address</label>
                    <textarea id="user-address" name="address" rows="2"></textarea>
                </div>

                <div class="form-group">
                    <label for="user-emergency">Emergency Contact</label>
                    <input type="text" id="user-emergency" name="emergency_contact" placeholder="Name - Phone">
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeAddUserModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i>
                        Create User
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div id="edit-user-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-user-edit"></i> Edit User</h2>
                <button class="close-btn" onclick="closeEditUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="edit-user-form" class="modal-form">
                <input type="hidden" id="edit-user-id" name="user_id">

                <div class="form-row">
                    <div class="form-group">
                        <label for="edit-user-full-name">Full Name *</label>
                        <input type="text" id="edit-user-full-name" name="full_name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-user-email">Email *</label>
                        <input type="email" id="edit-user-email" name="email" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="edit-user-password">New Password (leave blank to keep current)</label>
                        <input type="password" id="edit-user-password" name="password" minlength="6">
                    </div>
                    <div class="form-group">
                        <label for="edit-user-role">Role *</label>
                        <select id="edit-user-role" name="role" required>
                            <option value="">Select Role</option>
                            <option value="user">Regular User</option>
                            <option value="doctor">Doctor</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="edit-user-phone">Phone</label>
                        <input type="tel" id="edit-user-phone" name="phone">
                    </div>
                    <div class="form-group">
                        <label for="edit-user-dob">Date of Birth</label>
                        <input type="date" id="edit-user-dob" name="date_of_birth">
                    </div>
                </div>

                <div class="form-group">
                    <label for="edit-user-address">Address</label>
                    <textarea id="edit-user-address" name="address" rows="2"></textarea>
                </div>

                <div class="form-group">
                    <label for="edit-user-emergency">Emergency Contact</label>
                    <input type="text" id="edit-user-emergency" name="emergency_contact" placeholder="Name - Phone">
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeEditUserModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Update User
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Global variables
        let allUsers = [];
        let filteredUsers = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
        });

        // Load users
        async function loadUsers() {
            try {
                const response = await fetch('/admin/api/users');
                const data = await response.json();

                if (data.success) {
                    allUsers = data.users;
                    filteredUsers = [...allUsers];
                    displayUsers();
                } else {
                    showAlert('Failed to load users: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Error loading users:', error);
                showAlert('Error loading users. Please try again.', 'error');
            }
        }

        // Display users
        function displayUsers() {
            const tbody = document.getElementById('users-table-body');

            if (filteredUsers.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6">
                            <div class="empty-state">
                                <i class="fas fa-users"></i>
                                <h4>No users found</h4>
                                <p>No users match your current filters.</p>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            filteredUsers.forEach(user => {
                const row = createUserRow(user);
                tbody.appendChild(row);
            });
        }

        // Create user row
        function createUserRow(user) {
            const tr = document.createElement('tr');

            const createdDate = user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A';
            const lastLogin = user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never';
            const initials = user.full_name.split(' ').map(n => n[0]).join('').toUpperCase();

            tr.innerHTML = `
                <td>
                    <div class="user-info">
                        <div class="user-avatar">${initials}</div>
                        <div class="user-details">
                            <h4>${user.full_name}</h4>
                            <p>${user.email}</p>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="role-badge role-${user.role}">${user.role}</span>
                </td>
                <td>
                    <span class="status-badge status-${user.is_active ? 'active' : 'inactive'}">
                        ${user.is_active ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>${createdDate}</td>
                <td>${lastLogin}</td>
                <td>
                    <div class="actions">
                        <button class="btn btn-secondary btn-small" onclick="editUser(${user.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-${user.is_active ? 'warning' : 'success'} btn-small"
                                onclick="toggleUserStatus(${user.id}, ${!user.is_active})">
                            <i class="fas fa-${user.is_active ? 'ban' : 'check'}"></i>
                        </button>
                        ${user.role !== 'admin' ? `
                            <button class="btn btn-danger btn-small" onclick="deleteUser(${user.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            `;

            return tr;
        }

        // Search users
        function searchUsers() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();

            if (searchTerm === '') {
                filteredUsers = [...allUsers];
            } else {
                filteredUsers = allUsers.filter(user =>
                    user.full_name.toLowerCase().includes(searchTerm) ||
                    user.email.toLowerCase().includes(searchTerm)
                );
            }

            applyFilters();
            displayUsers();
        }

        // Filter users
        function filterUsers() {
            applyFilters();
            displayUsers();
        }

        // Apply filters
        function applyFilters() {
            const roleFilter = document.getElementById('role-filter').value;
            const statusFilter = document.getElementById('status-filter').value;
            const searchTerm = document.getElementById('search-input').value.toLowerCase();

            filteredUsers = allUsers.filter(user => {
                const matchesSearch = searchTerm === '' ||
                    user.full_name.toLowerCase().includes(searchTerm) ||
                    user.email.toLowerCase().includes(searchTerm);

                const matchesRole = roleFilter === '' || user.role === roleFilter;

                const matchesStatus = statusFilter === '' ||
                    (statusFilter === 'active' && user.is_active) ||
                    (statusFilter === 'inactive' && !user.is_active);

                return matchesSearch && matchesRole && matchesStatus;
            });
        }

        // Edit user
        async function editUser(userId) {
            try {
                // Fetch user data
                const response = await fetch(`/admin/api/users/${userId}`);
                const result = await response.json();

                if (result.success) {
                    const user = result.user;

                    // Populate the edit form
                    document.getElementById('edit-user-id').value = user.id;
                    document.getElementById('edit-user-full-name').value = user.full_name || '';
                    document.getElementById('edit-user-email').value = user.email || '';
                    document.getElementById('edit-user-password').value = ''; // Always empty for security
                    document.getElementById('edit-user-role').value = user.role || '';
                    document.getElementById('edit-user-phone').value = user.phone || '';
                    document.getElementById('edit-user-dob').value = user.date_of_birth || '';
                    document.getElementById('edit-user-address').value = user.address || '';
                    document.getElementById('edit-user-emergency').value = user.emergency_contact || '';

                    // Show the modal
                    showEditUserModal();
                } else {
                    showAlert(result.error || 'Failed to load user data', 'error');
                }
            } catch (error) {
                console.error('Error loading user data:', error);
                showAlert('Error loading user data. Please try again.', 'error');
            }
        }

        // Show edit user modal
        function showEditUserModal() {
            const modal = document.getElementById('edit-user-modal');
            modal.classList.add('show');
        }

        // Close edit user modal
        function closeEditUserModal() {
            const modal = document.getElementById('edit-user-modal');
            modal.classList.remove('show');
        }

        // Toggle user status
        async function toggleUserStatus(userId, newStatus) {
            try {
                const response = await fetch(`/admin/api/users/${userId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        is_active: newStatus
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showAlert(`User ${newStatus ? 'activated' : 'deactivated'} successfully!`, 'success');
                    loadUsers();
                } else {
                    showAlert(result.error || 'Failed to update user status.', 'error');
                }
            } catch (error) {
                console.error('Error updating user status:', error);
                showAlert('Error updating user status. Please try again.', 'error');
            }
        }

        // Delete user
        async function deleteUser(userId) {
            if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`/admin/api/users/${userId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('User deleted successfully!', 'success');
                    loadUsers();
                } else {
                    showAlert(result.error || 'Failed to delete user.', 'error');
                }
            } catch (error) {
                console.error('Error deleting user:', error);
                showAlert('Error deleting user. Please try again.', 'error');
            }
        }

        // Show add user modal
        function showAddUserModal() {
            const modal = document.getElementById('add-user-modal');
            modal.classList.add('show');

            // Reset form
            document.getElementById('add-user-form').reset();
        }

        // Close add user modal
        function closeAddUserModal() {
            const modal = document.getElementById('add-user-modal');
            modal.classList.remove('show');
        }

        // Handle add user form submission
        document.addEventListener('DOMContentLoaded', function() {
            const addForm = document.getElementById('add-user-form');
            if (addForm) {
                addForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const formData = new FormData(addForm);
                    const userData = {};

                    // Convert FormData to object
                    for (let [key, value] of formData.entries()) {
                        userData[key] = value;
                    }

                    try {
                        const response = await fetch('/admin/api/users', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(userData)
                        });

                        const result = await response.json();

                        if (result.success) {
                            showAlert('User created successfully!', 'success');
                            closeAddUserModal();
                            loadUsers(); // Reload the users table
                        } else {
                            showAlert(result.error || 'Failed to create user', 'error');
                        }
                    } catch (error) {
                        console.error('Error creating user:', error);
                        showAlert('Error creating user. Please try again.', 'error');
                    }
                });
            }

            // Handle edit user form submission
            const editForm = document.getElementById('edit-user-form');
            if (editForm) {
                editForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const formData = new FormData(editForm);
                    const userData = {};

                    // Convert FormData to object
                    for (let [key, value] of formData.entries()) {
                        if (key !== 'user_id') { // Don't include user_id in the data
                            userData[key] = value;
                        }
                    }

                    // Remove password if it's empty (keep current password)
                    if (!userData.password) {
                        delete userData.password;
                    }

                    const userId = document.getElementById('edit-user-id').value;

                    try {
                        const response = await fetch(`/admin/api/users/${userId}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(userData)
                        });

                        const result = await response.json();

                        if (result.success) {
                            showAlert('User updated successfully!', 'success');
                            closeEditUserModal();
                            loadUsers(); // Reload the users table
                        } else {
                            showAlert(result.error || 'Failed to update user', 'error');
                        }
                    } catch (error) {
                        console.error('Error updating user:', error);
                        showAlert('Error updating user. Please try again.', 'error');
                    }
                });
            }

            // Close modals when clicking outside
            const addModal = document.getElementById('add-user-modal');
            if (addModal) {
                addModal.addEventListener('click', function(e) {
                    if (e.target === addModal) {
                        closeAddUserModal();
                    }
                });
            }

            const editModal = document.getElementById('edit-user-modal');
            if (editModal) {
                editModal.addEventListener('click', function(e) {
                    if (e.target === editModal) {
                        closeEditUserModal();
                    }
                });
            }
        });

        // Show alert
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alert-container');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;

            const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle';
            alert.innerHTML = `
                <i class="fas fa-${icon}"></i>
                ${message}
            `;

            alertContainer.appendChild(alert);

            // Auto remove after 5 seconds
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // Search on Enter key
        document.getElementById('search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchUsers();
            }
        });
    </script>
</body>
</html>
