#!/usr/bin/env python3
"""
Test script for public content API endpoints
"""

import requests
import json

def test_public_content_api():
    """Test the public content API endpoints"""
    
    print("🧪 TESTING PUBLIC CONTENT API ENDPOINTS")
    print("=" * 60)
    
    try:
        # Test 1: Test public nutrition API
        print("1. Testing public nutrition API...")
        nutrition_response = requests.get('http://127.0.0.1:5000/api/nutrition-data')
        
        if nutrition_response.status_code == 200:
            print("   ✅ Public nutrition API working")
            data = nutrition_response.json()
            if data.get('success') and 'data' in data:
                print(f"   📊 Nutrition items available: {len(data['data'])}")
                if data['data']:
                    print(f"   📋 Sample item: {data['data'][0]['title']}")
            else:
                print(f"   ⚠️ API response format: {data}")
        else:
            print(f"   ❌ Public nutrition API failed: {nutrition_response.status_code}")
            print(f"   Response: {nutrition_response.text}")
        
        # Test 2: Test public FAQ API
        print("2. Testing public FAQ API...")
        faq_response = requests.get('http://127.0.0.1:5000/api/faq-data')
        
        if faq_response.status_code == 200:
            print("   ✅ Public FAQ API working")
            data = faq_response.json()
            if data.get('success') and 'data' in data:
                print(f"   📊 FAQ items available: {len(data['data'])}")
                if data['data']:
                    print(f"   📋 Sample FAQ: {data['data'][0]['question'][:50]}...")
            else:
                print(f"   ⚠️ API response format: {data}")
        else:
            print(f"   ❌ Public FAQ API failed: {faq_response.status_code}")
            print(f"   Response: {faq_response.text}")
        
        # Test 3: Test public schemes API
        print("3. Testing public government schemes API...")
        schemes_response = requests.get('http://127.0.0.1:5000/api/schemes-data')
        
        if schemes_response.status_code == 200:
            print("   ✅ Public schemes API working")
            data = schemes_response.json()
            if data.get('success') and 'data' in data:
                print(f"   📊 Government schemes available: {len(data['data'])}")
                if data['data']:
                    print(f"   📋 Sample scheme: {data['data'][0]['name']}")
            else:
                print(f"   ⚠️ API response format: {data}")
        else:
            print(f"   ❌ Public schemes API failed: {schemes_response.status_code}")
            print(f"   Response: {schemes_response.text}")
        
        # Test 4: Test public vaccination API
        print("4. Testing public vaccination API...")
        vaccination_response = requests.get('http://127.0.0.1:5000/api/vaccination-data')
        
        if vaccination_response.status_code == 200:
            print("   ✅ Public vaccination API working")
            data = vaccination_response.json()
            if data.get('success') and 'data' in data:
                print(f"   📊 Vaccination schedules available: {len(data['data'])}")
                if data['data']:
                    print(f"   📋 Sample vaccine: {data['data'][0]['vaccine_name']}")
            else:
                print(f"   ⚠️ API response format: {data}")
        else:
            print(f"   ❌ Public vaccination API failed: {vaccination_response.status_code}")
            print(f"   Response: {vaccination_response.text}")
        
        print("\n🎉 PUBLIC CONTENT API TESTING COMPLETE!")
        print("=" * 60)
        print("\n📋 SUMMARY:")
        print("✅ All public API endpoints are accessible without authentication")
        print("✅ Content added by admins is now visible to all users")
        print("✅ Database-driven content management is working")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_public_content_api()
