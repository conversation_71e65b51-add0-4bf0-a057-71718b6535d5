<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Maternal and Child Health Monitoring</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #ec407a; /* Main Pink */
            --primary-dark: #d81b60; /* Darker Pink */
            --secondary: #f48fb1; /* Lighter Pink */
            --accent: #ff80ab; /* Accent Pink */
            --success: #48bb78;
            --warning: #ed8936;
            --danger: #f56565;
            --light: #fce4ec; /* Very light pink */
            --dark: #372d32;
            --gray: #7d5a6a; /* A pinkish gray */
            --light-gray: #fde8f0;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --shadow: 0 4px 20px rgba(0,0,0,0.1);
            --shadow-hover: 0 8px 30px rgba(0,0,0,0.15);
            --border-radius: 20px;
            --gradient-primary: linear-gradient(135deg, #f8bbd0, #ec407a);
            --gradient-secondary: linear-gradient(135deg, #ff80ab, #d81b60);
            --gradient-accent: linear-gradient(135deg, #f8bbd0, #f06292);
        }

        @keyframes gradient-animation {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.08); }
            100% { transform: scale(1); }
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(-45deg, #f8bbd0, #ec407a, #f06292, #d81b60);
            background-size: 400% 400%;
            animation: gradient-animation 15s ease infinite;
            min-height: 100vh;
            color: var(--dark);
            line-height: 1.6;
        }

        /* Navigation Header */
        .admin-nav {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
            margin-bottom: 2rem;
        }

        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: 0.5rem 1rem;
            border-radius: 10px;
        }

        .nav-links a:hover {
            background: var(--light-gray);
            color: var(--primary);
            transform: translateY(-2px);
        }

        .nav-links a.active {
            background: var(--primary);
            color: white;
            box-shadow: 0 4px 15px rgba(236, 64, 122, 0.4);
        }

        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem 2rem;
        }

        .admin-header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-header h1 {
            color: var(--primary-dark);
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .admin-header p {
            color: var(--gray);
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
            opacity: 0;
            animation: fadeInUp 0.6s ease-out forwards;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-accent);
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card h3 {
            color: var(--primary);
            font-size: 1.2rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .stat-card h3 i {
            animation: pulse 2.5s infinite ease-in-out;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: var(--primary-dark);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--gray);
            font-size: 0.9rem;
        }

        .management-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .management-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            border: 1px solid rgba(255, 255, 255, 0.2);
            opacity: 0;
            animation: fadeInUp 0.6s ease-out forwards;
        }

        .management-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
        }

        .management-card h3 {
            color: var(--primary);
            font-size: 1.3rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .management-card p {
            color: var(--gray);
            line-height: 1.6;
        }

        .management-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--gradient-accent);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            transition: var(--transition);
        }

        .management-card:hover .management-icon {
            transform: scale(1.1) rotate(-10deg);
            box-shadow: 0 8px 20px rgba(236, 64, 122, 0.4);
        }

        .recent-activity {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .recent-activity h2 {
            color: var(--primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid var(--light-gray);
            display: flex;
            align-items: center;
            gap: 1rem;
            opacity: 0;
            animation: fadeInUp 0.5s ease-out forwards;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--gradient-accent);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 0.2rem;
        }

        .activity-subtitle {
            font-size: 0.9rem;
            color: var(--gray);
            margin-bottom: 0.2rem;
        }

        .activity-time {
            font-size: 0.8rem;
            color: var(--gray);
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: var(--gray);
        }

        .error {
            background: #fee;
            color: #c33;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }

        .loading, .error {
            text-align: center;
            padding: 2rem;
            color: var(--gray);
            font-style: italic;
        }

        .error {
            color: var(--danger);
        }

        .loading::before {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--light-gray);
            border-top: 2px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .admin-container {
                padding: 1rem;
            }

            .admin-header h1 {
                font-size: 2rem;
            }

            .stats-grid,
            .management-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .nav-links {
                display: none; /* Simple hide for mobile, a burger menu would be a further improvement */
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Header -->
    <nav class="admin-nav">
        <div class="nav-content">
            <div class="nav-logo">
                <i class="fas fa-crown"></i>
                <span>Admin Dashboard</span>
            </div>
            <div class="nav-links">
                <a href="/admin" class="active"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/admin/manage-content"><i class="fas fa-edit"></i> Content</a>
                <a href="/admin/manage-consultations"><i class="fas fa-stethoscope"></i> Consultations</a>
                <a href="/admin/manage-patients"><i class="fas fa-user-injured"></i> Patients</a>
                <a href="/admin/manage-nutrition"><i class="fas fa-apple-alt"></i> Nutrition</a>
                <a href="/admin/manage-vaccination"><i class="fas fa-syringe"></i> Vaccinations</a>
                <a href="/admin/manage-users"><i class="fas fa-users"></i> Users</a>
                <a href="/auth/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </div>
    </nav>

    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <div>
                <h1>
                    <i class="fas fa-crown"></i>
                    Admin Dashboard
                </h1>
                <p>Comprehensive management system for Maternal and Child Health Monitoring</p>
            </div>
            <button onclick="refreshDashboard()" class="btn btn-primary" style="padding: 0.75rem 1.5rem; border: none; border-radius: 10px; background: var(--primary); color: white; cursor: pointer; font-weight: 600; transition: var(--transition);">
                <i class="fas fa-sync-alt"></i> Refresh Data
            </button>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid" id="stats-grid">
            <div class="loading">Loading statistics...</div>
        </div>

        <!-- Management Grid -->
        <div class="management-grid">
            <a href="/admin/manage-consultations" class="management-card">
                <div class="management-icon">
                    <i class="fas fa-stethoscope"></i>
                </div>
                <h3>Consultation Management</h3>
                <p>Manage Web3 consultation requests, appointments, and blockchain-verified medical consultations</p>
            </a>

            <a href="/admin/manage-patients" class="management-card">
                <div class="management-icon">
                    <i class="fas fa-user-injured"></i>
                </div>
                <h3>Patient Management</h3>
                <p>Manage patient records, medical history, and healthcare information</p>
            </a>

            <a href="/admin/manage-users" class="management-card">
                <div class="management-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3>User Management</h3>
                <p>Manage user accounts, roles, and permissions for the system</p>
            </a>

            <a href="/admin/manage-vaccination" class="management-card">
                <div class="management-icon">
                    <i class="fas fa-syringe"></i>
                </div>
                <h3>Vaccination Management</h3>
                <p>Manage vaccination schedules, records, and reminders</p>
            </a>

            <a href="/admin/manage-nutrition" class="management-card">
                <div class="management-icon">
                    <i class="fas fa-apple-alt"></i>
                </div>
                <h3>Nutrition Management</h3>
                <p>Manage nutrition plans, guidelines, and tracking</p>
            </a>

            <a href="/admin/manage-schemes" class="management-card">
                <div class="management-icon">
                    <i class="fas fa-hands-helping"></i>
                </div>
                <h3>Government Schemes</h3>
                <p>Manage government schemes and benefits information</p>
            </a>

            <a href="/admin/manage-faq" class="management-card">
                <div class="management-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <h3>FAQ Management</h3>
                <p>Manage frequently asked questions and answers</p>
            </a>

            <a href="/admin/manage-exercises" class="management-card">
                <div class="management-icon">
                    <i class="fas fa-running"></i>
                </div>
                <h3>Exercise Management</h3>
                <p>Manage exercise routines and physical activity guides</p>
            </a>

            <a href="/admin/manage-meditation" class="management-card">
                <div class="management-icon">
                    <i class="fas fa-leaf"></i>
                </div>
                <h3>Meditation Management</h3>
                <p>Manage meditation sessions and mindfulness content</p>
            </a>

            <a href="/admin/manage-content" class="management-card">
                <div class="management-icon">
                    <i class="fas fa-edit"></i>
                </div>
                <h3>Content Management</h3>
                <p>Manage website content, pages, and media</p>
            </a>
        </div>

        <!-- Recent Activity -->
        <div class="recent-activity">
            <h2>
                <i class="fas fa-clock"></i>
                Recent Activity
            </h2>
            <div id="recent-activity-list">
                <div class="loading">Loading recent activity...</div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardStats();
            
            // Staggered animations for cards that are already in the DOM
            const animatedCards = document.querySelectorAll('.management-card');
            animatedCards.forEach((card, index) => {
                card.style.animationDelay = `${index * 100}ms`;
            });
        });

        async function loadDashboardStats() {
            try {
                // Load real data from the API
                const response = await fetch('/admin/api/dashboard-stats');
                const data = await response.json();

                if (data.success) {
                    displayStats(data.stats);
                    displayRecentActivity(data.recent_activity);
                } else {
                    showError('Failed to load dashboard data: ' + (data.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error loading dashboard stats:', error);
                showError('Error loading dashboard data. Please check your connection.');
            }
        }

        function displayStats(stats) {
            const statsGrid = document.getElementById('stats-grid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <h3><i class="fas fa-users"></i> Total Users</h3>
                    <div class="stat-number">${stats.users.total || 0}</div>
                    <div class="stat-label">Registered users</div>
                </div>
                <div class="stat-card">
                    <h3><i class="fas fa-user-shield"></i> Admin Users</h3>
                    <div class="stat-number">${stats.users.admin || 0}</div>
                    <div class="stat-label">Administrator accounts</div>
                </div>
                <div class="stat-card">
                    <h3><i class="fas fa-user-md"></i> Doctors</h3>
                    <div class="stat-number">${stats.users.doctor || 0}</div>
                    <div class="stat-label">Medical professionals</div>
                </div>
                <div class="stat-card">
                    <h3><i class="fas fa-user"></i> Regular Users</h3>
                    <div class="stat-number">${stats.users.regular || 0}</div>
                    <div class="stat-label">Regular user accounts</div>
                </div>
                <div class="stat-card">
                    <h3><i class="fas fa-apple-alt"></i> Nutrition Content</h3>
                    <div class="stat-number">${stats.content.nutrition || 0}</div>
                    <div class="stat-label">Nutrition articles</div>
                </div>
                <div class="stat-card">
                    <h3><i class="fas fa-syringe"></i> Vaccination Schedules</h3>
                    <div class="stat-number">${stats.content.vaccinations || 0}</div>
                    <div class="stat-label">Vaccination schedules</div>
                </div>
                <div class="stat-card">
                    <h3><i class="fas fa-question-circle"></i> FAQs</h3>
                    <div class="stat-number">${stats.content.faqs || 0}</div>
                    <div class="stat-label">Frequently asked questions</div>
                </div>
                <div class="stat-card">
                    <h3><i class="fas fa-hands-helping"></i> Government Schemes</h3>
                    <div class="stat-number">${stats.content.schemes || 0}</div>
                    <div class="stat-label">Available schemes</div>
                </div>
            `;
            // Staggered animation for dynamically added stat cards
            const statCards = statsGrid.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.animationDelay = `${index * 100}ms`;
            });
        }

        function displayRecentActivity(activity) {
            const activityList = document.getElementById('recent-activity-list');
            let activityHTML = '';

            if (activity.recent_users && activity.recent_users.length > 0) {
                activity.recent_users.forEach(user => {
                    activityHTML += `
                        <div class="activity-item">
                            <div class="activity-icon"><i class="fas fa-user-plus"></i></div>
                            <div class="activity-content">
                                <div class="activity-title">User: ${user.full_name || user.name || 'Unknown User'}</div>
                                <div class="activity-subtitle">${user.email || ''}</div>
                                <div class="activity-time">${formatDate(user.created_at)}</div>
                            </div>
                        </div>`;
                });
            }

            // Show content summary
            if (activity.content_summary) {
                activityHTML += `
                    <div class="activity-item">
                        <div class="activity-icon"><i class="fas fa-info-circle"></i></div>
                        <div class="activity-content">
                            <div class="activity-title">Content Summary</div>
                            <div class="activity-subtitle">${activity.content_summary}</div>
                        </div>
                    </div>`;
            }

            if (activity.recent_babies && activity.recent_babies.length > 0) {
                activity.recent_babies.forEach(baby => {
                    activityHTML += `
                        <div class="activity-item">
                            <div class="activity-icon"><i class="fas fa-baby"></i></div>
                            <div class="activity-content">
                                <div class="activity-title">New baby registered: ${baby.name}</div>
                                <div class="activity-time">${formatDate(baby.created_at)}</div>
                            </div>
                        </div>`;
                });
            }

            if (activityHTML === '') {
                activityHTML = '<div class="activity-item"><div class="activity-content"><div class="activity-title">No recent activity</div></div></div>';
            }
            activityList.innerHTML = activityHTML;
            
            // Staggered animation for dynamically added activity items
            const activityItems = activityList.querySelectorAll('.activity-item');
            activityItems.forEach((item, index) => {
                item.style.animationDelay = `${index * 150}ms`;
            });
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        }

        function showError(message) {
            const statsGrid = document.getElementById('stats-grid');
            const activityList = document.getElementById('recent-activity-list');
            const errorHTML = `<div class="error">${message}</div>`;
            statsGrid.innerHTML = errorHTML;
            activityList.innerHTML = errorHTML;
        }

        function refreshDashboard() {
            // Show loading state
            const statsGrid = document.getElementById('stats-grid');
            const activityList = document.getElementById('recent-activity-list');
            statsGrid.innerHTML = '<div class="loading">Refreshing statistics...</div>';
            activityList.innerHTML = '<div class="loading">Refreshing activity...</div>';

            // Reload dashboard stats
            loadDashboardStats();
        }
    </script>
</body>
</html>

 