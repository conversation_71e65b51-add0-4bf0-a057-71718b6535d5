{% extends "base.html" %}

{% block title %}FAQ Management - Admin Dashboard{% endblock %}

{% block nav_title %}Admin Panel{% endblock %}

{% block nav_links %}
<li><a href="/admin"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
<li><a href="/admin/users"><i class="fas fa-users"></i> Users</a></li>
<li><a href="/admin/manage-vaccination"><i class="fas fa-syringe"></i> Vaccinations</a></li>
<li><a href="/admin/manage-exercises"><i class="fas fa-dumbbell"></i> Exercises</a></li>
<li><a href="/admin/manage-faq" class="active"><i class="fas fa-question-circle"></i> FAQ</a></li>
<li><a href="/admin/manage-schemes"><i class="fas fa-file-contract"></i> Schemes</a></li>
{% endblock %}

{% block extra_css %}
<style>
    body {
        background: var(--gradient-admin);
    }

    .admin-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow);
        margin-bottom: 2rem;
        text-align: center;
    }

    .admin-title {
        font-size: 3rem;
        font-weight: 800;
        background: var(--gradient-admin);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1rem;
    }

    .admin-subtitle {
        font-size: 1.2rem;
        color: var(--gray);
        max-width: 600px;
        margin: 0 auto;
    }

    .controls {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow);
        margin-bottom: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .search-box {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex: 1;
        max-width: 400px;
    }

    .search-input {
        flex: 1;
        padding: 0.75rem 1rem;
        border: 2px solid var(--light-gray);
        border-radius: 10px;
        font-size: 1rem;
        transition: var(--transition);
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 10px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-primary {
        background: var(--gradient-admin);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-hover);
    }

    .btn-secondary {
        background: var(--light-gray);
        color: var(--dark);
    }

    .btn-success {
        background: var(--success);
        color: white;
    }

    .btn-warning {
        background: var(--warning);
        color: white;
    }

    .btn-danger {
        background: var(--danger);
        color: white;
    }

    .btn-small {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .faq-list {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        overflow: hidden;
    }

    .faq-header {
        background: var(--gradient-admin);
        color: white;
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .faq-item {
        border-bottom: 1px solid var(--light-gray);
        transition: var(--transition);
    }

    .faq-item:last-child {
        border-bottom: none;
    }

    .faq-item:hover {
        background: rgba(102, 126, 234, 0.05);
    }

    .faq-question {
        padding: 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
    }

    .faq-question-text {
        font-weight: 600;
        color: var(--dark);
        flex: 1;
        margin-right: 1rem;
    }

    .faq-category {
        background: var(--primary);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-right: 1rem;
    }

    .faq-actions {
        display: flex;
        gap: 0.5rem;
    }

    .faq-answer {
        padding: 0 1.5rem 1.5rem;
        color: var(--gray);
        line-height: 1.6;
        display: none;
    }

    .faq-answer.show {
        display: block;
    }

    .faq-toggle {
        background: none;
        border: none;
        font-size: 1.2rem;
        color: var(--gray);
        cursor: pointer;
        transition: var(--transition);
    }

    .faq-toggle:hover {
        color: var(--primary);
    }

    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
    }

    .modal-content {
        background: white;
        margin: 5% auto;
        padding: 2rem;
        border-radius: var(--border-radius);
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: var(--shadow-hover);
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--light-gray);
    }

    .modal-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--dark);
    }

    .close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--gray);
        transition: var(--transition);
    }

    .close:hover {
        color: var(--danger);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--dark);
    }

    .form-input,
    .form-select,
    .form-textarea {
        width: 100%;
        padding: 1rem;
        border: 2px solid var(--light-gray);
        border-radius: 10px;
        font-size: 1rem;
        transition: var(--transition);
        background: white;
    }

    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-textarea {
        resize: vertical;
        min-height: 120px;
    }

    .loading {
        text-align: center;
        padding: 3rem;
        color: var(--gray);
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--gray);
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .controls {
            flex-direction: column;
            align-items: stretch;
        }

        .faq-question {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .faq-question-text {
            margin-right: 0;
        }

        .modal-content {
            margin: 10% auto;
            width: 95%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Page Header -->
    <div class="admin-header">
        <h1 class="admin-title">
            <i class="fas fa-question-circle"></i>
            FAQ Management
        </h1>
        <p class="admin-subtitle">
            Manage frequently asked questions about pregnancy, baby care, and health topics
        </p>
    </div>

    <!-- Alert Messages -->
    <div id="alert-container"></div>

    <!-- Controls -->
    <div class="controls">
        <div class="search-box">
            <input type="text" id="search-input" class="search-input" placeholder="Search FAQs by question or answer...">
            <button class="btn btn-secondary" onclick="searchFAQs()">
                <i class="fas fa-search"></i>
                Search
            </button>
        </div>
        <div style="display: flex; gap: 1rem;">
            <select id="category-filter" class="search-input" onchange="filterFAQs()">
                <option value="">All Categories</option>
                <option value="pregnancy">Pregnancy</option>
                <option value="baby-care">Baby Care</option>
                <option value="nutrition">Nutrition</option>
                <option value="health">Health</option>
                <option value="exercise">Exercise</option>
                <option value="general">General</option>
            </select>
            <button class="btn btn-primary" onclick="showAddFAQModal()">
                <i class="fas fa-plus"></i>
                Add FAQ
            </button>
        </div>
    </div>

    <!-- FAQ List -->
    <div class="faq-list">
        <div class="faq-header">
            <i class="fas fa-question-circle"></i>
            <h3>Frequently Asked Questions</h3>
        </div>
        <div id="faq-container">
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                Loading FAQs...
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit FAQ Modal -->
<div id="faq-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title" id="modal-title">Add New FAQ</h2>
            <button class="close" onclick="closeModal()">&times;</button>
        </div>

        <form id="faq-form">
            <input type="hidden" id="faq-id" name="faq_id">

            <div class="form-group">
                <label for="faq-question" class="form-label">Question</label>
                <input type="text" id="faq-question" name="question" class="form-input" required placeholder="Enter the frequently asked question...">
            </div>

            <div class="form-group">
                <label for="faq-answer" class="form-label">Answer</label>
                <textarea id="faq-answer" name="answer" class="form-textarea" required placeholder="Provide a comprehensive answer to the question..."></textarea>
            </div>

            <div class="form-group">
                <label for="faq-category" class="form-label">Category</label>
                <select id="faq-category" name="category" class="form-select" required>
                    <option value="">Select Category</option>
                    <option value="pregnancy">Pregnancy</option>
                    <option value="baby-care">Baby Care</option>
                    <option value="nutrition">Nutrition</option>
                    <option value="health">Health</option>
                    <option value="exercise">Exercise</option>
                    <option value="general">General</option>
                </select>
            </div>

            <div class="form-group">
                <label for="faq-tags" class="form-label">Tags (comma-separated)</label>
                <input type="text" id="faq-tags" name="tags" class="form-input" placeholder="e.g., pregnancy, first trimester, symptoms">
            </div>

            <div class="form-group">
                <label for="faq-priority" class="form-label">Priority</label>
                <select id="faq-priority" name="priority" class="form-select">
                    <option value="low">Low</option>
                    <option value="medium" selected>Medium</option>
                    <option value="high">High</option>
                </select>
            </div>

            <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Save FAQ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Global variables
    let allFAQs = [];
    let filteredFAQs = [];
    let editingFAQ = null;

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        loadFAQs();
        setupFormHandlers();
    });

    // Load FAQs
    async function loadFAQs() {
        try {
            const response = await fetch('/admin/api/content/faq');
            const data = await response.json();

            if (data.success) {
                allFAQs = data.data || [];
                filteredFAQs = [...allFAQs];
                displayFAQs();
            } else {
                showAlert('Failed to load FAQs: ' + (data.error || 'Unknown error'), 'error');
                displayEmptyState();
            }
        } catch (error) {
            console.error('Error loading FAQs:', error);
            showAlert('Error loading FAQs. Please try again.', 'error');
            displayEmptyState();
        }
    }

    // Display FAQs
    function displayFAQs() {
        const container = document.getElementById('faq-container');

        if (filteredFAQs.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-question-circle"></i>
                    <h3>No FAQs found</h3>
                    <p>No FAQs match your current filters or none have been added yet.</p>
                    <button class="btn btn-primary" onclick="showAddFAQModal()">
                        <i class="fas fa-plus"></i>
                        Add First FAQ
                    </button>
                </div>
            `;
            return;
        }

        container.innerHTML = '';

        filteredFAQs.forEach(faq => {
            const item = createFAQItem(faq);
            container.appendChild(item);
        });
    }

    // Create FAQ item
    function createFAQItem(faq) {
        const div = document.createElement('div');
        div.className = 'faq-item';

        const categoryColors = {
            'pregnancy': 'var(--primary)',
            'baby-care': 'var(--secondary)',
            'nutrition': 'var(--accent)',
            'health': 'var(--success)',
            'exercise': 'var(--warning)',
            'general': 'var(--info)'
        };

        div.innerHTML = `
            <div class="faq-question" onclick="toggleFAQ(${faq.id})">
                <div class="faq-question-text">${faq.question}</div>
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <span class="faq-category" style="background: ${categoryColors[faq.category] || 'var(--gray)'}">
                        ${faq.category}
                    </span>
                    <div class="faq-actions">
                        <button class="btn btn-warning btn-small" onclick="event.stopPropagation(); editFAQ(${faq.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger btn-small" onclick="event.stopPropagation(); deleteFAQ(${faq.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <button class="faq-toggle">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="faq-answer" id="answer-${faq.id}">
                ${faq.answer}
                ${faq.tags ? `<br><br><strong>Tags:</strong> ${faq.tags}` : ''}
            </div>
        `;

        return div;
    }

    // Toggle FAQ answer
    function toggleFAQ(faqId) {
        const answer = document.getElementById(`answer-${faqId}`);
        const toggle = answer.previousElementSibling.querySelector('.faq-toggle i');

        if (answer.classList.contains('show')) {
            answer.classList.remove('show');
            toggle.className = 'fas fa-chevron-down';
        } else {
            // Close all other answers
            document.querySelectorAll('.faq-answer.show').forEach(el => {
                el.classList.remove('show');
                el.previousElementSibling.querySelector('.faq-toggle i').className = 'fas fa-chevron-down';
            });

            answer.classList.add('show');
            toggle.className = 'fas fa-chevron-up';
        }
    }

    // Search FAQs
    function searchFAQs() {
        applyFilters();
    }

    // Filter FAQs
    function filterFAQs() {
        applyFilters();
    }

    // Apply filters
    function applyFilters() {
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        const categoryFilter = document.getElementById('category-filter').value;

        filteredFAQs = allFAQs.filter(faq => {
            const matchesSearch = searchTerm === '' ||
                faq.question.toLowerCase().includes(searchTerm) ||
                faq.answer.toLowerCase().includes(searchTerm) ||
                (faq.tags && faq.tags.toLowerCase().includes(searchTerm));

            const matchesCategory = categoryFilter === '' || faq.category === categoryFilter;

            return matchesSearch && matchesCategory;
        });

        displayFAQs();
    }

    // Show add FAQ modal
    function showAddFAQModal() {
        editingFAQ = null;
        document.getElementById('modal-title').textContent = 'Add New FAQ';
        document.getElementById('faq-form').reset();
        document.getElementById('faq-id').value = '';
        document.getElementById('faq-modal').style.display = 'block';
    }

    // Edit FAQ
    function editFAQ(faqId) {
        const faq = allFAQs.find(f => f.id === faqId);
        if (!faq) return;

        editingFAQ = faq;
        document.getElementById('modal-title').textContent = 'Edit FAQ';

        // Populate form
        document.getElementById('faq-id').value = faq.id;
        document.getElementById('faq-question').value = faq.question || '';
        document.getElementById('faq-answer').value = faq.answer || '';
        document.getElementById('faq-category').value = faq.category || '';
        document.getElementById('faq-tags').value = faq.tags || '';
        document.getElementById('faq-priority').value = faq.priority || 'medium';

        document.getElementById('faq-modal').style.display = 'block';
    }

    // Delete FAQ
    async function deleteFAQ(faqId) {
        if (!confirm('Are you sure you want to delete this FAQ? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch('/admin/api/content/faq', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id: faqId })
            });

            const result = await response.json();

            if (result.success) {
                showAlert('FAQ deleted successfully!', 'success');
                loadFAQs();
            } else {
                showAlert(result.error || 'Failed to delete FAQ.', 'error');
            }
        } catch (error) {
            console.error('Error deleting FAQ:', error);
            showAlert('Error deleting FAQ. Please try again.', 'error');
        }
    }

    // Setup form handlers
    function setupFormHandlers() {
        // FAQ form submission
        document.getElementById('faq-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            await saveFAQ();
        });

        // Search on Enter key
        document.getElementById('search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchFAQs();
            }
        });

        // Close modal when clicking outside
        document.getElementById('faq-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    }

    // Save FAQ
    async function saveFAQ() {
        const formData = new FormData(document.getElementById('faq-form'));
        const data = {
            question: formData.get('question'),
            answer: formData.get('answer'),
            category: formData.get('category'),
            tags: formData.get('tags'),
            priority: formData.get('priority')
        };

        try {
            const faqId = formData.get('faq_id');
            const method = faqId ? 'PUT' : 'POST';

            if (faqId) {
                data.id = faqId;
            }

            const response = await fetch('/admin/api/content/faq', {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                showAlert(`FAQ ${faqId ? 'updated' : 'created'} successfully!`, 'success');
                closeModal();
                loadFAQs();
            } else {
                showAlert(result.error || `Failed to ${faqId ? 'update' : 'create'} FAQ.`, 'error');
            }
        } catch (error) {
            console.error('Error saving FAQ:', error);
            showAlert('Error saving FAQ. Please try again.', 'error');
        }
    }

    // Close modal
    function closeModal() {
        document.getElementById('faq-modal').style.display = 'none';
        editingFAQ = null;
    }

    // Display empty state
    function displayEmptyState() {
        const container = document.getElementById('faq-container');
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-question-circle"></i>
                <h3>No FAQs available</h3>
                <p>Start by adding your first frequently asked question.</p>
                <button class="btn btn-primary" onclick="showAddFAQModal()">
                    <i class="fas fa-plus"></i>
                    Add FAQ
                </button>
            </div>
        `;
    }

    // Show alert
    function showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;

        const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle';
        alert.innerHTML = `
            <i class="fas fa-${icon}"></i>
            <div>${message}</div>
        `;

        alertContainer.appendChild(alert);

        // Auto remove after 5 seconds
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
</script>
{% endblock %}