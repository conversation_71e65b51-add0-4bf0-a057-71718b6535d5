<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chatbot - Baby Care Assistant</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding-top: 80px;
        }

        /* Header Styles */
        .header {
            background: white;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 0.5rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 70px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
            text-decoration: none;
        }

        .logo-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 1.5rem;
            align-items: center;
        }

        .nav-link {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            padding: 0.8rem 1.2rem;
            border-radius: 25px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
        }

        .nav-link:hover,
        .nav-link.active {
            color: white;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .logout-btn:hover {
            background: linear-gradient(135deg, #f44336, #ff5722) !important;
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3) !important;
        }

        /* Main Content */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .page-subtitle {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .chatbot-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
            height: 600px;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .chat-header h3 {
            margin: 0;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .chat-messages {
            flex: 1;
            padding: 1.5rem;
            overflow-y: auto;
            background: #f8f9ff;
        }

        .message {
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .message.bot .message-avatar {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #4caf50, #81c784);
        }

        .message-content {
            background: white;
            padding: 1rem;
            border-radius: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 70%;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .chat-input {
            padding: 1.5rem;
            background: white;
            border-top: 1px solid #e0e6ff;
        }

        .input-group {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .chat-input input {
            flex: 1;
            padding: 1rem;
            border: 2px solid #e0e6ff;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
        }

        .chat-input input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .send-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .quick-questions {
            margin-bottom: 1rem;
        }

        .quick-questions h4 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .question-chips {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .question-chip {
            background: #e0e6ff;
            color: #667eea;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }

        .question-chip:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .chatbot-container {
                height: 500px;
                margin: 0 1rem;
            }

            .question-chips {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="/" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <span>AI Chatbot</span>
            </a>
            <nav class="nav-menu">
                <a href="/" class="nav-link">
                    <i class="fas fa-home"></i> Home
                </a>
                <a href="/pregnancy" class="nav-link">
                    <i class="fas fa-heart"></i> Pregnancy Care
                </a>
                <a href="/babycare" class="nav-link">
                    <i class="fas fa-baby"></i> Baby Care
                </a>
                <a href="/babycare/chatbot" class="nav-link active">
                    <i class="fas fa-robot"></i> AI Chatbot
                </a>
                <a href="/auth/logout" class="nav-link logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </nav>
        </div>
    </header>

    <div class="container">
        <h1 class="page-title">
            <i class="fas fa-robot"></i> AI Baby Care Assistant
        </h1>
        <p class="page-subtitle">
            Get instant answers to your baby care questions from our intelligent assistant
        </p>

        <div class="chatbot-container">
            <div class="chat-header">
                <h3>
                    <i class="fas fa-robot"></i> Baby Care AI Assistant
                </h3>
                <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-size: 0.9rem;">
                    Ask me anything about baby care, feeding, sleep, development, and more!
                </p>
            </div>

            <div class="chat-messages" id="chat-messages">
                <div class="message bot">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>Hello! I'm your AI Baby Care Assistant. I'm here to help you with any questions about baby care, feeding, sleep schedules, development milestones, and more. How can I assist you today?</p>
                    </div>
                </div>
            </div>

            <div class="chat-input">
                <div class="quick-questions">
                    <h4>Quick Questions:</h4>
                    <div class="question-chips">
                        <button class="question-chip" onclick="askQuestion('How often should I feed my newborn?')">
                            Feeding Schedule
                        </button>
                        <button class="question-chip" onclick="askQuestion('When should my baby start sleeping through the night?')">
                            Sleep Patterns
                        </button>
                        <button class="question-chip" onclick="askQuestion('What are the key development milestones for a 6-month-old?')">
                            Development Milestones
                        </button>
                        <button class="question-chip" onclick="askQuestion('How do I know if my baby is getting enough milk?')">
                            Feeding Signs
                        </button>
                    </div>
                </div>

                <div class="input-group">
                    <input type="text" id="user-input" placeholder="Type your question here..." onkeypress="handleKeyPress(event)">
                    <button class="send-btn" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function askQuestion(question) {
            document.getElementById('user-input').value = question;
            sendMessage();
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function sendMessage() {
            const input = document.getElementById('user-input');
            const message = input.value.trim();

            if (!message) return;

            // Add user message
            addMessage(message, 'user');

            // Clear input
            input.value = '';

            // Simulate AI response
            setTimeout(() => {
                const response = generateAIResponse(message);
                addMessage(response, 'bot');
            }, 1000);
        }

        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.innerHTML = sender === 'bot' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';

            const content = document.createElement('div');
            content.className = 'message-content';
            content.innerHTML = `<p>${text}</p>`;

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(content);
            messagesContainer.appendChild(messageDiv);

            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function generateAIResponse(question) {
            const responses = {
                'feeding': [
                    "For newborns (0-3 months), feeding every 2-3 hours is typical. Breastfed babies may feed more frequently than formula-fed babies. Watch for hunger cues like rooting, sucking motions, or fussiness.",
                    "A good feeding schedule varies by baby, but generally newborns need 8-12 feedings per day. As they grow, the frequency decreases but the amount per feeding increases."
                ],
                'sleep': [
                    "Most babies start sleeping through the night (6-8 hour stretches) between 3-6 months old. Every baby is different, so don't worry if yours takes a bit longer!",
                    "Newborns sleep 14-17 hours per day in short bursts. By 6 months, most babies sleep 12-15 hours with longer nighttime stretches and 2-3 naps during the day."
                ],
                'development': [
                    "At 6 months, key milestones include: sitting with support, rolling over, reaching for objects, babbling, and showing interest in solid foods. Remember, every baby develops at their own pace!",
                    "Development milestones are guidelines, not strict deadlines. Focus on your baby's progress and consult your pediatrician if you have concerns."
                ],
                'milk': [
                    "Signs your baby is getting enough milk: steady weight gain, wet diapers (6+ per day after day 5), contentment after feeds, and regular bowel movements.",
                    "If you're concerned about milk intake, track feedings and diaper changes, and consult your pediatrician. They can assess if your baby is thriving."
                ]
            };

            // Simple keyword matching for demo
            const lowerQuestion = question.toLowerCase();

            if (lowerQuestion.includes('feed') || lowerQuestion.includes('milk') || lowerQuestion.includes('bottle') || lowerQuestion.includes('breast')) {
                if (lowerQuestion.includes('enough') || lowerQuestion.includes('getting')) {
                    return responses.milk[Math.floor(Math.random() * responses.milk.length)];
                }
                return responses.feeding[Math.floor(Math.random() * responses.feeding.length)];
            }

            if (lowerQuestion.includes('sleep') || lowerQuestion.includes('night') || lowerQuestion.includes('nap')) {
                return responses.sleep[Math.floor(Math.random() * responses.sleep.length)];
            }

            if (lowerQuestion.includes('development') || lowerQuestion.includes('milestone') || lowerQuestion.includes('month')) {
                return responses.development[Math.floor(Math.random() * responses.development.length)];
            }

            // Default response
            return "That's a great question! While I can provide general guidance, I always recommend consulting with your pediatrician for personalized advice about your baby's specific needs. Is there a particular aspect of baby care you'd like to know more about?";
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI Chatbot loaded successfully');
        });
    </script>
</body>
</html>